Module(body=[Assign(targets=[Name(id='l',
                                  ctx=Store())],
                    value=List(elts=[Num(n=1),
                                     Num(n=2),
                                     Num(n=3),
                                     Num(n=4)],
                               ctx=Load())),
             For(target=Name(id='i',
                             ctx=Store()),
                 iter=Call(func=Attribute(value=Name(id='l',
                                                     ctx=Load()),
                                          attr='__iter__',
                                          ctx=Load()),
                           args=[],
                           keywords=[],
                           starargs=None,
                           kwargs=None),
                 body=[Print(dest=None,
                             values=[Name(id='i',
                                          ctx=Load())],
                             nl=True)],
                 orelse=[]),
             ClassDef(name='MyIterable',
                      bases=[],
                      body=[FunctionDef(name='__init__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param()),
                                                             Name(id='lst',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Assign(targets=[Attribute(value=Name(id='self',
                                                                                   ctx=Load()),
                                                                        attr='x',
                                                                        ctx=Store())],
                                                     value=Num(n=3)),
                                              Assign(targets=[Attribute(value=Name(id='self',
                                                                                   ctx=Load()),
                                                                        attr='iter',
                                                                        ctx=Store())],
                                                     value=Name(id='lst',
                                                                ctx=Load()))],
                                        decorator_list=[]),
                            FunctionDef(name='__iter__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Return(value=Call(func=Attribute(value=Attribute(value=Name(id='self',
                                                                                                          ctx=Load()),
                                                                                               attr='iter',
                                                                                               ctx=Load()),
                                                                               attr='__iter__',
                                                                               ctx=Load()),
                                                                args=[],
                                                                keywords=[],
                                                                starargs=None,
                                                                kwargs=None))],
                                        decorator_list=[])],
                      decorator_list=[]),
             Assign(targets=[Name(id='mi',
                                  ctx=Store())],
                    value=Call(func=Name(id='MyIterable',
                                         ctx=Load()),
                               args=[List(elts=[Num(n=5),
                                                Num(n=6),
                                                Num(n=7)],
                                          ctx=Load())],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             For(target=Name(id='i',
                             ctx=Store()),
                 iter=Call(func=Attribute(value=Name(id='mi',
                                                     ctx=Load()),
                                          attr='__iter__',
                                          ctx=Load()),
                           args=[],
                           keywords=[],
                           starargs=None,
                           kwargs=None),
                 body=[Print(dest=None,
                             values=[Name(id='i',
                                          ctx=Load())],
                             nl=True)],
                 orelse=[]),
             For(target=Name(id='i',
                             ctx=Store()),
                 iter=Name(id='mi',
                           ctx=Load()),
                 body=[Print(dest=None,
                             values=[Name(id='i',
                                          ctx=Load())],
                             nl=True)],
                 orelse=[]),
             ClassDef(name='Counter',
                      bases=[],
                      body=[FunctionDef(name='__init__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param()),
                                                             Name(id='low',
                                                                  ctx=Param()),
                                                             Name(id='high',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Assign(targets=[Attribute(value=Name(id='self',
                                                                                   ctx=Load()),
                                                                        attr='current',
                                                                        ctx=Store())],
                                                     value=Name(id='low',
                                                                ctx=Load())),
                                              Assign(targets=[Attribute(value=Name(id='self',
                                                                                   ctx=Load()),
                                                                        attr='high',
                                                                        ctx=Store())],
                                                     value=Name(id='high',
                                                                ctx=Load()))],
                                        decorator_list=[]),
                            FunctionDef(name='__iter__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Return(value=Name(id='self',
                                                                ctx=Load()))],
                                        decorator_list=[]),
                            FunctionDef(name='next',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[If(test=Compare(left=Attribute(value=Name(id='self',
                                                                                        ctx=Load()),
                                                                             attr='current',
                                                                             ctx=Load()),
                                                              ops=[Gt()],
                                                              comparators=[Attribute(value=Name(id='self',
                                                                                                ctx=Load()),
                                                                                     attr='high',
                                                                                     ctx=Load())]),
                                                 body=[Raise(type=Name(id='StopIteration',
                                                                       ctx=Load()),
                                                             inst=None,
                                                             tback=None)],
                                                 orelse=[AugAssign(target=Attribute(value=Name(id='self',
                                                                                               ctx=Load()),
                                                                                    attr='current',
                                                                                    ctx=Store()),
                                                                   op=Add(),
                                                                   value=Num(n=1)),
                                                         Return(value=BinOp(left=Attribute(value=Name(id='self',
                                                                                                      ctx=Load()),
                                                                                           attr='current',
                                                                                           ctx=Load()),
                                                                            op=Sub(),
                                                                            right=Num(n=1)))])],
                                        decorator_list=[])],
                      decorator_list=[]),
             For(target=Name(id='c',
                             ctx=Store()),
                 iter=Call(func=Name(id='Counter',
                                     ctx=Load()),
                           args=[Num(n=9),
                                 Num(n=12)],
                           keywords=[],
                           starargs=None,
                           kwargs=None),
                 body=[Print(dest=None,
                             values=[Name(id='c',
                                          ctx=Load())],
                             nl=True)],
                 orelse=[]),
             ClassDef(name='SillyTupleIter',
                      bases=[],
                      body=[FunctionDef(name='__init__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param()),
                                                             Name(id='s',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Assign(targets=[Attribute(value=Name(id='self',
                                                                                   ctx=Load()),
                                                                        attr='w',
                                                                        ctx=Store())],
                                                     value=Call(func=Name(id='tuple',
                                                                          ctx=Load()),
                                                                args=[Name(id='s',
                                                                           ctx=Load())],
                                                                keywords=[],
                                                                starargs=None,
                                                                kwargs=None))],
                                        decorator_list=[]),
                            FunctionDef(name='__iter__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Return(value=Call(func=Attribute(value=Attribute(value=Name(id='self',
                                                                                                          ctx=Load()),
                                                                                               attr='w',
                                                                                               ctx=Load()),
                                                                               attr='__iter__',
                                                                               ctx=Load()),
                                                                args=[],
                                                                keywords=[],
                                                                starargs=None,
                                                                kwargs=None))],
                                        decorator_list=[])],
                      decorator_list=[]),
             Assign(targets=[Name(id='x',
                                  ctx=Store())],
                    value=Call(func=Name(id='SillyTupleIter',
                                         ctx=Load()),
                               args=[Str(s='foo')],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             For(target=Name(id='i',
                             ctx=Store()),
                 iter=Name(id='x',
                           ctx=Load()),
                 body=[Print(dest=None,
                             values=[Name(id='i',
                                          ctx=Load())],
                             nl=True)],
                 orelse=[])])
