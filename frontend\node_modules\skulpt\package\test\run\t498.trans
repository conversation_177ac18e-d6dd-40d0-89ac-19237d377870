Module(body=[Print(dest=None,
                   values=[Str(s='\nintegers')],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='pow',
                                          ctx=Load()),
                                args=[Num(n=0),
                                      Num(n=0)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='pow',
                                          ctx=Load()),
                                args=[Num(n=0),
                                      Num(n=3)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='pow',
                                          ctx=Load()),
                                args=[Num(n=12),
                                      Num(n=0)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='pow',
                                          ctx=Load()),
                                args=[Num(n=2),
                                      Num(n=3)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='pow',
                                          ctx=Load()),
                                args=[Num(n=-2),
                                      Num(n=3)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='pow',
                                          ctx=Load()),
                                args=[Num(n=2),
                                      Num(n=-3)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='pow',
                                          ctx=Load()),
                                args=[Num(n=-2),
                                      Num(n=-3)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='pow',
                                          ctx=Load()),
                                args=[Num(n=4),
                                      Num(n=5),
                                      Num(n=3)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='pow',
                                          ctx=Load()),
                                args=[Num(n=-4),
                                      Num(n=5),
                                      Num(n=3)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='pow',
                                          ctx=Load()),
                                args=[Num(n=-4),
                                      Num(n=5),
                                      Num(n=-3)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\nlong integers')],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='pow',
                                          ctx=Load()),
                                args=[Num(n=0),
                                      Num(n=0)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='pow',
                                          ctx=Load()),
                                args=[Num(n=0),
                                      Num(n=3)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='pow',
                                          ctx=Load()),
                                args=[Num(n=12),
                                      Num(n=0)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='pow',
                                          ctx=Load()),
                                args=[Num(n=2),
                                      Num(n=3)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='pow',
                                          ctx=Load()),
                                args=[Num(n=-2),
                                      Num(n=3)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='pow',
                                          ctx=Load()),
                                args=[Num(n=2),
                                      Num(n=-3)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='pow',
                                          ctx=Load()),
                                args=[Num(n=-2),
                                      Num(n=-3)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='pow',
                                          ctx=Load()),
                                args=[Num(n=4),
                                      Num(n=5),
                                      Num(n=3)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='pow',
                                          ctx=Load()),
                                args=[Num(n=-4),
                                      Num(n=5),
                                      Num(n=3)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='pow',
                                          ctx=Load()),
                                args=[Num(n=-4),
                                      Num(n=5),
                                      Num(n=-3)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\nfloating point')],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='pow',
                                          ctx=Load()),
                                args=[Num(n=0.0),
                                      Num(n=0.0)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='pow',
                                          ctx=Load()),
                                args=[Num(n=0.0),
                                      Num(n=3.1)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='pow',
                                          ctx=Load()),
                                args=[Num(n=12.0),
                                      Num(n=0.0)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='pow',
                                          ctx=Load()),
                                args=[Num(n=2.5),
                                      Num(n=3.7)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\nintegers and long integers')],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='pow',
                                          ctx=Load()),
                                args=[Num(n=2),
                                      Num(n=3)],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Call(func=Name(id='type',
                                          ctx=Load()),
                                args=[Call(func=Name(id='pow',
                                                     ctx=Load()),
                                           args=[Num(n=2),
                                                 Num(n=3)],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='pow',
                                          ctx=Load()),
                                args=[Num(n=-2),
                                      Num(n=3)],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Call(func=Name(id='type',
                                          ctx=Load()),
                                args=[Call(func=Name(id='pow',
                                                     ctx=Load()),
                                           args=[Num(n=-2),
                                                 Num(n=3)],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='pow',
                                          ctx=Load()),
                                args=[Num(n=2),
                                      Num(n=-3)],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Call(func=Name(id='type',
                                          ctx=Load()),
                                args=[Call(func=Name(id='pow',
                                                     ctx=Load()),
                                           args=[Num(n=2),
                                                 Num(n=-3)],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='pow',
                                          ctx=Load()),
                                args=[Num(n=-2),
                                      Num(n=-3)],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Call(func=Name(id='type',
                                          ctx=Load()),
                                args=[Call(func=Name(id='pow',
                                                     ctx=Load()),
                                           args=[Num(n=-2),
                                                 Num(n=-3)],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='pow',
                                          ctx=Load()),
                                args=[Num(n=2),
                                      Num(n=3),
                                      Num(n=5)],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Call(func=Name(id='type',
                                          ctx=Load()),
                                args=[Call(func=Name(id='pow',
                                                     ctx=Load()),
                                           args=[Num(n=2),
                                                 Num(n=3),
                                                 Num(n=5)],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='pow',
                                          ctx=Load()),
                                args=[Num(n=2),
                                      Num(n=3),
                                      Num(n=5)],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Call(func=Name(id='type',
                                          ctx=Load()),
                                args=[Call(func=Name(id='pow',
                                                     ctx=Load()),
                                           args=[Num(n=2),
                                                 Num(n=3),
                                                 Num(n=5)],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\nintegers and floating point')],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='pow',
                                          ctx=Load()),
                                args=[Num(n=2.5),
                                      Num(n=3)],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Call(func=Name(id='type',
                                          ctx=Load()),
                                args=[Call(func=Name(id='pow',
                                                     ctx=Load()),
                                           args=[Num(n=2.5),
                                                 Num(n=3)],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='pow',
                                          ctx=Load()),
                                args=[Num(n=2),
                                      Num(n=3.5)],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Call(func=Name(id='type',
                                          ctx=Load()),
                                args=[Call(func=Name(id='pow',
                                                     ctx=Load()),
                                           args=[Num(n=2),
                                                 Num(n=3.5)],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='pow',
                                          ctx=Load()),
                                args=[Num(n=2.5),
                                      Num(n=-3)],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Call(func=Name(id='type',
                                          ctx=Load()),
                                args=[Call(func=Name(id='pow',
                                                     ctx=Load()),
                                           args=[Num(n=2.5),
                                                 Num(n=-3)],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='pow',
                                          ctx=Load()),
                                args=[Num(n=2),
                                      Num(n=-3.5)],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Call(func=Name(id='type',
                                          ctx=Load()),
                                args=[Call(func=Name(id='pow',
                                                     ctx=Load()),
                                           args=[Num(n=2),
                                                 Num(n=-3.5)],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\nfloating point and long integers')],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='pow',
                                          ctx=Load()),
                                args=[Num(n=2.5),
                                      Num(n=3)],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Call(func=Name(id='type',
                                          ctx=Load()),
                                args=[Call(func=Name(id='pow',
                                                     ctx=Load()),
                                           args=[Num(n=2.5),
                                                 Num(n=3)],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='pow',
                                          ctx=Load()),
                                args=[Num(n=2),
                                      Num(n=3.5)],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Call(func=Name(id='type',
                                          ctx=Load()),
                                args=[Call(func=Name(id='pow',
                                                     ctx=Load()),
                                           args=[Num(n=2),
                                                 Num(n=3.5)],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='pow',
                                          ctx=Load()),
                                args=[Num(n=2.5),
                                      Num(n=-3)],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Call(func=Name(id='type',
                                          ctx=Load()),
                                args=[Call(func=Name(id='pow',
                                                     ctx=Load()),
                                           args=[Num(n=2.5),
                                                 Num(n=-3)],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='pow',
                                          ctx=Load()),
                                args=[Num(n=2),
                                      Num(n=-3.5)],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Call(func=Name(id='type',
                                          ctx=Load()),
                                args=[Call(func=Name(id='pow',
                                                     ctx=Load()),
                                           args=[Num(n=2),
                                                 Num(n=-3.5)],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\nERROR CHECKING:')],
                   nl=True),
             TryExcept(body=[Print(dest=None,
                                   values=[Call(func=Name(id='pow',
                                                          ctx=Load()),
                                                args=[List(elts=[Num(n=1),
                                                                 Num(n=2)],
                                                           ctx=Load()),
                                                      Str(s='34')],
                                                keywords=[],
                                                starargs=None,
                                                kwargs=None)],
                                   nl=True),
                             Print(dest=None,
                                   values=[Str(s="you shouldn't see this")],
                                   nl=True)],
                       handlers=[ExceptHandler(type=Name(id='TypeError',
                                                         ctx=Load()),
                                               name=Name(id='e',
                                                         ctx=Store()),
                                               body=[Print(dest=None,
                                                           values=[Name(id='e',
                                                                        ctx=Load())],
                                                           nl=True)])],
                       orelse=[]),
             TryExcept(body=[Print(dest=None,
                                   values=[Call(func=Name(id='pow',
                                                          ctx=Load()),
                                                args=[List(elts=[Num(n=1),
                                                                 Num(n=2)],
                                                           ctx=Load()),
                                                      Str(s='34'),
                                                      Num(n=5)],
                                                keywords=[],
                                                starargs=None,
                                                kwargs=None)],
                                   nl=True),
                             Print(dest=None,
                                   values=[Str(s="you shouldn't see this")],
                                   nl=True)],
                       handlers=[ExceptHandler(type=Name(id='TypeError',
                                                         ctx=Load()),
                                               name=Name(id='e',
                                                         ctx=Store()),
                                               body=[Print(dest=None,
                                                           values=[Name(id='e',
                                                                        ctx=Load())],
                                                           nl=True)])],
                       orelse=[]),
             TryExcept(body=[Print(dest=None,
                                   values=[Call(func=Name(id='pow',
                                                          ctx=Load()),
                                                args=[Num(n=-2.5),
                                                      Num(n=3.7)],
                                                keywords=[],
                                                starargs=None,
                                                kwargs=None)],
                                   nl=True),
                             Print(dest=None,
                                   values=[Str(s="you shouldn't see this")],
                                   nl=True)],
                       handlers=[ExceptHandler(type=Name(id='ValueError',
                                                         ctx=Load()),
                                               name=Name(id='e',
                                                         ctx=Store()),
                                               body=[Print(dest=None,
                                                           values=[Name(id='e',
                                                                        ctx=Load())],
                                                           nl=True)])],
                       orelse=[]),
             TryExcept(body=[Print(dest=None,
                                   values=[Call(func=Name(id='pow',
                                                          ctx=Load()),
                                                args=[Num(n=4.0),
                                                      Num(n=5.0),
                                                      Num(n=3)],
                                                keywords=[],
                                                starargs=None,
                                                kwargs=None)],
                                   nl=True),
                             Print(dest=None,
                                   values=[Str(s="you shouldn't see this")],
                                   nl=True)],
                       handlers=[ExceptHandler(type=Name(id='TypeError',
                                                         ctx=Load()),
                                               name=Name(id='e',
                                                         ctx=Store()),
                                               body=[Print(dest=None,
                                                           values=[Name(id='e',
                                                                        ctx=Load())],
                                                           nl=True)])],
                       orelse=[]),
             TryExcept(body=[Print(dest=None,
                                   values=[Call(func=Name(id='pow',
                                                          ctx=Load()),
                                                args=[Num(n=4),
                                                      Num(n=-3),
                                                      Num(n=2)],
                                                keywords=[],
                                                starargs=None,
                                                kwargs=None)],
                                   nl=True),
                             Print(dest=None,
                                   values=[Str(s="you shouldn't see this")],
                                   nl=True)],
                       handlers=[ExceptHandler(type=Name(id='ValueError',
                                                         ctx=Load()),
                                               name=Name(id='e',
                                                         ctx=Store()),
                                               body=[Print(dest=None,
                                                           values=[Name(id='e',
                                                                        ctx=Load())],
                                                           nl=True)])],
                       orelse=[])])
