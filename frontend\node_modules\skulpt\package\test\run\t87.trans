Module(body=[Assign(targets=[Name(id='x',
                                  ctx=Store())],
                    value=Tuple(elts=[Num(n=1),
                                      Num(n=3)],
                                ctx=Load())),
             Print(dest=None,
                   values=[Subscript(value=Dict(keys=[Name(id='x',
                                                           ctx=Load())],
                                                values=[Str(s='OK')]),
                                     slice=Index(value=Name(id='x',
                                                            ctx=Load())),
                                     ctx=Load())],
                   nl=True)])
