Module(body=[ImportFrom(module='time',
                        names=[alias(name='sleep',
                                     asname=None)],
                        level=0),
             ClassDef(name='SleepyClass',
                      bases=[],
                      body=[FunctionDef(name='__init__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Print(dest=None,
                                                    values=[Str(s='Sleeping in __init__')],
                                                    nl=True),
                                              Print(dest=None,
                                                    values=[BinOp(left=Str(s='Sleep returned '),
                                                                  op=Add(),
                                                                  right=Call(func=Name(id='str',
                                                                                       ctx=Load()),
                                                                             args=[Call(func=Name(id='sleep',
                                                                                                  ctx=Load()),
                                                                                        args=[Num(n=0.01)],
                                                                                        keywords=[],
                                                                                        starargs=None,
                                                                                        kwargs=None)],
                                                                             keywords=[],
                                                                             starargs=None,
                                                                             kwargs=None))],
                                                    nl=True)],
                                        decorator_list=[]),
                            FunctionDef(name='doSleep',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param()),
                                                             Name(id='param',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Assign(targets=[Name(id='x',
                                                                   ctx=Store())],
                                                     value=Num(n=42)),
                                              Print(dest=None,
                                                    values=[Str(s='Sleeping for .01 seconds')],
                                                    nl=True),
                                              Print(dest=None,
                                                    values=[BinOp(left=Str(s='Sleep returned '),
                                                                  op=Add(),
                                                                  right=Call(func=Name(id='str',
                                                                                       ctx=Load()),
                                                                             args=[Call(func=Name(id='sleep',
                                                                                                  ctx=Load()),
                                                                                        args=[Num(n=0.01)],
                                                                                        keywords=[],
                                                                                        starargs=None,
                                                                                        kwargs=None)],
                                                                             keywords=[],
                                                                             starargs=None,
                                                                             kwargs=None))],
                                                    nl=True),
                                              Print(dest=None,
                                                    values=[BinOp(left=Str(s='Woke up; x = '),
                                                                  op=Add(),
                                                                  right=Call(func=Name(id='str',
                                                                                       ctx=Load()),
                                                                             args=[Name(id='x',
                                                                                        ctx=Load())],
                                                                             keywords=[],
                                                                             starargs=None,
                                                                             kwargs=None))],
                                                    nl=True),
                                              Print(dest=None,
                                                    values=[BinOp(left=Str(s='param = '),
                                                                  op=Add(),
                                                                  right=Call(func=Name(id='str',
                                                                                       ctx=Load()),
                                                                             args=[Name(id='param',
                                                                                        ctx=Load())],
                                                                             keywords=[],
                                                                             starargs=None,
                                                                             kwargs=None))],
                                                    nl=True)],
                                        decorator_list=[])],
                      decorator_list=[]),
             Assign(targets=[Name(id='sleeper',
                                  ctx=Store())],
                    value=Call(func=Name(id='SleepyClass',
                                         ctx=Load()),
                               args=[],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Expr(value=Call(func=Attribute(value=Name(id='sleeper',
                                                       ctx=Load()),
                                            attr='doSleep',
                                            ctx=Load()),
                             args=[Num(n=7)],
                             keywords=[],
                             starargs=None,
                             kwargs=None))])
