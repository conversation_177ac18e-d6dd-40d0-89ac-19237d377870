Module(body=[Import(names=[alias(name='re',
                                 asname=None)]),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='[a-z]*ei[a-z]*'),
                                      Str(s='Is <PERSON> your friend, <PERSON>?'),
                                      Attribute(value=Name(id='re',
                                                           ctx=Load()),
                                                attr='IGNORECASE',
                                                ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='[a-z]*(ei|ie)[a-z]*'),
                                      Str(s='Is <PERSON><PERSON> your friend, <PERSON>?'),
                                      Attribute(value=Name(id='re',
                                                           ctx=Load()),
                                                attr='IGNORECASE',
                                                ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='[a-z]*(ei|ie)([a-z]*)'),
                                      Str(s='Is Dr. Greiner your friend, Julie?'),
                                      Attribute(value=Name(id='re',
                                                           ctx=Load()),
                                                attr='IGNORECASE',
                                                ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='[a-z]*(?:ei|ie)[a-z]*'),
                                      Str(s='Is Dr. Greiner your friend, Julie?'),
                                      Attribute(value=Name(id='re',
                                                           ctx=Load()),
                                                attr='IGNORECASE',
                                                ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True)])
