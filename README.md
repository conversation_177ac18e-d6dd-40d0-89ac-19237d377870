# Python Learning Platform for Kids 🐍

A comprehensive, interactive Python learning platform designed specifically for children (ages 8-14) and absolute beginners. Built with modern web technologies and educational best practices.

## ✨ Features

### 🎮 Interactive Learning
- **In-browser Python execution** using Skulpt.js - no downloads required
- **Monaco Editor** with syntax highlighting and auto-completion
- **Real-time code execution** with instant feedback
- **Beginner-friendly error messages** that help kids understand mistakes

### 📚 Structured Curriculum
- **20+ progressive lessons** covering Python fundamentals
- **Step-by-step tutorials** with clear explanations
- **Interactive exercises** with hints and solutions
- **Prerequisites system** ensuring proper learning progression

### 🏆 Gamification & Motivation
- **Points system** (10 points per completed lesson)
- **Achievement badges** (Bronze, Silver, Gold levels)
- **Progress tracking** with visual indicators
- **Streak counters** to encourage daily practice
- **Leaderboards** for friendly competition

### 👤 User Experience
- **Guest mode** - try 3 lessons without registration
- **User authentication** with secure JWT tokens
- **Progress persistence** across devices
- **Mobile-responsive design** optimized for tablets
- **Kid-friendly UI** with colorful, cartoon-style interface

### 📊 Analytics & Tracking
- **Detailed progress reports** for parents/teachers
- **Time tracking** for each lesson
- **Score tracking** with improvement metrics
- **Learning analytics** dashboard

## 🛠 Tech Stack

### Frontend
- **React 18** with TypeScript for type safety
- **Vite** for fast development and hot reload
- **Tailwind CSS** with custom kid-friendly design system
- **Zustand** for state management
- **React Router** for navigation
- **Monaco Editor** for code editing
- **Skulpt.js** for client-side Python execution

### Backend
- **Flask 3.0** with Python 3.9+
- **SQLAlchemy** for database ORM
- **Flask-JWT-Extended** for authentication
- **Flask-CORS** for cross-origin requests
- **PostgreSQL** (production) / SQLite (development)
- **Flask-Migrate** for database migrations

### Development & Deployment
- **Docker Compose** for local development
- **Vercel/Netlify** for frontend deployment
- **Render.com** for backend deployment
- **GitHub Actions** for CI/CD (optional)

## 🚀 Quick Start

### Option 1: Docker Compose (Recommended)

```bash
# Clone the repository
git clone <repository-url>
cd pywebsite

# Start all services
docker-compose up

# Access the application
# Frontend: http://localhost:3000
# Backend: http://localhost:5000
```

### Option 2: Manual Setup

```bash
# Backend setup
cd backend
python -m venv venv
source venv/bin/activate  # or venv\Scripts\activate on Windows
pip install -r requirements.txt
python app.py

# Frontend setup (in new terminal)
cd frontend
npm install
npm run dev
```

## 📁 Project Structure

```
pywebsite/
├── 📁 frontend/              # React TypeScript application
│   ├── 📁 src/
│   │   ├── 📁 components/    # Reusable UI components
│   │   ├── 📁 pages/         # Page components
│   │   ├── 📁 store/         # Zustand state management
│   │   └── 📄 App.tsx        # Main application component
│   ├── 📄 package.json       # Node.js dependencies
│   └── 📄 tailwind.config.js # Tailwind CSS configuration
├── 📁 backend/               # Flask Python API
│   ├── 📁 routes/            # API route handlers
│   ├── 📄 app.py             # Main Flask application
│   ├── 📄 models.py          # Database models
│   ├── 📄 seed_data.py       # Sample lesson data
│   └── 📄 requirements.txt   # Python dependencies
├── 📁 docs/                  # Documentation
│   ├── 📄 development.md     # Development setup guide
│   ├── 📄 deployment.md      # Deployment instructions
│   └── 📄 api.md             # API documentation
├── 📄 docker-compose.yml     # Docker development environment
└── 📄 README.md              # This file
```

## 🎯 Learning Path

### Beginner Lessons (Ages 8-12)
1. **Welcome to Python** - Introduction and first "Hello, World!"
2. **Variables** - Storing and using data
3. **Numbers and Math** - Basic arithmetic operations
4. **Text and Strings** - Working with text
5. **Input and Output** - Interactive programs
6. **Making Decisions** - If statements and conditions
7. **Loops** - Repeating actions
8. **Lists** - Storing multiple items
9. **Functions** - Creating reusable code
10. **Simple Projects** - Fun mini-games and utilities

### Intermediate Lessons (Ages 10-14)
11. **Dictionaries** - Key-value data storage
12. **File Handling** - Reading and writing files
13. **Error Handling** - Dealing with mistakes gracefully
14. **Object-Oriented Programming** - Classes and objects
15. **Libraries and Modules** - Using external code
16. **Web Scraping Basics** - Getting data from websites
17. **Data Visualization** - Creating charts and graphs
18. **Game Development** - Building simple games
19. **API Integration** - Connecting to web services
20. **Final Project** - Comprehensive application

## 🏅 Achievement System

### Badges Available
- 👶 **First Steps** - Complete your first lesson
- 🚀 **Getting Started** - Complete 3 lessons
- 🔍 **Python Explorer** - Complete 5 lessons
- ⚔️ **Code Warrior** - Complete 10 lessons
- 💯 **Perfect Score** - Get 100% on any lesson
- ⚡ **Speed Demon** - Complete a lesson in under 5 minutes
- 🎯 **Persistent** - Maintain a 5-day learning streak
- 🏆 **Python Master** - Complete all lessons

## 📖 Documentation

- **[Development Setup](docs/development.md)** - Local development environment
- **[Deployment Guide](docs/deployment.md)** - Production deployment
- **[API Documentation](docs/api.md)** - Complete API reference

## 🔒 Security & Safety

- **Child-safe environment** - No external links or user-generated content
- **Secure authentication** - JWT tokens with proper validation
- **Data privacy** - Minimal data collection, COPPA compliant
- **Content filtering** - All lessons reviewed for age-appropriateness

## 🌐 Browser Support

- **Chrome** 90+ ✅
- **Firefox** 88+ ✅
- **Safari** 14+ ✅
- **Edge** 90+ ✅
- **Mobile browsers** ✅

## 🤝 Contributing

We welcome contributions! Please see our contributing guidelines:

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Skulpt.js** team for Python-in-browser execution
- **Monaco Editor** team for the excellent code editor
- **React** and **Flask** communities for amazing frameworks
- **Educational consultants** who helped design the curriculum

## 📞 Support

- 📧 Email: <EMAIL>
- 💬 Discord: [Join our community](https://discord.gg/pythonkids)
- 🐛 Issues: [GitHub Issues](https://github.com/your-repo/issues)
- 📚 Docs: [Documentation Site](https://docs.pythonkids.com)

---

**Made with ❤️ for young coders everywhere!**
