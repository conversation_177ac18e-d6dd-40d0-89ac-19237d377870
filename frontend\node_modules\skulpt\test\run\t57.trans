Module(body=[FunctionDef(name='test',
                         args=arguments(args=[],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[]),
                         body=[Global(names=['x']),
                               Assign(targets=[Name(id='x',
                                                    ctx=Store())],
                                      value=Str(s='OK'))],
                         decorator_list=[]),
             Expr(value=Call(func=Name(id='test',
                                       ctx=Load()),
                             args=[],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Name(id='x',
                                ctx=Load())],
                   nl=True)])
