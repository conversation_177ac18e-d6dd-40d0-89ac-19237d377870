Sym_type: module
Sym_name: top
Sym_lineno: 0
Sym_nested: False
Sym_haschildren: True
-- Identifiers --
name: Point
  is_referenced: True
  is_imported: False
  is_parameter: False
  is_global: False
  is_declared_global: False
  is_local: True
  is_free: False
  is_assigned: True
  is_namespace: True
  namespaces: [
    Sym_type: class
    Sym_name: Point
    Sym_lineno: 1
    Sym_nested: False
    Sym_haschildren: True
    Class_methods: ['__init__', '__str__']
    -- Identifiers --
    name: __init__
      is_referenced: False
      is_imported: False
      is_parameter: False
      is_global: False
      is_declared_global: False
      is_local: True
      is_free: False
      is_assigned: True
      is_namespace: True
      namespaces: [
        Sym_type: function
        Sym_name: __init__
        Sym_lineno: 2
        Sym_nested: False
        Sym_haschildren: False
        Func_params: ['initX', 'initY', 'self']
        Func_locals: ['initX', 'initY', 'self']
        Func_globals: []
        Func_frees: []
        -- Identifiers --
        name: initX
          is_referenced: True
          is_imported: False
          is_parameter: True
          is_global: False
          is_declared_global: False
          is_local: True
          is_free: False
          is_assigned: False
          is_namespace: False
          namespaces: [
          ]
        name: initY
          is_referenced: True
          is_imported: False
          is_parameter: True
          is_global: False
          is_declared_global: False
          is_local: True
          is_free: False
          is_assigned: False
          is_namespace: False
          namespaces: [
          ]
        name: self
          is_referenced: True
          is_imported: False
          is_parameter: True
          is_global: False
          is_declared_global: False
          is_local: True
          is_free: False
          is_assigned: False
          is_namespace: False
          namespaces: [
          ]
      ]
    name: __str__
      is_referenced: False
      is_imported: False
      is_parameter: False
      is_global: False
      is_declared_global: False
      is_local: True
      is_free: False
      is_assigned: True
      is_namespace: True
      namespaces: [
        Sym_type: function
        Sym_name: __str__
        Sym_lineno: 6
        Sym_nested: False
        Sym_haschildren: False
        Func_params: ['self']
        Func_locals: ['self']
        Func_globals: ['str']
        Func_frees: []
        -- Identifiers --
        name: self
          is_referenced: True
          is_imported: False
          is_parameter: True
          is_global: False
          is_declared_global: False
          is_local: True
          is_free: False
          is_assigned: False
          is_namespace: False
          namespaces: [
          ]
        name: str
          is_referenced: True
          is_imported: False
          is_parameter: False
          is_global: True
          is_declared_global: False
          is_local: False
          is_free: False
          is_assigned: False
          is_namespace: False
          namespaces: [
          ]
      ]
  ]
name: p
  is_referenced: True
  is_imported: False
  is_parameter: False
  is_global: False
  is_declared_global: False
  is_local: True
  is_free: False
  is_assigned: True
  is_namespace: False
  namespaces: [
  ]
name: str
  is_referenced: True
  is_imported: False
  is_parameter: False
  is_global: True
  is_declared_global: False
  is_local: False
  is_free: False
  is_assigned: False
  is_namespace: False
  namespaces: [
  ]
