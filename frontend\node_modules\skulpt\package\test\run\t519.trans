Module(body=[Assign(targets=[Name(id='l',
                                  ctx=Store())],
                    value=List(elts=[Str(s='h'),
                                     Str(s='e'),
                                     Str(s='l'),
                                     Str(s='l'),
                                     Str(s='o')],
                               ctx=Load())),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='l',
                                                          ctx=Load()),
                                               attr='index',
                                               ctx=Load()),
                                args=[Str(s='l')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='l',
                                                          ctx=Load()),
                                               attr='index',
                                               ctx=Load()),
                                args=[Str(s='l'),
                                      Num(n=2)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='l',
                                                          ctx=Load()),
                                               attr='index',
                                               ctx=Load()),
                                args=[Str(s='l'),
                                      Num(n=3)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='l',
                                                          ctx=Load()),
                                               attr='index',
                                               ctx=Load()),
                                args=[Str(s='l'),
                                      Num(n=2),
                                      Num(n=3)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='l',
                                                          ctx=Load()),
                                               attr='index',
                                               ctx=Load()),
                                args=[Str(s='l'),
                                      Num(n=3),
                                      Num(n=4)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='l',
                                                          ctx=Load()),
                                               attr='index',
                                               ctx=Load()),
                                args=[Str(s='l'),
                                      Num(n=2),
                                      Num(n=-1)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='l',
                                                          ctx=Load()),
                                               attr='index',
                                               ctx=Load()),
                                args=[Str(s='l'),
                                      Num(n=2),
                                      Num(n=-2)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='l',
                                                          ctx=Load()),
                                               attr='index',
                                               ctx=Load()),
                                args=[Str(s='l'),
                                      Num(n=3),
                                      Num(n=-1)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             TryExcept(body=[Print(dest=None,
                                   values=[Call(func=Attribute(value=Name(id='l',
                                                                          ctx=Load()),
                                                               attr='index',
                                                               ctx=Load()),
                                                args=[Str(s='l'),
                                                      Num(n=4)],
                                                keywords=[],
                                                starargs=None,
                                                kwargs=None)],
                                   nl=True)],
                       handlers=[ExceptHandler(type=Name(id='ValueError',
                                                         ctx=Load()),
                                               name=Name(id='e',
                                                         ctx=Store()),
                                               body=[Print(dest=None,
                                                           values=[Name(id='e',
                                                                        ctx=Load())],
                                                           nl=True)])],
                       orelse=[]),
             TryExcept(body=[Print(dest=None,
                                   values=[Call(func=Attribute(value=Name(id='l',
                                                                          ctx=Load()),
                                                               attr='index',
                                                               ctx=Load()),
                                                args=[Str(s='l'),
                                                      Num(n=-1)],
                                                keywords=[],
                                                starargs=None,
                                                kwargs=None)],
                                   nl=True)],
                       handlers=[ExceptHandler(type=Name(id='ValueError',
                                                         ctx=Load()),
                                               name=Name(id='e',
                                                         ctx=Store()),
                                               body=[Print(dest=None,
                                                           values=[Name(id='e',
                                                                        ctx=Load())],
                                                           nl=True)])],
                       orelse=[]),
             TryExcept(body=[Print(dest=None,
                                   values=[Call(func=Attribute(value=Name(id='l',
                                                                          ctx=Load()),
                                                               attr='index',
                                                               ctx=Load()),
                                                args=[Str(s='l'),
                                                      Num(n=2),
                                                      Num(n=2)],
                                                keywords=[],
                                                starargs=None,
                                                kwargs=None)],
                                   nl=True)],
                       handlers=[ExceptHandler(type=Name(id='ValueError',
                                                         ctx=Load()),
                                               name=Name(id='e',
                                                         ctx=Store()),
                                               body=[Print(dest=None,
                                                           values=[Name(id='e',
                                                                        ctx=Load())],
                                                           nl=True)])],
                       orelse=[]),
             TryExcept(body=[Print(dest=None,
                                   values=[Call(func=Attribute(value=Name(id='l',
                                                                          ctx=Load()),
                                                               attr='index',
                                                               ctx=Load()),
                                                args=[Str(s='l'),
                                                      Num(n=3),
                                                      Num(n=2)],
                                                keywords=[],
                                                starargs=None,
                                                kwargs=None)],
                                   nl=True)],
                       handlers=[ExceptHandler(type=Name(id='ValueError',
                                                         ctx=Load()),
                                               name=Name(id='e',
                                                         ctx=Store()),
                                               body=[Print(dest=None,
                                                           values=[Name(id='e',
                                                                        ctx=Load())],
                                                           nl=True)])],
                       orelse=[]),
             TryExcept(body=[Print(dest=None,
                                   values=[Call(func=Attribute(value=Name(id='l',
                                                                          ctx=Load()),
                                                               attr='index',
                                                               ctx=Load()),
                                                args=[Str(s='l'),
                                                      Num(n=3),
                                                      Num(n=-2)],
                                                keywords=[],
                                                starargs=None,
                                                kwargs=None)],
                                   nl=True)],
                       handlers=[ExceptHandler(type=Name(id='ValueError',
                                                         ctx=Load()),
                                               name=Name(id='e',
                                                         ctx=Store()),
                                               body=[Print(dest=None,
                                                           values=[Name(id='e',
                                                                        ctx=Load())],
                                                           nl=True)])],
                       orelse=[]),
             TryExcept(body=[Print(dest=None,
                                   values=[Call(func=Attribute(value=Name(id='l',
                                                                          ctx=Load()),
                                                               attr='index',
                                                               ctx=Load()),
                                                args=[Str(s='l'),
                                                      Num(n=3),
                                                      Num(n=0)],
                                                keywords=[],
                                                starargs=None,
                                                kwargs=None)],
                                   nl=True)],
                       handlers=[ExceptHandler(type=Name(id='ValueError',
                                                         ctx=Load()),
                                               name=Name(id='e',
                                                         ctx=Store()),
                                               body=[Print(dest=None,
                                                           values=[Name(id='e',
                                                                        ctx=Load())],
                                                           nl=True)])],
                       orelse=[]),
             TryExcept(body=[Print(dest=None,
                                   values=[Call(func=Attribute(value=Name(id='l',
                                                                          ctx=Load()),
                                                               attr='index',
                                                               ctx=Load()),
                                                args=[Str(s='l'),
                                                      Num(n=4.3)],
                                                keywords=[],
                                                starargs=None,
                                                kwargs=None)],
                                   nl=True)],
                       handlers=[ExceptHandler(type=Name(id='TypeError',
                                                         ctx=Load()),
                                               name=Name(id='e',
                                                         ctx=Store()),
                                               body=[Print(dest=None,
                                                           values=[Name(id='e',
                                                                        ctx=Load())],
                                                           nl=True)])],
                       orelse=[]),
             TryExcept(body=[Print(dest=None,
                                   values=[Call(func=Attribute(value=Name(id='l',
                                                                          ctx=Load()),
                                                               attr='index',
                                                               ctx=Load()),
                                                args=[Str(s='l'),
                                                      Num(n=3),
                                                      Num(n=0.6)],
                                                keywords=[],
                                                starargs=None,
                                                kwargs=None)],
                                   nl=True)],
                       handlers=[ExceptHandler(type=Name(id='TypeError',
                                                         ctx=Load()),
                                               name=Name(id='e',
                                                         ctx=Store()),
                                               body=[Print(dest=None,
                                                           values=[Name(id='e',
                                                                        ctx=Load())],
                                                           nl=True)])],
                       orelse=[])])
