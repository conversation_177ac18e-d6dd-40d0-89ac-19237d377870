import os
from flask import Flask, jsonify
from flask_sqlalchemy import SQLAlchemy
from flask_jwt_extended import J<PERSON><PERSON>anager
from flask_cors import CORS
from flask_migrate import Migrate
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Initialize Flask app
app = Flask(__name__)

# Configuration
app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'dev-secret-key-change-in-production')
app.config['JWT_SECRET_KEY'] = os.getenv('JWT_SECRET_KEY', 'dev-jwt-secret-key')
app.config['SQLALCHEMY_DATABASE_URI'] = os.getenv('DATABASE_URL', 'sqlite:///pylearning.db')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['JWT_ACCESS_TOKEN_EXPIRES'] = False  # For development

# Initialize extensions
db = SQLAlchemy(app)
jwt = JWTManager(app)
migrate = Migrate(app, db)
CORS(app, origins=['http://localhost:3000', 'http://localhost:5173'])

# Import models
from models import User, Lesson, Progress, Badge

# Import routes
from routes.auth import auth_bp
from routes.lessons import lessons_bp
from routes.users import users_bp

# Register blueprints
app.register_blueprint(auth_bp, url_prefix='/api/auth')
app.register_blueprint(lessons_bp, url_prefix='/api/lessons')
app.register_blueprint(users_bp, url_prefix='/api/users')

@app.route('/api/health')
def health_check():
    return jsonify({'status': 'healthy', 'message': 'Python Learning Platform API is running!'})

@app.route('/')
def index():
    return jsonify({
        'message': 'Welcome to Python Learning Platform API!',
        'version': '1.0.0',
        'endpoints': {
            'health': '/api/health',
            'auth': '/api/auth',
            'lessons': '/api/lessons',
            'users': '/api/users'
        }
    })

# Error handlers
@app.errorhandler(404)
def not_found(error):
    return jsonify({'error': 'Not found'}), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({'error': 'Internal server error'}), 500

@jwt.expired_token_loader
def expired_token_callback(jwt_header, jwt_payload):
    return jsonify({'error': 'Token has expired'}), 401

@jwt.invalid_token_loader
def invalid_token_callback(error):
    return jsonify({'error': 'Invalid token'}), 401

@jwt.unauthorized_loader
def missing_token_callback(error):
    return jsonify({'error': 'Authorization token is required'}), 401

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
        # Create sample data if needed
        from seed_data import create_sample_data
        create_sample_data()
    
    app.run(debug=True, host='0.0.0.0', port=5000)
