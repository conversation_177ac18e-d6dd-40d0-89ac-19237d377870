Module(body=[FunctionDef(name='divide',
                         args=arguments(args=[Name(id='x',
                                                   ctx=Param()),
                                              Name(id='y',
                                                   ctx=Param())],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[]),
                         body=[TryExcept(body=[Assign(targets=[Name(id='result',
                                                                    ctx=Store())],
                                                      value=BinOp(left=Name(id='x',
                                                                            ctx=Load()),
                                                                  op=Div(),
                                                                  right=Name(id='y',
                                                                             ctx=Load())))],
                                         handlers=[ExceptHandler(type=Name(id='ZeroDivisionError',
                                                                           ctx=Load()),
                                                                 name=None,
                                                                 body=[Print(dest=None,
                                                                             values=[Str(s='division by zero!')],
                                                                             nl=True)])],
                                         orelse=[Print(dest=None,
                                                       values=[Str(s='result is'),
                                                               Name(id='result',
                                                                    ctx=Load())],
                                                       nl=True)])],
                         decorator_list=[]),
             Expr(value=Call(func=Name(id='divide',
                                       ctx=Load()),
                             args=[Num(n=2),
                                   Num(n=1)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='divide',
                                       ctx=Load()),
                             args=[Num(n=2),
                                   Num(n=0)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             TryExcept(body=[Expr(value=Call(func=Name(id='divide',
                                                       ctx=Load()),
                                             args=[Str(s='2'),
                                                   Str(s='1')],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None))],
                       handlers=[ExceptHandler(type=Name(id='TypeError',
                                                         ctx=Load()),
                                               name=Name(id='e',
                                                         ctx=Store()),
                                               body=[Print(dest=None,
                                                           values=[Name(id='e',
                                                                        ctx=Load())],
                                                           nl=True)])],
                       orelse=[])])
