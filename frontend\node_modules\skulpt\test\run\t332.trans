Module(body=[Assign(targets=[Name(id='f',
                                  ctx=Store())],
                    value=Num(n=2.515)),
             Assign(targets=[Name(id='g',
                                  ctx=Store())],
                    value=Call(func=Name(id='round',
                                         ctx=Load()),
                               args=[Name(id='f',
                                          ctx=Load())],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Name(id='g',
                                ctx=Load())],
                   nl=True),
             Assign(targets=[Name(id='g',
                                  ctx=Store())],
                    value=Call(func=Name(id='round',
                                         ctx=Load()),
                               args=[Name(id='f',
                                          ctx=Load()),
                                     Num(n=1)],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Name(id='g',
                                ctx=Load())],
                   nl=True),
             Assign(targets=[Name(id='g',
                                  ctx=Store())],
                    value=Call(func=Name(id='round',
                                         ctx=Load()),
                               args=[Name(id='f',
                                          ctx=Load()),
                                     Num(n=2)],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Name(id='g',
                                ctx=Load())],
                   nl=True),
             Assign(targets=[Name(id='g',
                                  ctx=Store())],
                    value=Call(func=Name(id='round',
                                         ctx=Load()),
                               args=[Name(id='f',
                                          ctx=Load()),
                                     Num(n=3)],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Name(id='g',
                                ctx=Load())],
                   nl=True),
             Assign(targets=[Name(id='g',
                                  ctx=Store())],
                    value=Call(func=Name(id='round',
                                         ctx=Load()),
                               args=[Name(id='f',
                                          ctx=Load()),
                                     Num(n=4)],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Name(id='g',
                                ctx=Load())],
                   nl=True)])
