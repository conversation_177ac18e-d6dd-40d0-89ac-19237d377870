Module(body=[Assign(targets=[Name(id='t',
                                  ctx=Store())],
                    value=Tuple(elts=[Num(n=1),
                                      Num(n=2),
                                      Num(n=3),
                                      Num(n=4),
                                      Num(n=2),
                                      Num(n=1)],
                                ctx=Load())),
             Print(dest=None,
                   values=[Compare(left=Call(func=Attribute(value=Name(id='t',
                                                                       ctx=Load()),
                                                            attr='index',
                                                            ctx=Load()),
                                             args=[Num(n=1)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Num(n=0)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Attribute(value=Name(id='t',
                                                                       ctx=Load()),
                                                            attr='index',
                                                            ctx=Load()),
                                             args=[Num(n=2)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Num(n=1)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Attribute(value=Name(id='t',
                                                                       ctx=Load()),
                                                            attr='count',
                                                            ctx=Load()),
                                             args=[Num(n=1)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Num(n=2)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Attribute(value=Name(id='t',
                                                                       ctx=Load()),
                                                            attr='count',
                                                            ctx=Load()),
                                             args=[Num(n=2)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Num(n=2)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Attribute(value=Name(id='t',
                                                                       ctx=Load()),
                                                            attr='count',
                                                            ctx=Load()),
                                             args=[Num(n=4)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Num(n=1)])],
                   nl=True)])
