Module(body=[Print(dest=None,
                   values=[Subscript(value=Call(func=Attribute(value=Str(s='X-OK-Y'),
                                                               attr='split',
                                                               ctx=Load()),
                                                args=[Str(s='-')],
                                                keywords=[],
                                                starargs=None,
                                                kwargs=None),
                                     slice=Index(value=Num(n=1)),
                                     ctx=Load())],
                   nl=True)])
