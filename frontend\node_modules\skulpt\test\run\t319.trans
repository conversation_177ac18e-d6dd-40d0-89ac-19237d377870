Module(body=[Assign(targets=[Name(id='x',
                                  ctx=Store())],
                    value=Dict(keys=[],
                               values=[])),
             Assign(targets=[Subscript(value=Name(id='x',
                                                  ctx=Load()),
                                       slice=Index(value=Str(s='a')),
                                       ctx=Store())],
                    value=Num(n=1)),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='x',
                                                          ctx=Load()),
                                               attr='get',
                                               ctx=Load()),
                                args=[Str(s='a')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='x',
                                                          ctx=Load()),
                                               attr='get',
                                               ctx=Load()),
                                args=[Str(s='b')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True)])
