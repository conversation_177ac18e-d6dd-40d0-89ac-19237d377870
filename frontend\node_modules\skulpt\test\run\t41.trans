Module(body=[Assign(targets=[Name(id='x',
                                  ctx=Store())],
                    value=ListComp(elt=BinOp(left=Name(id='v',
                                                       ctx=Load()),
                                             op=Mult(),
                                             right=Name(id='v',
                                                        ctx=Load())),
                                   generators=[comprehension(target=Name(id='v',
                                                                         ctx=Store()),
                                                             iter=Call(func=Name(id='range',
                                                                                 ctx=Load()),
                                                                       args=[Num(n=0),
                                                                             Num(n=5)],
                                                                       keywords=[],
                                                                       starargs=None,
                                                                       kwargs=None),
                                                             ifs=[])])),
             Print(dest=None,
                   values=[Subscript(value=Name(id='x',
                                                ctx=Load()),
                                     slice=Index(value=Num(n=3)),
                                     ctx=Load())],
                   nl=True)])
