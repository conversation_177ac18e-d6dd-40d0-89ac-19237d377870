Module(body=[Print(dest=None,
                   values=[Str(s='Big number test')],
                   nl=True),
             Assign(targets=[Name(id='v',
                                  ctx=Store())],
                    value=List(elts=[Num(n=1),
                                     <PERSON><PERSON>(n=1.0),
                                     <PERSON><PERSON>(n=1),
                                     <PERSON><PERSON>(n=-1),
                                     <PERSON><PERSON>(n=-1.0),
                                     <PERSON><PERSON>(n=-1),
                                     <PERSON><PERSON>(n=2),
                                     <PERSON><PERSON>(n=2.0),
                                     <PERSON><PERSON>(n=2),
                                     <PERSON><PERSON>(n=-2),
                                     <PERSON><PERSON>(n=-2.0),
                                     <PERSON><PERSON>(n=-2),
                                     <PERSON><PERSON>(n=1000000000.0),
                                     <PERSON><PERSON>(n=-1000000000.0),
                                     <PERSON><PERSON>(n=1e-09),
                                     <PERSON><PERSON>(n=-1e-09),
                                     <PERSON><PERSON>(n=123456789),
                                     <PERSON><PERSON>(n=12345678901234567890123456789)],
                               ctx=Load())),
             Assign(targets=[Name(id='o',
                                  ctx=Store())],
                    value=List(elts=[Str(s='+'),
                                     Str(s='-'),
                                     <PERSON>r(s='*'),
                                     <PERSON>r(s='/'),
                                     <PERSON>r(s='**'),
                                     <PERSON>r(s='%'),
                                     <PERSON>r(s='<'),
                                     Str(s='='),
                                     Str(s='>'),
                                     Str(s='<='),
                                     Str(s='!='),
                                     Str(s='>=')],
                               ctx=Load())),
             FunctionDef(name='oper',
                         args=arguments(args=[Name(id='v1',
                                                   ctx=Param()),
                                              Name(id='v2',
                                                   ctx=Param()),
                                              Name(id='op',
                                                   ctx=Param())],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[]),
                         body=[If(test=Compare(left=Name(id='op',
                                                         ctx=Load()),
                                               ops=[Eq()],
                                               comparators=[Str(s='+')]),
                                  body=[Print(dest=None,
                                              values=[Str(s='              '),
                                                      Name(id='v1',
                                                           ctx=Load()),
                                                      Name(id='op',
                                                           ctx=Load()),
                                                      Name(id='v2',
                                                           ctx=Load()),
                                                      Str(s='='),
                                                      BinOp(left=Name(id='v1',
                                                                      ctx=Load()),
                                                            op=Add(),
                                                            right=Name(id='v2',
                                                                       ctx=Load())),
                                                      Call(func=Name(id='type',
                                                                     ctx=Load()),
                                                           args=[BinOp(left=Name(id='v1',
                                                                                 ctx=Load()),
                                                                       op=Add(),
                                                                       right=Name(id='v2',
                                                                                  ctx=Load()))],
                                                           keywords=[],
                                                           starargs=None,
                                                           kwargs=None)],
                                              nl=True)],
                                  orelse=[If(test=Compare(left=Name(id='op',
                                                                    ctx=Load()),
                                                          ops=[Eq()],
                                                          comparators=[Str(s='-')]),
                                             body=[Print(dest=None,
                                                         values=[Str(s='              '),
                                                                 Name(id='v1',
                                                                      ctx=Load()),
                                                                 Name(id='op',
                                                                      ctx=Load()),
                                                                 Name(id='v2',
                                                                      ctx=Load()),
                                                                 Str(s='='),
                                                                 BinOp(left=Name(id='v1',
                                                                                 ctx=Load()),
                                                                       op=Sub(),
                                                                       right=Name(id='v2',
                                                                                  ctx=Load())),
                                                                 Call(func=Name(id='type',
                                                                                ctx=Load()),
                                                                      args=[BinOp(left=Name(id='v1',
                                                                                            ctx=Load()),
                                                                                  op=Sub(),
                                                                                  right=Name(id='v2',
                                                                                             ctx=Load()))],
                                                                      keywords=[],
                                                                      starargs=None,
                                                                      kwargs=None)],
                                                         nl=True)],
                                             orelse=[If(test=Compare(left=Name(id='op',
                                                                               ctx=Load()),
                                                                     ops=[Eq()],
                                                                     comparators=[Str(s='*')]),
                                                        body=[Print(dest=None,
                                                                    values=[Str(s='              '),
                                                                            Name(id='v1',
                                                                                 ctx=Load()),
                                                                            Name(id='op',
                                                                                 ctx=Load()),
                                                                            Name(id='v2',
                                                                                 ctx=Load()),
                                                                            Str(s='='),
                                                                            BinOp(left=Name(id='v1',
                                                                                            ctx=Load()),
                                                                                  op=Mult(),
                                                                                  right=Name(id='v2',
                                                                                             ctx=Load())),
                                                                            Call(func=Name(id='type',
                                                                                           ctx=Load()),
                                                                                 args=[BinOp(left=Name(id='v1',
                                                                                                       ctx=Load()),
                                                                                             op=Mult(),
                                                                                             right=Name(id='v2',
                                                                                                        ctx=Load()))],
                                                                                 keywords=[],
                                                                                 starargs=None,
                                                                                 kwargs=None)],
                                                                    nl=True)],
                                                        orelse=[If(test=Compare(left=Name(id='op',
                                                                                          ctx=Load()),
                                                                                ops=[Eq()],
                                                                                comparators=[Str(s='/')]),
                                                                   body=[Print(dest=None,
                                                                               values=[Str(s='              '),
                                                                                       Name(id='v1',
                                                                                            ctx=Load()),
                                                                                       Name(id='op',
                                                                                            ctx=Load()),
                                                                                       Name(id='v2',
                                                                                            ctx=Load()),
                                                                                       Str(s='='),
                                                                                       BinOp(left=Name(id='v1',
                                                                                                       ctx=Load()),
                                                                                             op=Div(),
                                                                                             right=Name(id='v2',
                                                                                                        ctx=Load())),
                                                                                       Call(func=Name(id='type',
                                                                                                      ctx=Load()),
                                                                                            args=[BinOp(left=Name(id='v1',
                                                                                                                  ctx=Load()),
                                                                                                        op=Div(),
                                                                                                        right=Name(id='v2',
                                                                                                                   ctx=Load()))],
                                                                                            keywords=[],
                                                                                            starargs=None,
                                                                                            kwargs=None)],
                                                                               nl=True)],
                                                                   orelse=[If(test=Compare(left=Name(id='op',
                                                                                                     ctx=Load()),
                                                                                           ops=[Eq()],
                                                                                           comparators=[Str(s='**')]),
                                                                              body=[If(test=Compare(left=Name(id='v2',
                                                                                                              ctx=Load()),
                                                                                                    ops=[Gt()],
                                                                                                    comparators=[Num(n=100000000)]),
                                                                                       body=[Print(dest=None,
                                                                                                   values=[Str(s='skipping pow of really big number')],
                                                                                                   nl=True),
                                                                                             Return(value=None)],
                                                                                       orelse=[]),
                                                                                    Print(dest=None,
                                                                                          values=[Str(s='              '),
                                                                                                  Name(id='v1',
                                                                                                       ctx=Load()),
                                                                                                  Name(id='op',
                                                                                                       ctx=Load()),
                                                                                                  Name(id='v2',
                                                                                                       ctx=Load()),
                                                                                                  Str(s='='),
                                                                                                  BinOp(left=Name(id='v1',
                                                                                                                  ctx=Load()),
                                                                                                        op=Pow(),
                                                                                                        right=Name(id='v2',
                                                                                                                   ctx=Load())),
                                                                                                  Call(func=Name(id='type',
                                                                                                                 ctx=Load()),
                                                                                                       args=[BinOp(left=Name(id='v1',
                                                                                                                             ctx=Load()),
                                                                                                                   op=Pow(),
                                                                                                                   right=Name(id='v2',
                                                                                                                              ctx=Load()))],
                                                                                                       keywords=[],
                                                                                                       starargs=None,
                                                                                                       kwargs=None)],
                                                                                          nl=True)],
                                                                              orelse=[If(test=Compare(left=Name(id='op',
                                                                                                                ctx=Load()),
                                                                                                      ops=[Eq()],
                                                                                                      comparators=[Str(s='%')]),
                                                                                         body=[Print(dest=None,
                                                                                                     values=[Str(s='              '),
                                                                                                             Name(id='v1',
                                                                                                                  ctx=Load()),
                                                                                                             Name(id='op',
                                                                                                                  ctx=Load()),
                                                                                                             Name(id='v2',
                                                                                                                  ctx=Load()),
                                                                                                             Str(s='='),
                                                                                                             BinOp(left=Name(id='v1',
                                                                                                                             ctx=Load()),
                                                                                                                   op=Mod(),
                                                                                                                   right=Name(id='v2',
                                                                                                                              ctx=Load())),
                                                                                                             Call(func=Name(id='type',
                                                                                                                            ctx=Load()),
                                                                                                                  args=[BinOp(left=Name(id='v1',
                                                                                                                                        ctx=Load()),
                                                                                                                              op=Mod(),
                                                                                                                              right=Name(id='v2',
                                                                                                                                         ctx=Load()))],
                                                                                                                  keywords=[],
                                                                                                                  starargs=None,
                                                                                                                  kwargs=None)],
                                                                                                     nl=True)],
                                                                                         orelse=[If(test=Compare(left=Name(id='op',
                                                                                                                           ctx=Load()),
                                                                                                                 ops=[Eq()],
                                                                                                                 comparators=[Str(s='<')]),
                                                                                                    body=[Print(dest=None,
                                                                                                                values=[Str(s='              '),
                                                                                                                        Name(id='v1',
                                                                                                                             ctx=Load()),
                                                                                                                        Name(id='op',
                                                                                                                             ctx=Load()),
                                                                                                                        Name(id='v2',
                                                                                                                             ctx=Load()),
                                                                                                                        Str(s='='),
                                                                                                                        Compare(left=Name(id='v1',
                                                                                                                                          ctx=Load()),
                                                                                                                                ops=[Lt()],
                                                                                                                                comparators=[Name(id='v2',
                                                                                                                                                  ctx=Load())]),
                                                                                                                        Call(func=Name(id='type',
                                                                                                                                       ctx=Load()),
                                                                                                                             args=[Compare(left=Name(id='v1',
                                                                                                                                                     ctx=Load()),
                                                                                                                                           ops=[Lt()],
                                                                                                                                           comparators=[Name(id='v2',
                                                                                                                                                             ctx=Load())])],
                                                                                                                             keywords=[],
                                                                                                                             starargs=None,
                                                                                                                             kwargs=None)],
                                                                                                                nl=True)],
                                                                                                    orelse=[If(test=Compare(left=Name(id='op',
                                                                                                                                      ctx=Load()),
                                                                                                                            ops=[Eq()],
                                                                                                                            comparators=[Str(s='=')]),
                                                                                                               body=[Print(dest=None,
                                                                                                                           values=[Str(s='              '),
                                                                                                                                   Name(id='v1',
                                                                                                                                        ctx=Load()),
                                                                                                                                   Name(id='op',
                                                                                                                                        ctx=Load()),
                                                                                                                                   Name(id='v2',
                                                                                                                                        ctx=Load()),
                                                                                                                                   Str(s='='),
                                                                                                                                   Compare(left=Name(id='v1',
                                                                                                                                                     ctx=Load()),
                                                                                                                                           ops=[Eq()],
                                                                                                                                           comparators=[Name(id='v2',
                                                                                                                                                             ctx=Load())]),
                                                                                                                                   Call(func=Name(id='type',
                                                                                                                                                  ctx=Load()),
                                                                                                                                        args=[Compare(left=Name(id='v1',
                                                                                                                                                                ctx=Load()),
                                                                                                                                                      ops=[Eq()],
                                                                                                                                                      comparators=[Name(id='v2',
                                                                                                                                                                        ctx=Load())])],
                                                                                                                                        keywords=[],
                                                                                                                                        starargs=None,
                                                                                                                                        kwargs=None)],
                                                                                                                           nl=True)],
                                                                                                               orelse=[If(test=Compare(left=Name(id='op',
                                                                                                                                                 ctx=Load()),
                                                                                                                                       ops=[Eq()],
                                                                                                                                       comparators=[Str(s='>')]),
                                                                                                                          body=[Print(dest=None,
                                                                                                                                      values=[Str(s='              '),
                                                                                                                                              Name(id='v1',
                                                                                                                                                   ctx=Load()),
                                                                                                                                              Name(id='op',
                                                                                                                                                   ctx=Load()),
                                                                                                                                              Name(id='v2',
                                                                                                                                                   ctx=Load()),
                                                                                                                                              Str(s='='),
                                                                                                                                              Compare(left=Name(id='v1',
                                                                                                                                                                ctx=Load()),
                                                                                                                                                      ops=[Gt()],
                                                                                                                                                      comparators=[Name(id='v2',
                                                                                                                                                                        ctx=Load())]),
                                                                                                                                              Call(func=Name(id='type',
                                                                                                                                                             ctx=Load()),
                                                                                                                                                   args=[Compare(left=Name(id='v1',
                                                                                                                                                                           ctx=Load()),
                                                                                                                                                                 ops=[Gt()],
                                                                                                                                                                 comparators=[Name(id='v2',
                                                                                                                                                                                   ctx=Load())])],
                                                                                                                                                   keywords=[],
                                                                                                                                                   starargs=None,
                                                                                                                                                   kwargs=None)],
                                                                                                                                      nl=True)],
                                                                                                                          orelse=[If(test=Compare(left=Name(id='op',
                                                                                                                                                            ctx=Load()),
                                                                                                                                                  ops=[Eq()],
                                                                                                                                                  comparators=[Str(s='<=')]),
                                                                                                                                     body=[Print(dest=None,
                                                                                                                                                 values=[Str(s='              '),
                                                                                                                                                         Name(id='v1',
                                                                                                                                                              ctx=Load()),
                                                                                                                                                         Name(id='op',
                                                                                                                                                              ctx=Load()),
                                                                                                                                                         Name(id='v2',
                                                                                                                                                              ctx=Load()),
                                                                                                                                                         Str(s='='),
                                                                                                                                                         Compare(left=Name(id='v1',
                                                                                                                                                                           ctx=Load()),
                                                                                                                                                                 ops=[LtE()],
                                                                                                                                                                 comparators=[Name(id='v2',
                                                                                                                                                                                   ctx=Load())]),
                                                                                                                                                         Call(func=Name(id='type',
                                                                                                                                                                        ctx=Load()),
                                                                                                                                                              args=[Compare(left=Name(id='v1',
                                                                                                                                                                                      ctx=Load()),
                                                                                                                                                                            ops=[LtE()],
                                                                                                                                                                            comparators=[Name(id='v2',
                                                                                                                                                                                              ctx=Load())])],
                                                                                                                                                              keywords=[],
                                                                                                                                                              starargs=None,
                                                                                                                                                              kwargs=None)],
                                                                                                                                                 nl=True)],
                                                                                                                                     orelse=[If(test=Compare(left=Name(id='op',
                                                                                                                                                                       ctx=Load()),
                                                                                                                                                             ops=[Eq()],
                                                                                                                                                             comparators=[Str(s='!=')]),
                                                                                                                                                body=[Print(dest=None,
                                                                                                                                                            values=[Str(s='              '),
                                                                                                                                                                    Name(id='v1',
                                                                                                                                                                         ctx=Load()),
                                                                                                                                                                    Name(id='op',
                                                                                                                                                                         ctx=Load()),
                                                                                                                                                                    Name(id='v2',
                                                                                                                                                                         ctx=Load()),
                                                                                                                                                                    Str(s='='),
                                                                                                                                                                    Compare(left=Name(id='v1',
                                                                                                                                                                                      ctx=Load()),
                                                                                                                                                                            ops=[NotEq()],
                                                                                                                                                                            comparators=[Name(id='v2',
                                                                                                                                                                                              ctx=Load())]),
                                                                                                                                                                    Call(func=Name(id='type',
                                                                                                                                                                                   ctx=Load()),
                                                                                                                                                                         args=[Compare(left=Name(id='v1',
                                                                                                                                                                                                 ctx=Load()),
                                                                                                                                                                                       ops=[NotEq()],
                                                                                                                                                                                       comparators=[Name(id='v2',
                                                                                                                                                                                                         ctx=Load())])],
                                                                                                                                                                         keywords=[],
                                                                                                                                                                         starargs=None,
                                                                                                                                                                         kwargs=None)],
                                                                                                                                                            nl=True)],
                                                                                                                                                orelse=[If(test=Compare(left=Name(id='op',
                                                                                                                                                                                  ctx=Load()),
                                                                                                                                                                        ops=[Eq()],
                                                                                                                                                                        comparators=[Str(s='>=')]),
                                                                                                                                                           body=[Print(dest=None,
                                                                                                                                                                       values=[Str(s='              '),
                                                                                                                                                                               Name(id='v1',
                                                                                                                                                                                    ctx=Load()),
                                                                                                                                                                               Name(id='op',
                                                                                                                                                                                    ctx=Load()),
                                                                                                                                                                               Name(id='v2',
                                                                                                                                                                                    ctx=Load()),
                                                                                                                                                                               Str(s='='),
                                                                                                                                                                               Compare(left=Name(id='v1',
                                                                                                                                                                                                 ctx=Load()),
                                                                                                                                                                                       ops=[GtE()],
                                                                                                                                                                                       comparators=[Name(id='v2',
                                                                                                                                                                                                         ctx=Load())]),
                                                                                                                                                                               Call(func=Name(id='type',
                                                                                                                                                                                              ctx=Load()),
                                                                                                                                                                                    args=[Compare(left=Name(id='v1',
                                                                                                                                                                                                            ctx=Load()),
                                                                                                                                                                                                  ops=[GtE()],
                                                                                                                                                                                                  comparators=[Name(id='v2',
                                                                                                                                                                                                                    ctx=Load())])],
                                                                                                                                                                                    keywords=[],
                                                                                                                                                                                    starargs=None,
                                                                                                                                                                                    kwargs=None)],
                                                                                                                                                                       nl=True)],
                                                                                                                                                           orelse=[])])])])])])])])])])])])],
                         decorator_list=[]),
             For(target=Name(id='x',
                             ctx=Store()),
                 iter=Name(id='v',
                           ctx=Load()),
                 body=[Print(dest=None,
                             values=[Str(s='Op 1 ::: '),
                                     Call(func=Name(id='type',
                                                    ctx=Load()),
                                          args=[Name(id='x',
                                                     ctx=Load())],
                                          keywords=[],
                                          starargs=None,
                                          kwargs=None),
                                     Name(id='x',
                                          ctx=Load())],
                             nl=True),
                       For(target=Name(id='y',
                                       ctx=Store()),
                           iter=Name(id='v',
                                     ctx=Load()),
                           body=[Print(dest=None,
                                       values=[Str(s='     Op 2 ::: '),
                                               Call(func=Name(id='type',
                                                              ctx=Load()),
                                                    args=[Name(id='y',
                                                               ctx=Load())],
                                                    keywords=[],
                                                    starargs=None,
                                                    kwargs=None),
                                               Name(id='y',
                                                    ctx=Load())],
                                       nl=True),
                                 For(target=Name(id='z',
                                                 ctx=Store()),
                                     iter=Name(id='o',
                                               ctx=Load()),
                                     body=[TryExcept(body=[Expr(value=Call(func=Name(id='oper',
                                                                                     ctx=Load()),
                                                                           args=[Name(id='x',
                                                                                      ctx=Load()),
                                                                                 Name(id='y',
                                                                                      ctx=Load()),
                                                                                 Name(id='z',
                                                                                      ctx=Load())],
                                                                           keywords=[],
                                                                           starargs=None,
                                                                           kwargs=None))],
                                                     handlers=[ExceptHandler(type=None,
                                                                             name=None,
                                                                             body=[Print(dest=None,
                                                                                         values=[Str(s="Can't "),
                                                                                                 Call(func=Name(id='type',
                                                                                                                ctx=Load()),
                                                                                                      args=[Name(id='x',
                                                                                                                 ctx=Load())],
                                                                                                      keywords=[],
                                                                                                      starargs=None,
                                                                                                      kwargs=None),
                                                                                                 Name(id='z',
                                                                                                      ctx=Load()),
                                                                                                 Call(func=Name(id='type',
                                                                                                                ctx=Load()),
                                                                                                      args=[Name(id='y',
                                                                                                                 ctx=Load())],
                                                                                                      keywords=[],
                                                                                                      starargs=None,
                                                                                                      kwargs=None)],
                                                                                         nl=True)])],
                                                     orelse=[])],
                                     orelse=[])],
                           orelse=[])],
                 orelse=[])])
