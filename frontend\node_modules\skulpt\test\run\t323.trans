Module(body=[ClassDef(name='Foo',
                      bases=[],
                      body=[FunctionDef(name='__init__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param()),
                                                             Name(id='arg',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Assign(targets=[Attribute(value=Name(id='self',
                                                                                   ctx=Load()),
                                                                        attr='x',
                                                                        ctx=Store())],
                                                     value=Name(id='None',
                                                                ctx=Load()))],
                                        decorator_list=[]),
                            FunctionDef(name='__getitem__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param()),
                                                             Name(id='key',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Return(value=Attribute(value=Name(id='self',
                                                                                ctx=Load()),
                                                                     attr='x',
                                                                     ctx=Load()))],
                                        decorator_list=[])],
                      decorator_list=[]),
             Assign(targets=[Name(id='x',
                                  ctx=Store())],
                    value=Call(func=Name(id='Foo',
                                         ctx=Load()),
                               args=[Num(n=5)],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Subscript(value=Name(id='x',
                                                ctx=Load()),
                                     slice=Index(value=Num(n=1)),
                                     ctx=Load())],
                   nl=True)])
