Module(body=[Print(dest=None,
                   values=[Call(func=Name(id='zip',
                                          ctx=Load()),
                                args=[List(elts=[Num(n=8),
                                                 Num(n=9),
                                                 Num(n=10)],
                                           ctx=Load()),
                                      List(elts=[Num(n=11),
                                                 Num(n=12),
                                                 Num(n=13)],
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='zip',
                                          ctx=Load()),
                                args=[List(elts=[Num(n=1),
                                                 Num(n=2),
                                                 Num(n=3)],
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='zip',
                                          ctx=Load()),
                                args=[],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='zip',
                                          ctx=Load()),
                                args=[List(elts=[],
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True)])
