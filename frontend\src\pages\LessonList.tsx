import React, { useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useLessonStore } from '../store/lessonStore';
import { 
  BookOpen, 
  Clock, 
  Lock, 
  CheckCircle, 
  Play,
  Star,
  Target,
  TrendingUp
} from 'lucide-react';

const LessonList: React.FC = () => {
  const { lessons, progress, fetchLessons, isLoading } = useLessonStore();

  useEffect(() => {
    fetchLessons();
  }, [fetchLessons]);

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return 'bg-green-100 text-green-800';
      case 'intermediate': return 'bg-yellow-100 text-yellow-800';
      case 'advanced': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getLessonProgress = (lessonId: string) => {
    return progress.find(p => p.lessonId === lessonId);
  };

  const completedLessons = progress.filter(p => p.completed).length;
  const totalLessons = lessons.length;
  const progressPercentage = totalLessons > 0 ? (completedLessons / totalLessons) * 100 : 0;

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading lessons...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-kid-3xl font-bold text-gray-900 mb-4">
            Python Lessons 📚
          </h1>
          <p className="text-kid-lg text-gray-600 mb-6">
            Learn Python step by step with our interactive lessons designed for kids!
          </p>
          
          {/* Progress Overview */}
          <div className="card mb-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-kid-xl font-bold text-gray-900 flex items-center">
                <Target className="mr-2 text-primary-500" size={24} />
                Your Progress
              </h2>
              <div className="text-kid-base font-medium text-gray-700">
                {completedLessons} / {totalLessons} completed
              </div>
            </div>
            
            <div className="progress-bar mb-4">
              <div 
                className="progress-fill" 
                style={{ width: `${progressPercentage}%` }}
              />
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
              <div className="bg-primary-50 p-4 rounded-kid-lg">
                <div className="text-2xl font-bold text-primary-600 mb-1">{Math.round(progressPercentage)}%</div>
                <div className="text-primary-700 text-kid-sm">Complete</div>
              </div>
              <div className="bg-secondary-50 p-4 rounded-kid-lg">
                <div className="text-2xl font-bold text-secondary-600 mb-1">{totalLessons - completedLessons}</div>
                <div className="text-secondary-700 text-kid-sm">Remaining</div>
              </div>
              <div className="bg-success-50 p-4 rounded-kid-lg">
                <div className="text-2xl font-bold text-success-600 mb-1">{completedLessons * 10}</div>
                <div className="text-success-700 text-kid-sm">Points Earned</div>
              </div>
            </div>
          </div>
        </div>

        {/* Lessons Grid */}
        <div className="grid gap-6">
          {lessons.map((lesson, index) => {
            const lessonProgress = getLessonProgress(lesson.id);
            const isCompleted = lessonProgress?.completed || false;
            const isUnlocked = lesson.isUnlocked;
            
            return (
              <div 
                key={lesson.id}
                className={`card transition-all duration-200 ${
                  isUnlocked 
                    ? 'hover:shadow-kid-lg cursor-pointer' 
                    : 'opacity-60 cursor-not-allowed'
                }`}
              >
                <div className="flex items-start space-x-4">
                  {/* Lesson Number & Status */}
                  <div className="flex-shrink-0">
                    <div className={`w-12 h-12 rounded-kid-lg flex items-center justify-center font-bold text-white ${
                      isCompleted 
                        ? 'bg-gradient-to-br from-success-400 to-success-600' 
                        : isUnlocked
                        ? 'bg-gradient-to-br from-primary-400 to-secondary-400'
                        : 'bg-gray-400'
                    }`}>
                      {isCompleted ? (
                        <CheckCircle size={24} />
                      ) : isUnlocked ? (
                        <span>{lesson.order}</span>
                      ) : (
                        <Lock size={20} />
                      )}
                    </div>
                  </div>

                  {/* Lesson Content */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between mb-2">
                      <h3 className="text-kid-xl font-bold text-gray-900 mb-1">
                        {lesson.title}
                      </h3>
                      <div className="flex items-center space-x-2 ml-4">
                        <span className={`px-3 py-1 rounded-kid text-kid-xs font-medium ${getDifficultyColor(lesson.difficulty)}`}>
                          {lesson.difficulty}
                        </span>
                        {isCompleted && (
                          <div className="flex items-center space-x-1 bg-success-100 px-2 py-1 rounded-kid">
                            <Star className="text-success-600" size={14} />
                            <span className="text-success-700 text-kid-xs font-medium">+10 pts</span>
                          </div>
                        )}
                      </div>
                    </div>
                    
                    <p className="text-gray-600 mb-3 line-clamp-2">
                      {lesson.description}
                    </p>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4 text-kid-sm text-gray-500">
                        <div className="flex items-center space-x-1">
                          <Clock size={16} />
                          <span>{lesson.estimatedTime} min</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <BookOpen size={16} />
                          <span>{lesson.exercises.length} exercises</span>
                        </div>
                        {lesson.prerequisites.length > 0 && (
                          <div className="flex items-center space-x-1">
                            <TrendingUp size={16} />
                            <span>Requires: Lesson {lesson.prerequisites.join(', ')}</span>
                          </div>
                        )}
                      </div>
                      
                      <div className="flex items-center space-x-3">
                        {isCompleted && lessonProgress && (
                          <div className="text-kid-sm text-gray-500">
                            Score: {lessonProgress.score}%
                          </div>
                        )}
                        
                        {isUnlocked ? (
                          <Link 
                            to={`/lessons/${lesson.id}`}
                            className={`inline-flex items-center px-4 py-2 rounded-kid-lg font-medium transition-all duration-200 ${
                              isCompleted
                                ? 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                                : 'btn-primary'
                            }`}
                          >
                            {isCompleted ? (
                              <>
                                <BookOpen className="mr-2" size={16} />
                                Review
                              </>
                            ) : (
                              <>
                                <Play className="mr-2" size={16} />
                                Start Lesson
                              </>
                            )}
                          </Link>
                        ) : (
                          <div className="inline-flex items-center px-4 py-2 rounded-kid-lg font-medium bg-gray-100 text-gray-500">
                            <Lock className="mr-2" size={16} />
                            Locked
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Empty State */}
        {lessons.length === 0 && !isLoading && (
          <div className="text-center py-12">
            <BookOpen size={64} className="mx-auto text-gray-400 mb-4" />
            <h3 className="text-kid-xl font-medium text-gray-900 mb-2">No lessons available</h3>
            <p className="text-gray-600">Check back soon for new Python lessons!</p>
          </div>
        )}

        {/* Call to Action */}
        {completedLessons > 0 && completedLessons < totalLessons && (
          <div className="mt-12 text-center">
            <div className="card bg-gradient-to-r from-primary-50 to-secondary-50 border-primary-200">
              <h3 className="text-kid-xl font-bold text-gray-900 mb-2">
                Keep Going! 🚀
              </h3>
              <p className="text-gray-700 mb-4">
                You're doing great! Complete more lessons to earn points and unlock new badges.
              </p>
              <Link to="/dashboard" className="btn-primary">
                View Dashboard
              </Link>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default LessonList;
