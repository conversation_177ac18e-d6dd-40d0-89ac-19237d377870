Module(body=[Assign(targets=[Name(id='s',
                                  ctx=Store())],
                    value=Call(func=Name(id='set',
                                         ctx=Load()),
                               args=[List(elts=[Num(n=1),
                                                Num(n=2),
                                                Num(n=3)],
                                          ctx=Load())],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Assign(targets=[Name(id='copy_s',
                                  ctx=Store())],
                    value=Call(func=Attribute(value=Name(id='s',
                                                         ctx=Load()),
                                              attr='copy',
                                              ctx=Load()),
                               args=[],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Assign(targets=[Name(id='new_s',
                                  ctx=Store())],
                    value=Call(func=Name(id='set',
                                         ctx=Load()),
                               args=[Name(id='s',
                                          ctx=Load())],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Expr(value=Call(func=Attribute(value=Name(id='copy_s',
                                                       ctx=Load()),
                                            attr='add',
                                            ctx=Load()),
                             args=[Num(n=42)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Attribute(value=Name(id='new_s',
                                                       ctx=Load()),
                                            attr='add',
                                            ctx=Load()),
                             args=[Num(n=13)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Name(id='s',
                                ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Name(id='copy_s',
                                ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Name(id='new_s',
                                ctx=Load())],
                   nl=True)])
