Module(body=[Print(dest=None,
                   values=[Str(s='EVALUATE TO TRUE')],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='isinstance',
                                          ctx=Load()),
                                args=[Str(s='hello'),
                                      Name(id='str',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='isinstance',
                                          ctx=Load()),
                                args=[Num(n=1234),
                                      Name(id='int',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='isinstance',
                                          ctx=Load()),
                                args=[Num(n=56),
                                      Name(id='long',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='isinstance',
                                          ctx=Load()),
                                args=[Num(n=7.89),
                                      Name(id='float',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             ClassDef(name='A',
                      bases=[],
                      body=[FunctionDef(name='__init__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Pass()],
                                        decorator_list=[])],
                      decorator_list=[]),
             ClassDef(name='B',
                      bases=[Name(id='A',
                                  ctx=Load())],
                      body=[FunctionDef(name='__init__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Pass()],
                                        decorator_list=[])],
                      decorator_list=[]),
             ClassDef(name='C',
                      bases=[Name(id='B',
                                  ctx=Load())],
                      body=[FunctionDef(name='__init__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Pass()],
                                        decorator_list=[])],
                      decorator_list=[]),
             ClassDef(name='D',
                      bases=[],
                      body=[FunctionDef(name='__init__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Pass()],
                                        decorator_list=[])],
                      decorator_list=[]),
             Assign(targets=[Name(id='a',
                                  ctx=Store())],
                    value=Call(func=Name(id='A',
                                         ctx=Load()),
                               args=[],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Call(func=Name(id='isinstance',
                                          ctx=Load()),
                                args=[Name(id='a',
                                           ctx=Load()),
                                      Name(id='A',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='isinstance',
                                          ctx=Load()),
                                args=[Call(func=Name(id='A',
                                                     ctx=Load()),
                                           args=[],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None),
                                      Name(id='A',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='isinstance',
                                          ctx=Load()),
                                args=[Call(func=Name(id='B',
                                                     ctx=Load()),
                                           args=[],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None),
                                      Name(id='A',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='isinstance',
                                          ctx=Load()),
                                args=[Call(func=Name(id='C',
                                                     ctx=Load()),
                                           args=[],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None),
                                      Name(id='A',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='isinstance',
                                          ctx=Load()),
                                args=[Call(func=Name(id='C',
                                                     ctx=Load()),
                                           args=[],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None),
                                      Tuple(elts=[Name(id='D',
                                                       ctx=Load()),
                                                  Name(id='A',
                                                       ctx=Load())],
                                            ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='EVALUATE TO FALSE')],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='isinstance',
                                          ctx=Load()),
                                args=[Call(func=Name(id='D',
                                                     ctx=Load()),
                                           args=[],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None),
                                      Name(id='A',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='isinstance',
                                          ctx=Load()),
                                args=[Call(func=Name(id='A',
                                                     ctx=Load()),
                                           args=[],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None),
                                      Tuple(elts=[Name(id='B',
                                                       ctx=Load()),
                                                  Name(id='C',
                                                       ctx=Load())],
                                            ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='isinstance',
                                          ctx=Load()),
                                args=[Call(func=Name(id='A',
                                                     ctx=Load()),
                                           args=[],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None),
                                      Tuple(elts=[Name(id='D',
                                                       ctx=Load()),
                                                  Tuple(elts=[Name(id='B',
                                                                   ctx=Load()),
                                                              Name(id='C',
                                                                   ctx=Load())],
                                                        ctx=Load())],
                                            ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='isinstance',
                                          ctx=Load()),
                                args=[Str(s='hello'),
                                      Name(id='int',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True)])
