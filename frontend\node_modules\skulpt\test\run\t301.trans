Module(body=[Print(dest=None,
                   values=[Str(s='# actual super & subsets')],
                   nl=True),
             Assign(targets=[Name(id='sup',
                                  ctx=Store())],
                    value=Call(func=Name(id='set',
                                         ctx=Load()),
                               args=[List(elts=[Num(n=1),
                                                Num(n=2),
                                                Num(n=3),
                                                Num(n=4),
                                                Num(n=100)],
                                          ctx=Load())],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Name(id='sup',
                                ctx=Load())],
                   nl=True),
             Assign(targets=[Name(id='sub',
                                  ctx=Store())],
                    value=Call(func=Name(id='set',
                                         ctx=Load()),
                               args=[List(elts=[Num(n=2),
                                                Num(n=3),
                                                Num(n=4)],
                                          ctx=Load())],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Name(id='sub',
                                ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='# forwards')],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='sup',
                                                          ctx=Load()),
                                               attr='isdisjoint',
                                               ctx=Load()),
                                args=[Name(id='sub',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='sup',
                                             ctx=Load()),
                                   ops=[Gt()],
                                   comparators=[Name(id='sub',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='sup',
                                                          ctx=Load()),
                                               attr='issuperset',
                                               ctx=Load()),
                                args=[Name(id='sub',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='sup',
                                             ctx=Load()),
                                   ops=[GtE()],
                                   comparators=[Name(id='sub',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='sup',
                                             ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Name(id='sub',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='sup',
                                             ctx=Load()),
                                   ops=[NotEq()],
                                   comparators=[Name(id='sub',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='sup',
                                                          ctx=Load()),
                                               attr='issubset',
                                               ctx=Load()),
                                args=[Name(id='sub',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='sup',
                                             ctx=Load()),
                                   ops=[LtE()],
                                   comparators=[Name(id='sub',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='sup',
                                             ctx=Load()),
                                   ops=[Lt()],
                                   comparators=[Name(id='sub',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='# backwards')],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='sub',
                                                          ctx=Load()),
                                               attr='isdisjoint',
                                               ctx=Load()),
                                args=[Name(id='sup',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='sub',
                                             ctx=Load()),
                                   ops=[Gt()],
                                   comparators=[Name(id='sup',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='sub',
                                                          ctx=Load()),
                                               attr='issuperset',
                                               ctx=Load()),
                                args=[Name(id='sup',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='sub',
                                             ctx=Load()),
                                   ops=[GtE()],
                                   comparators=[Name(id='sup',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='sub',
                                             ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Name(id='sup',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='sub',
                                             ctx=Load()),
                                   ops=[NotEq()],
                                   comparators=[Name(id='sup',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='sub',
                                                          ctx=Load()),
                                               attr='issubset',
                                               ctx=Load()),
                                args=[Name(id='sup',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='sub',
                                             ctx=Load()),
                                   ops=[LtE()],
                                   comparators=[Name(id='sup',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='sub',
                                             ctx=Load()),
                                   ops=[Lt()],
                                   comparators=[Name(id='sup',
                                                     ctx=Load())])],
                   nl=True)])
