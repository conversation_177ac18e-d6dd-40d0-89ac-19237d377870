Module(body=[Print(dest=None,
                   values=[Compare(left=Call(func=Name(id='set',
                                                       ctx=Load()),
                                             args=[List(elts=[],
                                                        ctx=Load())],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[List(elts=[],
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Name(id='set',
                                                       ctx=Load()),
                                             args=[List(elts=[Str(s='a')],
                                                        ctx=Load())],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[List(elts=[Str(s='a')],
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Name(id='set',
                                                       ctx=Load()),
                                             args=[List(elts=[Str(s='a'),
                                                              Str(s='b')],
                                                        ctx=Load())],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[List(elts=[Str(s='a'),
                                                           Str(s='b')],
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Name(id='set',
                                                       ctx=Load()),
                                             args=[List(elts=[Str(s='b'),
                                                              Str(s='a')],
                                                        ctx=Load())],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[List(elts=[Str(s='a'),
                                                           Str(s='b')],
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Name(id='set',
                                                       ctx=Load()),
                                             args=[List(elts=[Str(s='a'),
                                                              Str(s='c'),
                                                              Str(s='b')],
                                                        ctx=Load())],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[List(elts=[Str(s='c'),
                                                           Str(s='b'),
                                                           Str(s='a')],
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Name(id='set',
                                                       ctx=Load()),
                                             args=[List(elts=[Str(s='a')],
                                                        ctx=Load())],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Call(func=Name(id='set',
                                                               ctx=Load()),
                                                     args=[List(elts=[Str(s='a')],
                                                                ctx=Load())],
                                                     keywords=[],
                                                     starargs=None,
                                                     kwargs=None)])],
                   nl=True)])
