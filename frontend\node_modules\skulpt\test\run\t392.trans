Module(body=[ClassDef(name='Ship',
                      bases=[],
                      body=[FunctionDef(name='__init__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param()),
                                                             Name(id='name',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Assign(targets=[Attribute(value=Name(id='self',
                                                                                   ctx=Load()),
                                                                        attr='name',
                                                                        ctx=Store())],
                                                     value=Name(id='name',
                                                                ctx=Load())),
                                              Assign(targets=[Attribute(value=Name(id='self',
                                                                                   ctx=Load()),
                                                                        attr='thrust',
                                                                        ctx=Store())],
                                                     value=Name(id='False',
                                                                ctx=Load()))],
                                        decorator_list=[]),
                            FunctionDef(name='thrust',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Assign(targets=[Attribute(value=Name(id='self',
                                                                                   ctx=Load()),
                                                                        attr='thrust',
                                                                        ctx=Store())],
                                                     value=Name(id='True',
                                                                ctx=Load())),
                                              Print(dest=None,
                                                    values=[Str(s='Thrust'),
                                                            Attribute(value=Name(id='self',
                                                                                 ctx=Load()),
                                                                      attr='thrust',
                                                                      ctx=Load())],
                                                    nl=True)],
                                        decorator_list=[])],
                      decorator_list=[]),
             Assign(targets=[Name(id='my_ship',
                                  ctx=Store())],
                    value=Call(func=Name(id='Ship',
                                         ctx=Load()),
                               args=[Str(s='a_name')],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Expr(value=Call(func=Attribute(value=Name(id='my_ship',
                                                       ctx=Load()),
                                            attr='thrust',
                                            ctx=Load()),
                             args=[],
                             keywords=[],
                             starargs=None,
                             kwargs=None))])
