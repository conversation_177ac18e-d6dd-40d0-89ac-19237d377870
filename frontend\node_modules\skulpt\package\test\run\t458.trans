Module(body=[Print(dest=None,
                   values=[Str(s='0 % 10 ='),
                           BinOp(left=Num(n=0),
                                 op=Mod(),
                                 right=Num(n=10))],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='3 % 10 ='),
                           BinOp(left=Num(n=3),
                                 op=Mod(),
                                 right=Num(n=10))],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='6 % 10 ='),
                           BinOp(left=Num(n=6),
                                 op=Mod(),
                                 right=Num(n=10))],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='10 % 10 ='),
                           BinOp(left=Num(n=10),
                                 op=Mod(),
                                 right=Num(n=10))],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='14 % 10 ='),
                           BinOp(left=Num(n=14),
                                 op=Mod(),
                                 right=Num(n=10))],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='-3 % 10 ='),
                           BinOp(left=Num(n=-3),
                                 op=Mod(),
                                 right=Num(n=10))],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='-6 % 10 ='),
                           BinOp(left=Num(n=-6),
                                 op=Mod(),
                                 right=Num(n=10))],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='-10 % 10 ='),
                           BinOp(left=Num(n=-10),
                                 op=Mod(),
                                 right=Num(n=10))],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='-14 % 10 ='),
                           BinOp(left=Num(n=-14),
                                 op=Mod(),
                                 right=Num(n=10))],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='3.6 % 10 ='),
                           BinOp(left=Num(n=3.6),
                                 op=Mod(),
                                 right=Num(n=10))],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='-3.6 % 10 ='),
                           BinOp(left=Num(n=-3.6),
                                 op=Mod(),
                                 right=Num(n=10))],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='35.9 % 10 ='),
                           BinOp(left=Num(n=35.9),
                                 op=Mod(),
                                 right=Num(n=10))],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='-35.9 % 10 ='),
                           BinOp(left=Num(n=-35.9),
                                 op=Mod(),
                                 right=Num(n=10))],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='104 % 10 ='),
                           BinOp(left=Num(n=104),
                                 op=Mod(),
                                 right=Num(n=10))],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='-104 % 10 ='),
                           BinOp(left=Num(n=-104),
                                 op=Mod(),
                                 right=Num(n=10))],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='0 % -10 ='),
                           BinOp(left=Num(n=0),
                                 op=Mod(),
                                 right=Num(n=-10))],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='3 % -10 ='),
                           BinOp(left=Num(n=3),
                                 op=Mod(),
                                 right=Num(n=-10))],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='6 % -10 ='),
                           BinOp(left=Num(n=6),
                                 op=Mod(),
                                 right=Num(n=-10))],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='10 % -10 ='),
                           BinOp(left=Num(n=10),
                                 op=Mod(),
                                 right=Num(n=-10))],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='14 % -10 ='),
                           BinOp(left=Num(n=14),
                                 op=Mod(),
                                 right=Num(n=-10))],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='-3 % -10 ='),
                           BinOp(left=Num(n=-3),
                                 op=Mod(),
                                 right=Num(n=-10))],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='-6 % -10 ='),
                           BinOp(left=Num(n=-6),
                                 op=Mod(),
                                 right=Num(n=-10))],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='-10 % -10 ='),
                           BinOp(left=Num(n=-10),
                                 op=Mod(),
                                 right=Num(n=-10))],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='-14 % -10 ='),
                           BinOp(left=Num(n=-14),
                                 op=Mod(),
                                 right=Num(n=-10))],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='3.6 % -10 ='),
                           BinOp(left=Num(n=3.6),
                                 op=Mod(),
                                 right=Num(n=-10))],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='-3.6 % -10 ='),
                           BinOp(left=Num(n=-3.6),
                                 op=Mod(),
                                 right=Num(n=-10))],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='35.9 % -10 ='),
                           BinOp(left=Num(n=35.9),
                                 op=Mod(),
                                 right=Num(n=-10))],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='-35.9 % -10 ='),
                           BinOp(left=Num(n=-35.9),
                                 op=Mod(),
                                 right=Num(n=-10))],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='104 % -10 ='),
                           BinOp(left=Num(n=104),
                                 op=Mod(),
                                 right=Num(n=-10))],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='-104 % -10 ='),
                           BinOp(left=Num(n=-104),
                                 op=Mod(),
                                 right=Num(n=-10))],
                   nl=True)])
