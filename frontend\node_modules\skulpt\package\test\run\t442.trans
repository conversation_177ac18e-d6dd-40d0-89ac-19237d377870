Module(body=[FunctionDef(name='helper',
                         args=arguments(args=[Name(id='got',
                                                   ctx=Param()),
                                              Name(id='expect',
                                                   ctx=Param())],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[]),
                         body=[If(test=Compare(left=Name(id='got',
                                                         ctx=Load()),
                                               ops=[Eq()],
                                               comparators=[Name(id='expect',
                                                                 ctx=Load())]),
                                  body=[Print(dest=None,
                                              values=[Name(id='True',
                                                           ctx=Load())],
                                              nl=True)],
                                  orelse=[Print(dest=None,
                                                values=[Name(id='False',
                                                             ctx=Load()),
                                                        Name(id='expect',
                                                             ctx=Load()),
                                                        Name(id='got',
                                                             ctx=Load())],
                                                nl=True)])],
                         decorator_list=[]),
             Print(dest=None,
                   values=[Str(s='\nstr.split()')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s=''),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s=''),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Name(id='None',
                                                   ctx=Load())],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s=''),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Name(id='None',
                                                   ctx=Load()),
                                              Num(n=1)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s=''),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Str(s='a')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s=''),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Str(s='a'),
                                              Num(n=1)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='hello'),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='hello')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='hello'),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Name(id='None',
                                                   ctx=Load())],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='hello')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='   hello world      '),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='hello'),
                                              Str(s='world')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='   hello world      '),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Name(id='None',
                                                   ctx=Load())],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='hello'),
                                              Str(s='world')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='   hello world      '),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Name(id='None',
                                                   ctx=Load()),
                                              Num(n=1)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='hello'),
                                              Str(s='world      ')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='hello world   ! '),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='hello'),
                                              Str(s='world'),
                                              Str(s='!')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='hello'),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Str(s='l')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='he'),
                                              Str(s=''),
                                              Str(s='o')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='hello'),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Str(s='l'),
                                              Num(n=1)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='he'),
                                              Str(s='lo')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Str(s='\nSEP AS A REGULAR EXPRESSION')],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='without regex syntax')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='aaaba'),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Str(s='a')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s=''),
                                              Str(s=''),
                                              Str(s=''),
                                              Str(s='b'),
                                              Str(s='')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='aaaba'),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Str(s='b')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='aaa'),
                                              Str(s='a')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Str(s='\nsyntax: .')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='aaaba'),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Str(s='a.')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='aaaba')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='aaaba'),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Str(s='.a')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='aaaba')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='aaaba'),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Str(s='a.'),
                                              Num(n=1)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='aaaba')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='aaaba'),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Str(s='.a'),
                                              Num(n=1)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='aaaba')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='aaaba'),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Str(s='b.')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='aaaba')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='aaaba'),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Str(s='.b')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='aaaba')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Str(s='\nsyntax: ^')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='aaaba'),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Str(s='^a')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='aaaba')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='aaaba'),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Str(s='^b')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='aaaba')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Str(s='\nsyntax: $')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='aaaba'),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Str(s='a$')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='aaaba')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='aaaba'),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Str(s='b$')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='aaaba')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Str(s='\nsyntax: *')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='aaaba'),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Str(s='a*')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='aaaba')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='aaaba'),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Str(s='b*')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='aaaba')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='aaaba'),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Str(s='ab*')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='aaaba')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='aaaba'),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Str(s='ab*'),
                                              Num(n=1)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='aaaba')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Str(s='\nsyntax: +')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='aaaba'),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Str(s='a+')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='aaaba')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='aaaba'),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Str(s='b+')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='aaaba')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='aaaba'),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Str(s='ab+')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='aaaba')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Str(s='\nsyntax: ?')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='aaaba'),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Str(s='a?')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='aaaba')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='aaaba'),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Str(s='a?'),
                                              Num(n=1)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='aaaba')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='aaaba'),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Str(s='b?')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='aaaba')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='aaaba'),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Str(s='ab?')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='aaaba')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='aaaba'),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Str(s='ab?'),
                                              Num(n=1)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='aaaba')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Str(s='\nsyntax: *?')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='aaaba'),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Str(s='a*?')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='aaaba')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='aaaba'),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Str(s='b*?')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='aaaba')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='aaaba'),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Str(s='ab*?')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='aaaba')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='aaaba'),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Str(s='ab*?'),
                                              Num(n=1)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='aaaba')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Str(s='\nsyntax: +?')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='aaaba'),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Str(s='a+?')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='aaaba')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='aaaba'),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Str(s='a+?'),
                                              Num(n=1)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='aaaba')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='aaaba'),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Str(s='b+?')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='aaaba')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='aaaba'),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Str(s='ab+?')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='aaaba')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Str(s='\nsyntax: ??')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='aaaba'),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Str(s='a??')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='aaaba')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='aaaba'),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Str(s='b??')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='aaaba')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='aaaba'),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Str(s='ab??')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='aaaba')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='aaaba'),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Str(s='ab??'),
                                              Num(n=1)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='aaaba')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Str(s='\nsyntax: {}')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='aaaba'),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Str(s='a{2}')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='aaaba')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='aaaba'),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Str(s='a{1,2}')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='aaaba')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='aaaba'),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Str(s='a{1,2}'),
                                              Num(n=1)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='aaaba')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='aaaba'),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Str(s='a{,2}')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='aaaba')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='aaaba'),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Str(s='a{1,}')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='aaaba')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='aaaba'),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Str(s='a{1,}'),
                                              Num(n=1)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='aaaba')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='aaaba'),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Str(s='b{1}')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='aaaba')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='aaaba'),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Str(s='b{1,2}')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='aaaba')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='aaaba'),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Str(s='b{,2}')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='aaaba')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='aaaba'),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Str(s='b{1,}')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='aaaba')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Str(s='\nsyntax: {}?')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='aaaba'),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Str(s='a{2}?')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='aaaba')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='aaaba'),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Str(s='a{1,2}?')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='aaaba')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='aaaba'),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Str(s='a{1,2}?'),
                                              Num(n=1)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='aaaba')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='aaaba'),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Str(s='a{,2}?')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='aaaba')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='aaaba'),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Str(s='a{1,}?')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='aaaba')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='aaaba'),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Str(s='a{1,}?'),
                                              Num(n=1)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='aaaba')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='aaaba'),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Str(s='b{1}?')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='aaaba')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='aaaba'),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Str(s='b{1,2}?')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='aaaba')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='aaaba'),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Str(s='b{,2}?')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='aaaba')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='aaaba'),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Str(s='b{1,}?')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='aaaba')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Str(s='\nsyntax: []')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='aaaba'),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Str(s='[a-z]')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='aaaba')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='aaaba'),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Str(s='[a-z]'),
                                              Num(n=1)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='aaaba')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='aaaba'),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Str(s='[ab]')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='aaaba')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='aaaba'),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Str(s='[ab]'),
                                              Num(n=1)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='aaaba')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Str(s='\nsyntax: |')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='aaaba'),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Str(s='a|b')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='aaaba')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='aaaba'),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Str(s='a|b'),
                                              Num(n=1)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='aaaba')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Str(s='\nsyntax: (...)')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='aaaba'),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Str(s='(a)(a)(b)(a)')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='aaaba')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='aaaba'),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Str(s='(a)(a)(b)(a)'),
                                              Num(n=1)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='aaaba')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='aaaba'),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Str(s='(a{2})(.b.)')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='aaaba')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='aaaba'),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Str(s='(a{2})(.b.)'),
                                              Num(n=1)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='aaaba')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None))])
