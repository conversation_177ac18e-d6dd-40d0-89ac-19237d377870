Module(body=[Assign(targets=[Name(id='d',
                                  ctx=Store())],
                    value=Dict(keys=[Str(s='foo'),
                                     Str(s='bar')],
                               values=[Num(n=2),
                                       Num(n=3)])),
             Print(dest=None,
                   values=[Name(id='d',
                                ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='min',
                                          ctx=Load()),
                                args=[Name(id='d',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='max',
                                          ctx=Load()),
                                args=[Name(id='d',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True)])
