Module(body=[Import(names=[alias(name='re',
                                 asname=None)]),
             Assign(targets=[Name(id='pattern',
                                  ctx=Store())],
                    value=Str(s='\\n')),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Name(id='pattern',
                                           ctx=Load()),
                                      Str(s='\n')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Name(id='pattern',
                                           ctx=Load()),
                                      Str(s='\n\n')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Name(id='pattern',
                                           ctx=Load()),
                                      Str(s='x\nx')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Name(id='pattern',
                                           ctx=Load()),
                                      Str(s='x\nx\n')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Assign(targets=[Name(id='pattern',
                                  ctx=Store())],
                    value=Str(s='\\t')),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Name(id='pattern',
                                           ctx=Load()),
                                      Str(s='\t')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Name(id='pattern',
                                           ctx=Load()),
                                      Str(s='\t\t')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Name(id='pattern',
                                           ctx=Load()),
                                      Str(s='x\tx')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Name(id='pattern',
                                           ctx=Load()),
                                      Str(s='x\tx\t')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True)])
