Sym_type: module
Sym_name: top
Sym_lineno: 0
Sym_nested: False
Sym_haschildren: True
-- Identifiers --
name: test
  is_referenced: True
  is_imported: False
  is_parameter: False
  is_global: False
  is_declared_global: False
  is_local: True
  is_free: False
  is_assigned: True
  is_namespace: True
  namespaces: [
    Sym_type: function
    Sym_name: test
    Sym_lineno: 1
    Sym_nested: False
    Sym_haschildren: False
    Func_params: ['a', 'b']
    Func_locals: ['a', 'b']
    Func_globals: []
    Func_frees: []
    -- Identifiers --
    name: a
      is_referenced: True
      is_imported: False
      is_parameter: True
      is_global: False
      is_declared_global: False
      is_local: True
      is_free: False
      is_assigned: False
      is_namespace: False
      namespaces: [
      ]
    name: b
      is_referenced: True
      is_imported: False
      is_parameter: True
      is_global: False
      is_declared_global: False
      is_local: True
      is_free: False
      is_assigned: False
      is_namespace: False
      namespaces: [
      ]
  ]
