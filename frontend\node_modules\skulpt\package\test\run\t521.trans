Module(body=[Assign(targets=[Name(id='x',
                                  ctx=Store())],
                    value=Tuple(elts=[Num(n=1),
                                      Num(n=2),
                                      Num(n=3)],
                                ctx=Load())),
             Print(dest=None,
                   values=[Call(func=Name(id='hash',
                                          ctx=Load()),
                                args=[Name(id='x',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Call(func=Name(id='type',
                                          ctx=Load()),
                                args=[Call(func=Name(id='hash',
                                                     ctx=Load()),
                                           args=[Name(id='x',
                                                      ctx=Load())],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True)])
