Module(body=[ClassDef(name='Silly',
                      bases=[],
                      body=[FunctionDef(name='__init__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param()),
                                                             Name(id='x',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Assign(targets=[Attribute(value=Name(id='self',
                                                                                   ctx=Load()),
                                                                        attr='h',
                                                                        ctx=Store())],
                                                     value=Name(id='x',
                                                                ctx=Load()))],
                                        decorator_list=[]),
                            FunctionDef(name='__hash__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Return(value=Num(n=3))],
                                        decorator_list=[]),
                            FunctionDef(name='__str__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Return(value=Call(func=Name(id='str',
                                                                          ctx=Load()),
                                                                args=[Attribute(value=Name(id='self',
                                                                                           ctx=Load()),
                                                                                attr='h',
                                                                                ctx=Load())],
                                                                keywords=[],
                                                                starargs=None,
                                                                kwargs=None))],
                                        decorator_list=[])],
                      decorator_list=[]),
             Assign(targets=[Name(id='a',
                                  ctx=Store())],
                    value=Call(func=Name(id='Silly',
                                         ctx=Load()),
                               args=[Num(n=1)],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Assign(targets=[Name(id='b',
                                  ctx=Store())],
                    value=Call(func=Name(id='Silly',
                                         ctx=Load()),
                               args=[Num(n=2)],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Assign(targets=[Name(id='c',
                                  ctx=Store())],
                    value=Call(func=Name(id='Silly',
                                         ctx=Load()),
                               args=[Num(n=3)],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Call(func=Name(id='hash',
                                          ctx=Load()),
                                args=[Name(id='a',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='hash',
                                          ctx=Load()),
                                args=[Name(id='b',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='hash',
                                          ctx=Load()),
                                args=[Name(id='c',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True)])
