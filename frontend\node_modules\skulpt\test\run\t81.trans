Module(body=[FunctionDef(name='test',
                         args=arguments(args=[],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[]),
                         body=[FunctionDef(name='fnc',
                                           args=arguments(args=[],
                                                          vararg=None,
                                                          kwarg=None,
                                                          defaults=[]),
                                           body=[Print(dest=None,
                                                       values=[Str(s='OK')],
                                                       nl=True)],
                                           decorator_list=[]),
                               Expr(value=Call(func=Name(id='fnc',
                                                         ctx=Load()),
                                               args=[],
                                               keywords=[],
                                               starargs=None,
                                               kwargs=None))],
                         decorator_list=[]),
             Expr(value=Call(func=Name(id='test',
                                       ctx=Load()),
                             args=[],
                             keywords=[],
                             starargs=None,
                             kwargs=None))])
