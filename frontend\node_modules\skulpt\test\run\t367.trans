Module(body=[Assign(targets=[Name(id='x',
                                  ctx=Store())],
                    value=BinOp(left=BinOp(left=Num(n=1),
                                           op=LShift(),
                                           right=Num(n=64)),
                                op=Add(),
                                right=Num(n=1))),
             Print(dest=None,
                   values=[Name(id='x',
                                ctx=Load())],
                   nl=True),
             AugAssign(target=Name(id='x',
                                   ctx=Store()),
                       op=RShift(),
                       value=Num(n=3)),
             Print(dest=None,
                   values=[Name(id='x',
                                ctx=Load())],
                   nl=True),
             Assign(targets=[Name(id='y',
                                  ctx=Store())],
                    value=BinOp(left=Num(n=1),
                                op=LShift(),
                                right=Num(n=64))),
             Print(dest=None,
                   values=[Name(id='y',
                                ctx=Load())],
                   nl=True),
             AugAssign(target=Name(id='y',
                                   ctx=Store()),
                       op=Add(),
                       value=Num(n=1)),
             Print(dest=None,
                   values=[Name(id='y',
                                ctx=Load())],
                   nl=True),
             AugAssign(target=Name(id='y',
                                   ctx=Store()),
                       op=BitAnd(),
                       value=Num(n=1)),
             Print(dest=None,
                   values=[Name(id='y',
                                ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[BinOp(left=BinOp(left=BinOp(left=Num(n=1),
                                                       op=LShift(),
                                                       right=Num(n=64)),
                                            op=Add(),
                                            right=Num(n=1)),
                                 op=BitAnd(),
                                 right=Num(n=1))],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=BinOp(left=BinOp(left=BinOp(left=Num(n=1),
                                                                    op=LShift(),
                                                                    right=Num(n=64)),
                                                         op=Add(),
                                                         right=Num(n=1)),
                                              op=BitAnd(),
                                              right=Num(n=1)),
                                   ops=[Eq()],
                                   comparators=[Num(n=0)])],
                   nl=True)])
