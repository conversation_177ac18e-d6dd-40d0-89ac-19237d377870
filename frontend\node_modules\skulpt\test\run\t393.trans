Module(body=[Print(dest=None,
                   values=[Call(func=Attribute(value=Str(s='a bc d e'),
                                               attr='split',
                                               ctx=Load()),
                                args=[Str(s=' '),
                                      Num(n=2)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Str(s='a b c'),
                                               attr='split',
                                               ctx=Load()),
                                args=[],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Str(s='abc'),
                                               attr='split',
                                               ctx=Load()),
                                args=[Str(s='')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True)])
