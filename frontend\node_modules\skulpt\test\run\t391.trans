Module(body=[Assign(targets=[Name(id='l',
                                  ctx=Store())],
                    value=Call(func=Name(id='range',
                                         ctx=Load()),
                               args=[Num(n=9)],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Subscript(value=Name(id='l',
                                                ctx=Load()),
                                     slice=Slice(lower=Num(n=-10),
                                                 upper=None,
                                                 step=None),
                                     ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Subscript(value=Name(id='l',
                                                ctx=Load()),
                                     slice=Slice(lower=Num(n=8),
                                                 upper=Num(n=-10),
                                                 step=Num(n=-1)),
                                     ctx=Load())],
                   nl=True),
             Assign(targets=[Name(id='s',
                                  ctx=Store())],
                    value=Str(s='01234')),
             Print(dest=None,
                   values=[Subscript(value=Name(id='s',
                                                ctx=Load()),
                                     slice=Slice(lower=Num(n=-6),
                                                 upper=Num(n=0),
                                                 step=None),
                                     ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Subscript(value=Name(id='s',
                                                ctx=Load()),
                                     slice=Slice(lower=Num(n=-6),
                                                 upper=None,
                                                 step=None),
                                     ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Subscript(value=Name(id='s',
                                                ctx=Load()),
                                     slice=Slice(lower=Num(n=-6),
                                                 upper=Num(n=-3),
                                                 step=None),
                                     ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Subscript(value=Name(id='s',
                                                ctx=Load()),
                                     slice=Slice(lower=Num(n=-6),
                                                 upper=Num(n=20),
                                                 step=None),
                                     ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Subscript(value=Name(id='l',
                                                ctx=Load()),
                                     slice=Index(value=Num(n=4)),
                                     ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Subscript(value=Name(id='l',
                                                ctx=Load()),
                                     slice=Index(value=Num(n=-1)),
                                     ctx=Load())],
                   nl=True),
             Assign(targets=[Subscript(value=Name(id='l',
                                                  ctx=Load()),
                                       slice=Index(value=Name(id='None',
                                                              ctx=Load())),
                                       ctx=Store())],
                    value=Num(n=4))])
