Module(body=[Import(names=[alias(name='math',
                                 asname=None)]),
             Print(dest=None,
                   values=[Str(s='integers')],
                   nl=True),
             Print(dest=None,
                   values=[BinOp(left=Num(n=1),
                                 op=Add(),
                                 right=Name(id='True',
                                            ctx=Load()))],
                   nl=True),
             Print(dest=None,
                   values=[BinOp(left=Num(n=2),
                                 op=Sub(),
                                 right=Name(id='True',
                                            ctx=Load()))],
                   nl=True),
             Print(dest=None,
                   values=[BinOp(left=Num(n=3),
                                 op=Mult(),
                                 right=Name(id='False',
                                            ctx=Load()))],
                   nl=True),
             Print(dest=None,
                   values=[BinOp(left=Num(n=4),
                                 op=Div(),
                                 right=Name(id='True',
                                            ctx=Load()))],
                   nl=True),
             Print(dest=None,
                   values=[BinOp(left=Num(n=5),
                                 op=Mod(),
                                 right=Name(id='True',
                                            ctx=Load()))],
                   nl=True),
             Print(dest=None,
                   values=[BinOp(left=Num(n=6),
                                 op=Pow(),
                                 right=Name(id='False',
                                            ctx=Load()))],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='floats')],
                   nl=True),
             Print(dest=None,
                   values=[BinOp(left=Num(n=1.5),
                                 op=Add(),
                                 right=Name(id='True',
                                            ctx=Load()))],
                   nl=True),
             Print(dest=None,
                   values=[BinOp(left=Num(n=2.5),
                                 op=Sub(),
                                 right=Name(id='True',
                                            ctx=Load()))],
                   nl=True),
             Print(dest=None,
                   values=[BinOp(left=Num(n=3.5),
                                 op=Mult(),
                                 right=Name(id='False',
                                            ctx=Load()))],
                   nl=True),
             Print(dest=None,
                   values=[BinOp(left=Num(n=4.5),
                                 op=Div(),
                                 right=Name(id='True',
                                            ctx=Load()))],
                   nl=True),
             Print(dest=None,
                   values=[BinOp(left=Num(n=5.5),
                                 op=Mod(),
                                 right=Name(id='True',
                                            ctx=Load()))],
                   nl=True),
             Print(dest=None,
                   values=[BinOp(left=Num(n=6.5),
                                 op=Pow(),
                                 right=Name(id='False',
                                            ctx=Load()))],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='longs')],
                   nl=True),
             Print(dest=None,
                   values=[BinOp(left=Num(n=1),
                                 op=Add(),
                                 right=Name(id='True',
                                            ctx=Load()))],
                   nl=True),
             Print(dest=None,
                   values=[BinOp(left=Num(n=2),
                                 op=Sub(),
                                 right=Name(id='True',
                                            ctx=Load()))],
                   nl=True),
             Print(dest=None,
                   values=[BinOp(left=Num(n=3),
                                 op=Mult(),
                                 right=Name(id='False',
                                            ctx=Load()))],
                   nl=True),
             Print(dest=None,
                   values=[BinOp(left=Num(n=4),
                                 op=Div(),
                                 right=Name(id='True',
                                            ctx=Load()))],
                   nl=True),
             Print(dest=None,
                   values=[BinOp(left=Num(n=5),
                                 op=Mod(),
                                 right=Name(id='True',
                                            ctx=Load()))],
                   nl=True),
             Print(dest=None,
                   values=[BinOp(left=Num(n=6),
                                 op=Pow(),
                                 right=Name(id='False',
                                            ctx=Load()))],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='math module')],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='fabs',
                                               ctx=Load()),
                                args=[Name(id='True',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='cos',
                                               ctx=Load()),
                                args=[Name(id='True',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True)])
