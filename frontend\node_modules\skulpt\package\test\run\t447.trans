Module(body=[Print(dest=None,
                   values=[BinOp(left=BinOp(left=Num(n=1),
                                            op=Add(),
                                            right=Num(n=3)),
                                 op=Div(),
                                 right=Num(n=2))],
                   nl=True),
             Print(dest=None,
                   values=[BinOp(left=BinOp(left=Num(n=4),
                                            op=Div(),
                                            right=Num(n=2)),
                                 op=Add(),
                                 right=Num(n=3))],
                   nl=True),
             Print(dest=None,
                   values=[BinOp(left=BinOp(left=Num(n=1),
                                            op=Add(),
                                            right=Num(n=3)),
                                 op=Div(),
                                 right=Num(n=2))],
                   nl=True),
             Print(dest=None,
                   values=[BinOp(left=BinOp(left=Num(n=1),
                                            op=Add(),
                                            right=Num(n=3)),
                                 op=Div(),
                                 right=Num(n=3))],
                   nl=True),
             Print(dest=None,
                   values=[BinOp(left=BinOp(left=Num(n=1),
                                            op=Div(),
                                            right=Num(n=2)),
                                 op=Add(),
                                 right=Num(n=1))],
                   nl=True),
             Print(dest=None,
                   values=[BinOp(left=BinOp(left=Num(n=4),
                                            op=Div(),
                                            right=Num(n=2)),
                                 op=Add(),
                                 right=Num(n=3))],
                   nl=True),
             Print(dest=None,
                   values=[BinOp(left=BinOp(left=BinOp(left=Num(n=4),
                                                       op=Pow(),
                                                       right=Num(n=2)),
                                            op=Sub(),
                                            right=Num(n=5)),
                                 op=Add(),
                                 right=BinOp(left=BinOp(left=Num(n=3),
                                                        op=Div(),
                                                        right=Num(n=7)),
                                             op=Mod(),
                                             right=Num(n=2)))],
                   nl=True),
             Print(dest=None,
                   values=[BinOp(left=BinOp(left=BinOp(left=Num(n=4),
                                                       op=Pow(),
                                                       right=Num(n=2)),
                                            op=Sub(),
                                            right=Num(n=5)),
                                 op=Add(),
                                 right=BinOp(left=BinOp(left=Num(n=3),
                                                        op=Div(),
                                                        right=Num(n=7)),
                                             op=Mod(),
                                             right=Num(n=2)))],
                   nl=True)])
