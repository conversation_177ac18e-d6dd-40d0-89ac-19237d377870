Module(body=[FunctionDef(name='enumerate_helper',
                         args=arguments(args=[Name(id='iterable',
                                                   ctx=Param()),
                                              Name(id='start',
                                                   ctx=Param())],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[Num(n=0)]),
                         body=[Assign(targets=[Name(id='x',
                                                    ctx=Store())],
                                      value=List(elts=[],
                                                 ctx=Load())),
                               For(target=Name(id='i',
                                               ctx=Store()),
                                   iter=Call(func=Name(id='enumerate',
                                                       ctx=Load()),
                                             args=[Name(id='iterable',
                                                        ctx=Load()),
                                                   Name(id='start',
                                                        ctx=Load())],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   body=[Expr(value=Call(func=Attribute(value=Name(id='x',
                                                                                   ctx=Load()),
                                                                        attr='append',
                                                                        ctx=Load()),
                                                         args=[Name(id='i',
                                                                    ctx=Load())],
                                                         keywords=[],
                                                         starargs=None,
                                                         kwargs=None))],
                                   orelse=[]),
                               Print(dest=None,
                                     values=[Name(id='x',
                                                  ctx=Load())],
                                     nl=True)],
                         decorator_list=[]),
             Expr(value=Call(func=Name(id='enumerate_helper',
                                       ctx=Load()),
                             args=[List(elts=[Num(n=1),
                                              Num(n=2),
                                              Num(n=3),
                                              Num(n=4)],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='enumerate_helper',
                                       ctx=Load()),
                             args=[List(elts=[Num(n=1),
                                              Num(n=2),
                                              Num(n=3),
                                              Num(n=4)],
                                        ctx=Load()),
                                   Num(n=10)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='enumerate_helper',
                                       ctx=Load()),
                             args=[Str(s='hello')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='enumerate_helper',
                                       ctx=Load()),
                             args=[Str(s='WORLD'),
                                   Num(n=2)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='enumerate_helper',
                                       ctx=Load()),
                             args=[Tuple(elts=[Num(n=1),
                                               Num(n=2),
                                               Num(n=3)],
                                         ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='enumerate_helper',
                                       ctx=Load()),
                             args=[Tuple(elts=[Num(n=1),
                                               Num(n=2),
                                               Num(n=3)],
                                         ctx=Load()),
                                   Num(n=-1)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='enumerate_helper',
                                       ctx=Load()),
                             args=[Dict(keys=[Num(n=1),
                                              Num(n=2),
                                              Num(n=3)],
                                        values=[Str(s='a'),
                                                Str(s='b'),
                                                Str(s='c')])],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='enumerate_helper',
                                       ctx=Load()),
                             args=[Dict(keys=[Num(n=1),
                                              Num(n=2),
                                              Num(n=3)],
                                        values=[Str(s='a'),
                                                Str(s='b'),
                                                Str(s='c')]),
                                   Num(n=5)],
                             keywords=[],
                             starargs=None,
                             kwargs=None))])
