Module(body=[FunctionDef(name='f',
                         args=arguments(args=[Name(id='x',
                                                   ctx=Param())],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[]),
                         body=[Return(value=Name(id='None',
                                                 ctx=Load()))],
                         decorator_list=[]),
             Assign(targets=[Name(id='a',
                                  ctx=Store())],
                    value=BinOp(left=Call(func=Name(id='f',
                                                    ctx=Load()),
                                          args=[Num(n=1)],
                                          keywords=[],
                                          starargs=None,
                                          kwargs=None),
                                op=Add(),
                                right=Num(n=3)))])
