Module(body=[ClassDef(name='test',
                      bases=[],
                      body=[FunctionDef(name='__hash__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Return(value=Num(n=1))],
                                        decorator_list=[])],
                      decorator_list=[]),
             Assign(targets=[Name(id='a',
                                  ctx=Store())],
                    value=Call(func=Name(id='test',
                                         ctx=Load()),
                               args=[],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Assign(targets=[Name(id='b',
                                  ctx=Store())],
                    value=Call(func=Name(id='test',
                                         ctx=Load()),
                               args=[],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Assign(targets=[Name(id='d',
                                  ctx=Store())],
                    value=Dict(keys=[Name(id='a',
                                          ctx=Load())],
                               values=[Num(n=5)])),
             Assign(targets=[Subscript(value=Name(id='d',
                                                  ctx=Load()),
                                       slice=Index(value=Name(id='b',
                                                              ctx=Load())),
                                       ctx=Store())],
                    value=Num(n=6)),
             Print(dest=None,
                   values=[Name(id='d',
                                ctx=Load())],
                   nl=True)])
