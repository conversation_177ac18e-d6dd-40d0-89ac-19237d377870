Module(body=[Assign(targets=[Name(id='l',
                                  ctx=Store())],
                    value=List(elts=[Num(n=1),
                                     Num(n=1),
                                     Num(n=2),
                                     <PERSON>um(n=3),
                                     <PERSON>um(n=5),
                                     <PERSON>um(n=8),
                                     Num(n=13),
                                     Num(n=21)],
                               ctx=Load())),
             Print(dest=None,
                   values=[Name(id='l',
                                ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='l',
                                                          ctx=Load()),
                                               attr='count',
                                               ctx=Load()),
                                args=[Num(n=1)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='l',
                                                          ctx=Load()),
                                               attr='reverse',
                                               ctx=Load()),
                                args=[],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Name(id='l',
                                ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='l',
                                                          ctx=Load()),
                                               attr='count',
                                               ctx=Load()),
                                args=[Num(n=1)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='l',
                                                          ctx=Load()),
                                               attr='count',
                                               ctx=Load()),
                                args=[Num(n=0)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='l',
                                                          ctx=Load()),
                                               attr='count',
                                               ctx=Load()),
                                args=[Num(n=3)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='l',
                                                          ctx=Load()),
                                               attr='index',
                                               ctx=Load()),
                                args=[Num(n=5)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='l',
                                                          ctx=Load()),
                                               attr='remove',
                                               ctx=Load()),
                                args=[Num(n=5)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='l',
                                                          ctx=Load()),
                                               attr='remove',
                                               ctx=Load()),
                                args=[Num(n=1)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='l',
                                                          ctx=Load()),
                                               attr='count',
                                               ctx=Load()),
                                args=[Num(n=1)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Name(id='l',
                                ctx=Load())],
                   nl=True)])
