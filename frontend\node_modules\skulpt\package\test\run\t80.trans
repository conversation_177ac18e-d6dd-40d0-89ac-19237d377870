Module(body=[FunctionDef(name='test',
                         args=arguments(args=[Name(id='x',
                                                   ctx=Param()),
                                              Name(id='y',
                                                   ctx=Param())],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[]),
                         body=[Print(dest=None,
                                     values=[Name(id='x',
                                                  ctx=Load())],
                                     nl=True),
                               Return(value=Name(id='y',
                                                 ctx=Load()))],
                         decorator_list=[]),
             Expr(value=BoolOp(op=Or(),
                               values=[Call(func=Name(id='test',
                                                      ctx=Load()),
                                            args=[Str(s='a'),
                                                  Num(n=1)],
                                            keywords=[],
                                            starargs=None,
                                            kwargs=None),
                                       BoolOp(op=And(),
                                              values=[Call(func=Name(id='test',
                                                                     ctx=Load()),
                                                           args=[Str(s='b'),
                                                                 Num(n=1)],
                                                           keywords=[],
                                                           starargs=None,
                                                           kwargs=None),
                                                      Call(func=Name(id='test',
                                                                     ctx=Load()),
                                                           args=[Str(s='c'),
                                                                 Num(n=0)],
                                                           keywords=[],
                                                           starargs=None,
                                                           kwargs=None)])]))])
