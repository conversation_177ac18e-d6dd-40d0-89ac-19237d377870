Sym_type: module
Sym_name: top
Sym_lineno: 0
Sym_nested: False
Sym_haschildren: True
-- Identifiers --
name: Foo
  is_referenced: True
  is_imported: False
  is_parameter: False
  is_global: False
  is_declared_global: False
  is_local: True
  is_free: False
  is_assigned: True
  is_namespace: True
  namespaces: [
    Sym_type: class
    Sym_name: Foo
    Sym_lineno: 1
    Sym_nested: False
    Sym_haschildren: True
    Class_methods: ['__getitem__', '__init__']
    -- Identifiers --
    name: __getitem__
      is_referenced: False
      is_imported: False
      is_parameter: False
      is_global: False
      is_declared_global: False
      is_local: True
      is_free: False
      is_assigned: True
      is_namespace: True
      namespaces: [
        Sym_type: function
        Sym_name: __getitem__
        Sym_lineno: 6
        Sym_nested: False
        Sym_haschildren: False
        Func_params: ['key', 'self']
        Func_locals: ['key', 'self']
        Func_globals: []
        Func_frees: []
        -- Identifiers --
        name: key
          is_referenced: False
          is_imported: False
          is_parameter: True
          is_global: False
          is_declared_global: False
          is_local: True
          is_free: False
          is_assigned: False
          is_namespace: False
          namespaces: [
          ]
        name: self
          is_referenced: True
          is_imported: False
          is_parameter: True
          is_global: False
          is_declared_global: False
          is_local: True
          is_free: False
          is_assigned: False
          is_namespace: False
          namespaces: [
          ]
      ]
    name: __init__
      is_referenced: False
      is_imported: False
      is_parameter: False
      is_global: False
      is_declared_global: False
      is_local: True
      is_free: False
      is_assigned: True
      is_namespace: True
      namespaces: [
        Sym_type: function
        Sym_name: __init__
        Sym_lineno: 3
        Sym_nested: False
        Sym_haschildren: False
        Func_params: ['arg', 'self']
        Func_locals: ['arg', 'self']
        Func_globals: ['None']
        Func_frees: []
        -- Identifiers --
        name: None
          is_referenced: True
          is_imported: False
          is_parameter: False
          is_global: True
          is_declared_global: False
          is_local: False
          is_free: False
          is_assigned: False
          is_namespace: False
          namespaces: [
          ]
        name: arg
          is_referenced: False
          is_imported: False
          is_parameter: True
          is_global: False
          is_declared_global: False
          is_local: True
          is_free: False
          is_assigned: False
          is_namespace: False
          namespaces: [
          ]
        name: self
          is_referenced: True
          is_imported: False
          is_parameter: True
          is_global: False
          is_declared_global: False
          is_local: True
          is_free: False
          is_assigned: False
          is_namespace: False
          namespaces: [
          ]
      ]
  ]
name: x
  is_referenced: True
  is_imported: False
  is_parameter: False
  is_global: False
  is_declared_global: False
  is_local: True
  is_free: False
  is_assigned: True
  is_namespace: False
  namespaces: [
  ]
