Module(body=[Assign(targets=[Name(id='x',
                                  ctx=Store())],
                    value=List(elts=[Str(s='hello')],
                               ctx=Load())),
             Assign(targets=[Name(id='y',
                                  ctx=Store())],
                    value=Call(func=Name(id='list',
                                         ctx=Load()),
                               args=[Name(id='x',
                                          ctx=Load())],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Assign(targets=[Subscript(value=Name(id='x',
                                                  ctx=Load()),
                                       slice=Index(value=Num(n=0)),
                                       ctx=Store())],
                    value=Str(s='hi')),
             Print(dest=None,
                   values=[Subscript(value=Name(id='y',
                                                ctx=Load()),
                                     slice=Index(value=Num(n=0)),
                                     ctx=Load())],
                   nl=True)])
