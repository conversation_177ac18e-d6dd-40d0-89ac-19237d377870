Module(body=[FunctionDef(name='test',
                         args=arguments(args=[Name(id='y',
                                                   ctx=Param()),
                                              Name(id='x',
                                                   ctx=Param())],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[Str(s='K'),
                                                  Str(s='Z')]),
                         body=[Print(dest=None,
                                     values=[BinOp(left=Name(id='x',
                                                             ctx=Load()),
                                                   op=Add(),
                                                   right=Name(id='y',
                                                              ctx=Load()))],
                                     nl=True)],
                         decorator_list=[]),
             Expr(value=Call(func=Name(id='test',
                                       ctx=Load()),
                             args=[Str(s='O')],
                             keywords=[],
                             starargs=None,
                             kwargs=None))])
