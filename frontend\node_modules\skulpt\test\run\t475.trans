Module(body=[FunctionDef(name='helper',
                         args=arguments(args=[Name(id='got',
                                                   ctx=Param()),
                                              Name(id='expect',
                                                   ctx=Param())],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[]),
                         body=[If(test=Compare(left=Name(id='got',
                                                         ctx=Load()),
                                               ops=[Eq()],
                                               comparators=[Name(id='expect',
                                                                 ctx=Load())]),
                                  body=[Print(dest=None,
                                              values=[Name(id='True',
                                                           ctx=Load())],
                                              nl=True)],
                                  orelse=[Print(dest=None,
                                                values=[Name(id='False',
                                                             ctx=Load()),
                                                        Name(id='expect',
                                                             ctx=Load()),
                                                        Name(id='got',
                                                             ctx=Load())],
                                                nl=True)])],
                         decorator_list=[]),
             ClassDef(name='Squares',
                      bases=[],
                      body=[FunctionDef(name='__init__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param()),
                                                             Name(id='max',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Assign(targets=[Attribute(value=Name(id='self',
                                                                                   ctx=Load()),
                                                                        attr='max',
                                                                        ctx=Store())],
                                                     value=Name(id='max',
                                                                ctx=Load())),
                                              Assign(targets=[Attribute(value=Name(id='self',
                                                                                   ctx=Load()),
                                                                        attr='sofar',
                                                                        ctx=Store())],
                                                     value=List(elts=[],
                                                                ctx=Load()))],
                                        decorator_list=[]),
                            FunctionDef(name='__len__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Return(value=Call(func=Name(id='len',
                                                                          ctx=Load()),
                                                                args=[Attribute(value=Name(id='self',
                                                                                           ctx=Load()),
                                                                                attr='sofar',
                                                                                ctx=Load())],
                                                                keywords=[],
                                                                starargs=None,
                                                                kwargs=None))],
                                        decorator_list=[]),
                            FunctionDef(name='__getitem__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param()),
                                                             Name(id='i',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[If(test=UnaryOp(op=Not(),
                                                              operand=Compare(left=Num(n=0),
                                                                              ops=[LtE(),
                                                                                   Lt()],
                                                                              comparators=[Name(id='i',
                                                                                                ctx=Load()),
                                                                                           Attribute(value=Name(id='self',
                                                                                                                ctx=Load()),
                                                                                                     attr='max',
                                                                                                     ctx=Load())])),
                                                 body=[Raise(type=Name(id='IndexError',
                                                                       ctx=Load()),
                                                             inst=None,
                                                             tback=None)],
                                                 orelse=[]),
                                              Assign(targets=[Name(id='n',
                                                                   ctx=Store())],
                                                     value=Call(func=Name(id='len',
                                                                          ctx=Load()),
                                                                args=[Attribute(value=Name(id='self',
                                                                                           ctx=Load()),
                                                                                attr='sofar',
                                                                                ctx=Load())],
                                                                keywords=[],
                                                                starargs=None,
                                                                kwargs=None)),
                                              While(test=Compare(left=Name(id='n',
                                                                           ctx=Load()),
                                                                 ops=[LtE()],
                                                                 comparators=[Name(id='i',
                                                                                   ctx=Load())]),
                                                    body=[Expr(value=Call(func=Attribute(value=Attribute(value=Name(id='self',
                                                                                                                    ctx=Load()),
                                                                                                         attr='sofar',
                                                                                                         ctx=Load()),
                                                                                         attr='append',
                                                                                         ctx=Load()),
                                                                          args=[BinOp(left=Name(id='n',
                                                                                                ctx=Load()),
                                                                                      op=Mult(),
                                                                                      right=Name(id='n',
                                                                                                 ctx=Load()))],
                                                                          keywords=[],
                                                                          starargs=None,
                                                                          kwargs=None)),
                                                          AugAssign(target=Name(id='n',
                                                                                ctx=Store()),
                                                                    op=Add(),
                                                                    value=Num(n=1))],
                                                    orelse=[]),
                                              Return(value=Subscript(value=Attribute(value=Name(id='self',
                                                                                                ctx=Load()),
                                                                                     attr='sofar',
                                                                                     ctx=Load()),
                                                                     slice=Index(value=Name(id='i',
                                                                                            ctx=Load())),
                                                                     ctx=Load()))],
                                        decorator_list=[])],
                      decorator_list=[]),
             ClassDef(name='Counter',
                      bases=[],
                      body=[ClassDef(name='CounterIterator',
                                     bases=[],
                                     body=[FunctionDef(name='__init__',
                                                       args=arguments(args=[Name(id='self',
                                                                                 ctx=Param()),
                                                                            Name(id='c',
                                                                                 ctx=Param())],
                                                                      vararg=None,
                                                                      kwarg=None,
                                                                      defaults=[]),
                                                       body=[Assign(targets=[Attribute(value=Name(id='self',
                                                                                                  ctx=Load()),
                                                                                       attr='count',
                                                                                       ctx=Store())],
                                                                    value=Num(n=0)),
                                                             Assign(targets=[Attribute(value=Name(id='self',
                                                                                                  ctx=Load()),
                                                                                       attr='c',
                                                                                       ctx=Store())],
                                                                    value=Name(id='c',
                                                                               ctx=Load()))],
                                                       decorator_list=[]),
                                           FunctionDef(name='next',
                                                       args=arguments(args=[Name(id='self',
                                                                                 ctx=Param())],
                                                                      vararg=None,
                                                                      kwarg=None,
                                                                      defaults=[]),
                                                       body=[AugAssign(target=Attribute(value=Name(id='self',
                                                                                                   ctx=Load()),
                                                                                        attr='count',
                                                                                        ctx=Store()),
                                                                       op=Add(),
                                                                       value=Num(n=1)),
                                                             If(test=Compare(left=Attribute(value=Name(id='self',
                                                                                                       ctx=Load()),
                                                                                            attr='count',
                                                                                            ctx=Load()),
                                                                             ops=[Lt()],
                                                                             comparators=[Attribute(value=Attribute(value=Name(id='self',
                                                                                                                               ctx=Load()),
                                                                                                                    attr='c',
                                                                                                                    ctx=Load()),
                                                                                                    attr='stop',
                                                                                                    ctx=Load())]),
                                                                body=[Return(value=Attribute(value=Name(id='self',
                                                                                                        ctx=Load()),
                                                                                             attr='count',
                                                                                             ctx=Load()))],
                                                                orelse=[]),
                                                             Raise(type=Name(id='StopIteration',
                                                                             ctx=Load()),
                                                                   inst=None,
                                                                   tback=None)],
                                                       decorator_list=[]),
                                           FunctionDef(name='__iter__',
                                                       args=arguments(args=[Name(id='self',
                                                                                 ctx=Param())],
                                                                      vararg=None,
                                                                      kwarg=None,
                                                                      defaults=[]),
                                                       body=[Return(value=Name(id='self',
                                                                               ctx=Load()))],
                                                       decorator_list=[])],
                                     decorator_list=[]),
                            FunctionDef(name='__init__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param()),
                                                             Name(id='stop',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Assign(targets=[Attribute(value=Name(id='self',
                                                                                   ctx=Load()),
                                                                        attr='count',
                                                                        ctx=Store())],
                                                     value=Num(n=0)),
                                              Assign(targets=[Attribute(value=Name(id='self',
                                                                                   ctx=Load()),
                                                                        attr='stop',
                                                                        ctx=Store())],
                                                     value=Name(id='stop',
                                                                ctx=Load()))],
                                        decorator_list=[]),
                            FunctionDef(name='__iter__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Return(value=Call(func=Attribute(value=Name(id='self',
                                                                                          ctx=Load()),
                                                                               attr='CounterIterator',
                                                                               ctx=Load()),
                                                                args=[Name(id='self',
                                                                           ctx=Load())],
                                                                keywords=[],
                                                                starargs=None,
                                                                kwargs=None))],
                                        decorator_list=[])],
                      decorator_list=[]),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Name(id='sum',
                                                  ctx=Load()),
                                        args=[List(elts=[],
                                                   ctx=Load())],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Num(n=0)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Name(id='sum',
                                                  ctx=Load()),
                                        args=[Call(func=Name(id='range',
                                                             ctx=Load()),
                                                   args=[Num(n=2),
                                                         Num(n=8)],
                                                   keywords=[],
                                                   starargs=None,
                                                   kwargs=None)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Num(n=27)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Name(id='sum',
                                                  ctx=Load()),
                                        args=[List(elts=[List(elts=[Num(n=1)],
                                                              ctx=Load()),
                                                         List(elts=[Num(n=2)],
                                                              ctx=Load()),
                                                         List(elts=[Num(n=3)],
                                                              ctx=Load())],
                                                   ctx=Load()),
                                              List(elts=[],
                                                   ctx=Load())],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Num(n=1),
                                              Num(n=2),
                                              Num(n=3)],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Name(id='sum',
                                                  ctx=Load()),
                                        args=[List(elts=[List(elts=[Num(n=1),
                                                                    Num(n=2)],
                                                              ctx=Load()),
                                                         List(elts=[Num(n=3),
                                                                    Num(n=4)],
                                                              ctx=Load())],
                                                   ctx=Load()),
                                              List(elts=[Num(n=5),
                                                         Num(n=6)],
                                                   ctx=Load())],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Num(n=5),
                                              Num(n=6),
                                              Num(n=1),
                                              Num(n=2),
                                              Num(n=3),
                                              Num(n=4)],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Name(id='sum',
                                                  ctx=Load()),
                                        args=[Tuple(elts=[Tuple(elts=[Num(n=1),
                                                                      Num(n=2),
                                                                      Num(n=3)],
                                                                ctx=Load()),
                                                          Tuple(elts=[Num(n=4),
                                                                      Num(n=5)],
                                                                ctx=Load())],
                                                    ctx=Load()),
                                              Tuple(elts=[Num(n=6),
                                                          Num(n=7)],
                                                    ctx=Load())],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Tuple(elts=[Num(n=6),
                                               Num(n=7),
                                               Num(n=1),
                                               Num(n=2),
                                               Num(n=3),
                                               Num(n=4),
                                               Num(n=5)],
                                         ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Name(id='sum',
                                                  ctx=Load()),
                                        args=[Call(func=Name(id='Counter',
                                                             ctx=Load()),
                                                   args=[Num(n=10)],
                                                   keywords=[],
                                                   starargs=None,
                                                   kwargs=None),
                                              Num(n=5)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Num(n=50)],
                             keywords=[],
                             starargs=None,
                             kwargs=None))])
