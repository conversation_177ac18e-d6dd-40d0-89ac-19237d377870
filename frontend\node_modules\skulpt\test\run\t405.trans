Module(body=[Assign(targets=[Name(id='correct',
                                  ctx=Store())],
                    value=Name(id='True',
                               ctx=Load())),
             For(target=Name(id='x',
                             ctx=Store()),
                 iter=Call(func=Name(id='range',
                                     ctx=Load()),
                           args=[Num(n=256)],
                           keywords=[],
                           starargs=None,
                           kwargs=None),
                 body=[If(test=Compare(left=Name(id='x',
                                                 ctx=Load()),
                                       ops=[NotEq()],
                                       comparators=[Call(func=Name(id='ord',
                                                                   ctx=Load()),
                                                         args=[Call(func=Name(id='chr',
                                                                              ctx=Load()),
                                                                    args=[Name(id='x',
                                                                               ctx=Load())],
                                                                    keywords=[],
                                                                    starargs=None,
                                                                    kwargs=None)],
                                                         keywords=[],
                                                         starargs=None,
                                                         kwargs=None)]),
                          body=[Print(dest=None,
                                      values=[Name(id='x',
                                                   ctx=Load())],
                                      nl=True),
                                Assign(targets=[Name(id='correct',
                                                     ctx=Store())],
                                       value=Name(id='False',
                                                  ctx=Load()))],
                          orelse=[])],
                 orelse=[]),
             Print(dest=None,
                   values=[Str(s='chr and ord are inverses: '),
                           Name(id='correct',
                                ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='chr',
                                          ctx=Load()),
                                args=[Num(n=97)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Name(id='chr',
                                                       ctx=Load()),
                                             args=[Num(n=97)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Str(s='a')])],
                   nl=True)])
