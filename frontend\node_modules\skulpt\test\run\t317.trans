Module(body=[ClassDef(name='Point',
                      bases=[],
                      body=[FunctionDef(name='__init__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param()),
                                                             Name(id='initX',
                                                                  ctx=Param()),
                                                             Name(id='initY',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Assign(targets=[Attribute(value=Name(id='self',
                                                                                   ctx=Load()),
                                                                        attr='x',
                                                                        ctx=Store())],
                                                     value=Name(id='initX',
                                                                ctx=Load())),
                                              Assign(targets=[Attribute(value=Name(id='self',
                                                                                   ctx=Load()),
                                                                        attr='y',
                                                                        ctx=Store())],
                                                     value=Name(id='initY',
                                                                ctx=Load()))],
                                        decorator_list=[]),
                            FunctionDef(name='__str__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Return(value=BinOp(left=BinOp(left=Call(func=Name(id='str',
                                                                                                ctx=Load()),
                                                                                      args=[Attribute(value=Name(id='self',
                                                                                                                 ctx=Load()),
                                                                                                      attr='x',
                                                                                                      ctx=Load())],
                                                                                      keywords=[],
                                                                                      starargs=None,
                                                                                      kwargs=None),
                                                                            op=Add(),
                                                                            right=Str(s=',')),
                                                                 op=Add(),
                                                                 right=Call(func=Name(id='str',
                                                                                      ctx=Load()),
                                                                            args=[Attribute(value=Name(id='self',
                                                                                                       ctx=Load()),
                                                                                            attr='y',
                                                                                            ctx=Load())],
                                                                            keywords=[],
                                                                            starargs=None,
                                                                            kwargs=None)))],
                                        decorator_list=[])],
                      decorator_list=[]),
             Assign(targets=[Name(id='p',
                                  ctx=Store())],
                    value=Call(func=Name(id='Point',
                                         ctx=Load()),
                               args=[Num(n=1),
                                     Num(n=2)],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Name(id='p',
                                ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='str',
                                          ctx=Load()),
                                args=[Name(id='p',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True)])
