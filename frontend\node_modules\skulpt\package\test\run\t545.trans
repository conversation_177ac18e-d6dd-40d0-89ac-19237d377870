Module(body=[ImportFrom(module='time',
                        names=[alias(name='sleep',
                                     asname=None)],
                        level=0),
             FunctionDef(name='sleepFiveTimes',
                         args=arguments(args=[Name(id='param',
                                                   ctx=Param())],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[]),
                         body=[Assign(targets=[Name(id='v',
                                                    ctx=Store())],
                                      value=Num(n=0)),
                               For(target=Name(id='i',
                                               ctx=Store()),
                                   iter=Call(func=Name(id='range',
                                                       ctx=Load()),
                                             args=[Num(n=5)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   body=[Expr(value=Call(func=Name(id='sleep',
                                                                   ctx=Load()),
                                                         args=[Num(n=0.01)],
                                                         keywords=[],
                                                         starargs=None,
                                                         kwargs=None)),
                                         Expr(value=Yield(value=Name(id='v',
                                                                     ctx=Load()))),
                                         AugAssign(target=Name(id='v',
                                                               ctx=Store()),
                                                   op=Add(),
                                                   value=Name(id='param',
                                                              ctx=Load()))],
                                   orelse=[])],
                         decorator_list=[]),
             Assign(targets=[Name(id='gen',
                                  ctx=Store())],
                    value=Call(func=Name(id='sleepFiveTimes',
                                         ctx=Load()),
                               args=[Num(n=5)],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='gen',
                                                          ctx=Load()),
                                               attr='next',
                                               ctx=Load()),
                                args=[],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             For(target=Name(id='v',
                             ctx=Store()),
                 iter=Name(id='gen',
                           ctx=Load()),
                 body=[Print(dest=None,
                             values=[Name(id='v',
                                          ctx=Load())],
                             nl=True)],
                 orelse=[])])
