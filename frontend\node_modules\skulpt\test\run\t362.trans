Module(body=[FunctionDef(name='f',
                         args=arguments(args=[Name(id='x',
                                                   ctx=Param())],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[]),
                         body=[Print(dest=None,
                                     values=[BinOp(left=Str(s='f(%s) called'),
                                                   op=Mod(),
                                                   right=Name(id='x',
                                                              ctx=Load()))],
                                     nl=True),
                               Return(value=Name(id='True',
                                                 ctx=Load()))],
                         decorator_list=[]),
             FunctionDef(name='g',
                         args=arguments(args=[],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[]),
                         body=[Assign(targets=[Name(id='l',
                                                    ctx=Store())],
                                      value=List(elts=[],
                                                 ctx=Load())),
                               If(test=Call(func=Name(id='f',
                                                      ctx=Load()),
                                            args=[Num(n=3)],
                                            keywords=[],
                                            starargs=None,
                                            kwargs=None),
                                  body=[Expr(value=Call(func=Attribute(value=Name(id='l',
                                                                                  ctx=Load()),
                                                                       attr='append',
                                                                       ctx=Load()),
                                                        args=[Num(n=3)],
                                                        keywords=[],
                                                        starargs=None,
                                                        kwargs=None))],
                                  orelse=[]),
                               Print(dest=None,
                                     values=[Name(id='l',
                                                  ctx=Load())],
                                     nl=True)],
                         decorator_list=[]),
             Expr(value=Call(func=Name(id='g',
                                       ctx=Load()),
                             args=[],
                             keywords=[],
                             starargs=None,
                             kwargs=None))])
