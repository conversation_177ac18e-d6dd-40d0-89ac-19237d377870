Module(body=[ClassDef(name='C',
                      bases=[],
                      body=[TryExcept(body=[Raise(type=Call(func=Name(id='Exception',
                                                                      ctx=Load()),
                                                            args=[Str(s='Oops')],
                                                            keywords=[],
                                                            starargs=None,
                                                            kwargs=None),
                                                  inst=None,
                                                  tback=None)],
                                      handlers=[ExceptHandler(type=None,
                                                              name=None,
                                                              body=[Print(dest=None,
                                                                          values=[Str(s='Caught')],
                                                                          nl=True)])],
                                      orelse=[])],
                      decorator_list=[])])
