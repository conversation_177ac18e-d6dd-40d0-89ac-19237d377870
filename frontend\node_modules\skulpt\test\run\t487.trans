Module(body=[Expr(value=Call(func=Name(id='map',
                                       ctx=Load()),
                             args=[Lambda(args=arguments(args=[Name(id='x',
                                                                    ctx=Param()),
                                                               Name(id='y',
                                                                    ctx=Param())],
                                                         vararg=None,
                                                         kwarg=None,
                                                         defaults=[]),
                                          body=BinOp(left=Name(id='x',
                                                               ctx=Load()),
                                                     op=Add(),
                                                     right=Name(id='y',
                                                                ctx=Load()))),
                                   List(elts=[Num(n=0),
                                              Num(n=1),
                                              Num(n=2),
                                              Num(n=3),
                                              Num(n=4),
                                              Num(n=5)],
                                        ctx=Load()),
                                   Name(id='False',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None))])
