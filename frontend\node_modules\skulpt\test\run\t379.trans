Module(body=[Assign(targets=[Name(id='things',
                                  ctx=Store())],
                    value=List(elts=[Str(s='hi'),
                                     Str(s='a'),
                                     Str(s='b'),
                                     Str(s='c')],
                               ctx=Load())),
             Expr(value=Call(func=Attribute(value=Name(id='things',
                                                       ctx=Load()),
                                            attr='insert',
                                            ctx=Load()),
                             args=[Call(func=Name(id='len',
                                                  ctx=Load()),
                                        args=[Name(id='things',
                                                   ctx=Load())],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='bye')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Name(id='things',
                                ctx=Load())],
                   nl=True),
             Expr(value=Call(func=Attribute(value=Name(id='things',
                                                       ctx=Load()),
                                            attr='insert',
                                            ctx=Load()),
                             args=[BinOp(left=Call(func=Name(id='len',
                                                             ctx=Load()),
                                                   args=[Name(id='things',
                                                              ctx=Load())],
                                                   keywords=[],
                                                   starargs=None,
                                                   kwargs=None),
                                         op=Add(),
                                         right=Num(n=3)),
                                   Str(s='surpise')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Name(id='things',
                                ctx=Load())],
                   nl=True)])
