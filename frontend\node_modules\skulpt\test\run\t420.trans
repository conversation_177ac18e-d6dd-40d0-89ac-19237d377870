Module(body=[Print(dest=None,
                   values=[Call(func=Name(id='range',
                                          ctx=Load()),
                                args=[Num(n=10)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='range',
                                          ctx=Load()),
                                args=[Num(n=1),
                                      Num(n=10)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='range',
                                          ctx=Load()),
                                args=[Num(n=0),
                                      Num(n=10),
                                      Num(n=2)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='range',
                                          ctx=Load()),
                                args=[Num(n=0),
                                      Num(n=-10),
                                      Num(n=-1)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='range',
                                          ctx=Load()),
                                args=[Num(n=0),
                                      Num(n=-10),
                                      Num(n=2)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='range',
                                          ctx=Load()),
                                args=[Num(n=-10),
                                      Num(n=0),
                                      Num(n=2)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True)])
