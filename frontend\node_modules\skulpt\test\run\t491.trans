Module(body=[ClassDef(name='f',
                      bases=[Name(id='object',
                                  ctx=Load())],
                      body=[FunctionDef(name='__init__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param()),
                                                             Name(id='w',
                                                                  ctx=Param()),
                                                             Name(id='x',
                                                                  ctx=Param()),
                                                             Name(id='y',
                                                                  ctx=Param()),
                                                             Name(id='z',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[Num(n=1),
                                                                 Num(n=2),
                                                                 Num(n=3)]),
                                        body=[Print(dest=None,
                                                    values=[Name(id='w',
                                                                 ctx=Load()),
                                                            Name(id='x',
                                                                 ctx=Load()),
                                                            Name(id='y',
                                                                 ctx=Load()),
                                                            Name(id='z',
                                                                 ctx=Load())],
                                                    nl=True)],
                                        decorator_list=[])],
                      decorator_list=[]),
             Assign(targets=[Name(id='z',
                                  ctx=Store())],
                    value=Str(s='x')),
             Expr(value=Call(func=Name(id='f',
                                       ctx=Load()),
                             args=[Str(s='a')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='f',
                                       ctx=Load()),
                             args=[Str(s='a'),
                                   Num(n=3)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='f',
                                       ctx=Load()),
                             args=[Str(s='a'),
                                   Num(n=3)],
                             keywords=[keyword(arg='y',
                                               value=Str(s='z'))],
                             starargs=None,
                             kwargs=None))])
