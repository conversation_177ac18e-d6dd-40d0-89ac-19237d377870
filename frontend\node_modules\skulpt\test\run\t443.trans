Module(body=[Expr(value=Str(s='\nAdapted from http://hg.python.org/cpython/file/936621d33c38/Lib/test/test_scope.py\n')),
             Print(dest=None,
                   values=[Str(s='\ntestSimpleNesting')],
                   nl=True),
             FunctionDef(name='make_adder',
                         args=arguments(args=[Name(id='x',
                                                   ctx=Param())],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[]),
                         body=[FunctionDef(name='adder',
                                           args=arguments(args=[Name(id='y',
                                                                     ctx=Param())],
                                                          vararg=None,
                                                          kwarg=None,
                                                          defaults=[]),
                                           body=[Return(value=BinOp(left=Name(id='x',
                                                                              ctx=Load()),
                                                                    op=Add(),
                                                                    right=Name(id='y',
                                                                               ctx=Load())))],
                                           decorator_list=[]),
                               Return(value=Name(id='adder',
                                                 ctx=Load()))],
                         decorator_list=[]),
             Assign(targets=[Name(id='inc',
                                  ctx=Store())],
                    value=Call(func=Name(id='make_adder',
                                         ctx=Load()),
                               args=[Num(n=1)],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Assign(targets=[Name(id='plus10',
                                  ctx=Store())],
                    value=Call(func=Name(id='make_adder',
                                         ctx=Load()),
                               args=[Num(n=10)],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Call(func=Name(id='inc',
                                          ctx=Load()),
                                args=[Num(n=1)],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Num(n=2),
                           Compare(left=Call(func=Name(id='inc',
                                                       ctx=Load()),
                                             args=[Num(n=1)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Num(n=2)])],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='inc',
                                          ctx=Load()),
                                args=[Num(n=-4)],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Num(n=-3),
                           Compare(left=Call(func=Name(id='inc',
                                                       ctx=Load()),
                                             args=[Num(n=-4)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Num(n=-3)])],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='plus10',
                                          ctx=Load()),
                                args=[Num(n=8)],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Num(n=18),
                           Compare(left=Call(func=Name(id='plus10',
                                                       ctx=Load()),
                                             args=[Num(n=8)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Num(n=18)])],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='plus10',
                                          ctx=Load()),
                                args=[Num(n=-2)],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Num(n=8),
                           Compare(left=Call(func=Name(id='plus10',
                                                       ctx=Load()),
                                             args=[Num(n=-2)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Num(n=8)])],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\ntestSimpleAndRebinding')],
                   nl=True),
             FunctionDef(name='make_adder3',
                         args=arguments(args=[Name(id='x',
                                                   ctx=Param())],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[]),
                         body=[FunctionDef(name='adder',
                                           args=arguments(args=[Name(id='y',
                                                                     ctx=Param())],
                                                          vararg=None,
                                                          kwarg=None,
                                                          defaults=[]),
                                           body=[Return(value=BinOp(left=Name(id='x',
                                                                              ctx=Load()),
                                                                    op=Add(),
                                                                    right=Name(id='y',
                                                                               ctx=Load())))],
                                           decorator_list=[]),
                               Assign(targets=[Name(id='x',
                                                    ctx=Store())],
                                      value=BinOp(left=Name(id='x',
                                                            ctx=Load()),
                                                  op=Add(),
                                                  right=Num(n=1))),
                               Return(value=Name(id='adder',
                                                 ctx=Load()))],
                         decorator_list=[]),
             Assign(targets=[Name(id='inc',
                                  ctx=Store())],
                    value=Call(func=Name(id='make_adder3',
                                         ctx=Load()),
                               args=[Num(n=0)],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Assign(targets=[Name(id='plus10',
                                  ctx=Store())],
                    value=Call(func=Name(id='make_adder3',
                                         ctx=Load()),
                               args=[Num(n=9)],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Call(func=Name(id='inc',
                                          ctx=Load()),
                                args=[Num(n=1)],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Num(n=2),
                           Compare(left=Call(func=Name(id='inc',
                                                       ctx=Load()),
                                             args=[Num(n=1)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Num(n=2)])],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='inc',
                                          ctx=Load()),
                                args=[Num(n=-4)],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Num(n=-3),
                           Compare(left=Call(func=Name(id='inc',
                                                       ctx=Load()),
                                             args=[Num(n=-4)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Num(n=-3)])],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='plus10',
                                          ctx=Load()),
                                args=[Num(n=8)],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Num(n=18),
                           Compare(left=Call(func=Name(id='plus10',
                                                       ctx=Load()),
                                             args=[Num(n=8)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Num(n=18)])],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='plus10',
                                          ctx=Load()),
                                args=[Num(n=-2)],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Num(n=8),
                           Compare(left=Call(func=Name(id='plus10',
                                                       ctx=Load()),
                                             args=[Num(n=-2)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Num(n=8)])],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\ntestNestingGlobalNoFree')],
                   nl=True),
             FunctionDef(name='make_adder4',
                         args=arguments(args=[],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[]),
                         body=[FunctionDef(name='nest',
                                           args=arguments(args=[],
                                                          vararg=None,
                                                          kwarg=None,
                                                          defaults=[]),
                                           body=[FunctionDef(name='nest',
                                                             args=arguments(args=[],
                                                                            vararg=None,
                                                                            kwarg=None,
                                                                            defaults=[]),
                                                             body=[FunctionDef(name='adder',
                                                                               args=arguments(args=[Name(id='y',
                                                                                                         ctx=Param())],
                                                                                              vararg=None,
                                                                                              kwarg=None,
                                                                                              defaults=[]),
                                                                               body=[Return(value=BinOp(left=Name(id='global_x',
                                                                                                                  ctx=Load()),
                                                                                                        op=Add(),
                                                                                                        right=Name(id='y',
                                                                                                                   ctx=Load())))],
                                                                               decorator_list=[]),
                                                                   Return(value=Name(id='adder',
                                                                                     ctx=Load()))],
                                                             decorator_list=[]),
                                                 Return(value=Call(func=Name(id='nest',
                                                                             ctx=Load()),
                                                                   args=[],
                                                                   keywords=[],
                                                                   starargs=None,
                                                                   kwargs=None))],
                                           decorator_list=[]),
                               Return(value=Call(func=Name(id='nest',
                                                           ctx=Load()),
                                                 args=[],
                                                 keywords=[],
                                                 starargs=None,
                                                 kwargs=None))],
                         decorator_list=[]),
             Assign(targets=[Name(id='global_x',
                                  ctx=Store())],
                    value=Num(n=1)),
             Assign(targets=[Name(id='adder',
                                  ctx=Store())],
                    value=Call(func=Name(id='make_adder4',
                                         ctx=Load()),
                               args=[],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Assign(targets=[Name(id='x',
                                  ctx=Store())],
                    value=Call(func=Name(id='adder',
                                         ctx=Load()),
                               args=[Num(n=1)],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Name(id='x',
                                ctx=Load()),
                           Num(n=2),
                           Compare(left=Name(id='x',
                                             ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Num(n=2)])],
                   nl=True),
             Assign(targets=[Name(id='global_x',
                                  ctx=Store())],
                    value=Num(n=10)),
             Assign(targets=[Name(id='x',
                                  ctx=Store())],
                    value=Call(func=Name(id='adder',
                                         ctx=Load()),
                               args=[Num(n=-2)],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Name(id='x',
                                ctx=Load()),
                           Num(n=8),
                           Compare(left=Name(id='x',
                                             ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Num(n=8)])],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\ntestNestingPlusFreeRefToGlobal')],
                   nl=True),
             FunctionDef(name='make_adder6',
                         args=arguments(args=[Name(id='x',
                                                   ctx=Param())],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[]),
                         body=[Global(names=['global_nest_x']),
                               FunctionDef(name='adder',
                                           args=arguments(args=[Name(id='y',
                                                                     ctx=Param())],
                                                          vararg=None,
                                                          kwarg=None,
                                                          defaults=[]),
                                           body=[Return(value=BinOp(left=Name(id='global_nest_x',
                                                                              ctx=Load()),
                                                                    op=Add(),
                                                                    right=Name(id='y',
                                                                               ctx=Load())))],
                                           decorator_list=[]),
                               Assign(targets=[Name(id='global_nest_x',
                                                    ctx=Store())],
                                      value=Name(id='x',
                                                 ctx=Load())),
                               Return(value=Name(id='adder',
                                                 ctx=Load()))],
                         decorator_list=[]),
             Assign(targets=[Name(id='inc',
                                  ctx=Store())],
                    value=Call(func=Name(id='make_adder6',
                                         ctx=Load()),
                               args=[Num(n=1)],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Call(func=Name(id='inc',
                                          ctx=Load()),
                                args=[Num(n=1)],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Num(n=2),
                           Compare(left=Call(func=Name(id='inc',
                                                       ctx=Load()),
                                             args=[Num(n=1)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Num(n=2)])],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='inc',
                                          ctx=Load()),
                                args=[Num(n=-4)],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Num(n=-3),
                           Compare(left=Call(func=Name(id='inc',
                                                       ctx=Load()),
                                             args=[Num(n=-4)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Num(n=-3)])],
                   nl=True),
             Assign(targets=[Name(id='plus10',
                                  ctx=Store())],
                    value=Call(func=Name(id='make_adder6',
                                         ctx=Load()),
                               args=[Num(n=10)],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Call(func=Name(id='plus10',
                                          ctx=Load()),
                                args=[Num(n=8)],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Num(n=18),
                           Compare(left=Call(func=Name(id='plus10',
                                                       ctx=Load()),
                                             args=[Num(n=8)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Num(n=18)])],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='plus10',
                                          ctx=Load()),
                                args=[Num(n=-2)],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Num(n=8),
                           Compare(left=Call(func=Name(id='plus10',
                                                       ctx=Load()),
                                             args=[Num(n=-2)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Num(n=8)])],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\ntestNearestEnclosingScope')],
                   nl=True),
             FunctionDef(name='f',
                         args=arguments(args=[Name(id='x',
                                                   ctx=Param())],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[]),
                         body=[FunctionDef(name='g',
                                           args=arguments(args=[Name(id='y',
                                                                     ctx=Param())],
                                                          vararg=None,
                                                          kwarg=None,
                                                          defaults=[]),
                                           body=[Assign(targets=[Name(id='x',
                                                                      ctx=Store())],
                                                        value=Num(n=42)),
                                                 FunctionDef(name='h',
                                                             args=arguments(args=[Name(id='z',
                                                                                       ctx=Param())],
                                                                            vararg=None,
                                                                            kwarg=None,
                                                                            defaults=[]),
                                                             body=[Return(value=BinOp(left=Name(id='x',
                                                                                                ctx=Load()),
                                                                                      op=Add(),
                                                                                      right=Name(id='z',
                                                                                                 ctx=Load())))],
                                                             decorator_list=[]),
                                                 Return(value=Name(id='h',
                                                                   ctx=Load()))],
                                           decorator_list=[]),
                               Return(value=Call(func=Name(id='g',
                                                           ctx=Load()),
                                                 args=[Num(n=2)],
                                                 keywords=[],
                                                 starargs=None,
                                                 kwargs=None))],
                         decorator_list=[]),
             Assign(targets=[Name(id='test_func',
                                  ctx=Store())],
                    value=Call(func=Name(id='f',
                                         ctx=Load()),
                               args=[Num(n=10)],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Call(func=Name(id='test_func',
                                          ctx=Load()),
                                args=[Num(n=5)],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Num(n=47),
                           Compare(left=Call(func=Name(id='test_func',
                                                       ctx=Load()),
                                             args=[Num(n=5)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Num(n=47)])],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\ntestMixedFreevarsAndCellvars')],
                   nl=True),
             FunctionDef(name='identity',
                         args=arguments(args=[Name(id='x',
                                                   ctx=Param())],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[]),
                         body=[Return(value=Name(id='x',
                                                 ctx=Load()))],
                         decorator_list=[]),
             FunctionDef(name='f',
                         args=arguments(args=[Name(id='x',
                                                   ctx=Param()),
                                              Name(id='y',
                                                   ctx=Param()),
                                              Name(id='z',
                                                   ctx=Param())],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[]),
                         body=[FunctionDef(name='g',
                                           args=arguments(args=[Name(id='a',
                                                                     ctx=Param()),
                                                                Name(id='b',
                                                                     ctx=Param()),
                                                                Name(id='c',
                                                                     ctx=Param())],
                                                          vararg=None,
                                                          kwarg=None,
                                                          defaults=[]),
                                           body=[Assign(targets=[Name(id='a',
                                                                      ctx=Store())],
                                                        value=BinOp(left=Name(id='a',
                                                                              ctx=Load()),
                                                                    op=Add(),
                                                                    right=Name(id='x',
                                                                               ctx=Load()))),
                                                 FunctionDef(name='h',
                                                             args=arguments(args=[],
                                                                            vararg=None,
                                                                            kwarg=None,
                                                                            defaults=[]),
                                                             body=[Return(value=Call(func=Name(id='identity',
                                                                                               ctx=Load()),
                                                                                     args=[BinOp(left=Name(id='z',
                                                                                                           ctx=Load()),
                                                                                                 op=Mult(),
                                                                                                 right=BinOp(left=Name(id='b',
                                                                                                                       ctx=Load()),
                                                                                                             op=Add(),
                                                                                                             right=Name(id='y',
                                                                                                                        ctx=Load())))],
                                                                                     keywords=[],
                                                                                     starargs=None,
                                                                                     kwargs=None))],
                                                             decorator_list=[]),
                                                 Assign(targets=[Name(id='y',
                                                                      ctx=Store())],
                                                        value=BinOp(left=Name(id='c',
                                                                              ctx=Load()),
                                                                    op=Add(),
                                                                    right=Name(id='z',
                                                                               ctx=Load()))),
                                                 Return(value=Name(id='h',
                                                                   ctx=Load()))],
                                           decorator_list=[]),
                               Return(value=Name(id='g',
                                                 ctx=Load()))],
                         decorator_list=[]),
             Assign(targets=[Name(id='g',
                                  ctx=Store())],
                    value=Call(func=Name(id='f',
                                         ctx=Load()),
                               args=[Num(n=1),
                                     Num(n=2),
                                     Num(n=3)],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Assign(targets=[Name(id='h',
                                  ctx=Store())],
                    value=Call(func=Name(id='g',
                                         ctx=Load()),
                               args=[Num(n=2),
                                     Num(n=4),
                                     Num(n=6)],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Call(func=Name(id='h',
                                          ctx=Load()),
                                args=[],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Num(n=39),
                           Compare(left=Call(func=Name(id='h',
                                                       ctx=Load()),
                                             args=[],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Num(n=39)])],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\ntestFreeVarInMethod')],
                   nl=True),
             Assign(targets=[Name(id='method_and_var',
                                  ctx=Store())],
                    value=Str(s='var')),
             ClassDef(name='Test',
                      bases=[],
                      body=[FunctionDef(name='method_and_var',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Return(value=Str(s='method'))],
                                        decorator_list=[]),
                            FunctionDef(name='test',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Return(value=Name(id='method_and_var',
                                                                ctx=Load()))],
                                        decorator_list=[]),
                            FunctionDef(name='actual_global',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Return(value=Call(func=Name(id='str',
                                                                          ctx=Load()),
                                                                args=[Str(s='global')],
                                                                keywords=[],
                                                                starargs=None,
                                                                kwargs=None))],
                                        decorator_list=[]),
                            FunctionDef(name='str',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Return(value=Call(func=Name(id='str',
                                                                          ctx=Load()),
                                                                args=[Name(id='self',
                                                                           ctx=Load())],
                                                                keywords=[],
                                                                starargs=None,
                                                                kwargs=None))],
                                        decorator_list=[])],
                      decorator_list=[]),
             Assign(targets=[Name(id='t',
                                  ctx=Store())],
                    value=Call(func=Name(id='Test',
                                         ctx=Load()),
                               args=[],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='t',
                                                          ctx=Load()),
                                               attr='test',
                                               ctx=Load()),
                                args=[],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Str(s='var'),
                           Compare(left=Call(func=Attribute(value=Name(id='t',
                                                                       ctx=Load()),
                                                            attr='test',
                                                            ctx=Load()),
                                             args=[],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Str(s='var')])],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='t',
                                                          ctx=Load()),
                                               attr='method_and_var',
                                               ctx=Load()),
                                args=[],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Str(s='method'),
                           Compare(left=Call(func=Attribute(value=Name(id='t',
                                                                       ctx=Load()),
                                                            attr='method_and_var',
                                                            ctx=Load()),
                                             args=[],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Str(s='method')])],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='t',
                                                          ctx=Load()),
                                               attr='actual_global',
                                               ctx=Load()),
                                args=[],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Str(s='global'),
                           Compare(left=Call(func=Attribute(value=Name(id='t',
                                                                       ctx=Load()),
                                                            attr='actual_global',
                                                            ctx=Load()),
                                             args=[],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Str(s='global')])],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\ntestRecursion')],
                   nl=True),
             FunctionDef(name='f',
                         args=arguments(args=[Name(id='x',
                                                   ctx=Param())],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[]),
                         body=[FunctionDef(name='fact',
                                           args=arguments(args=[Name(id='n',
                                                                     ctx=Param())],
                                                          vararg=None,
                                                          kwarg=None,
                                                          defaults=[]),
                                           body=[If(test=Compare(left=Name(id='n',
                                                                           ctx=Load()),
                                                                 ops=[Eq()],
                                                                 comparators=[Num(n=0)]),
                                                    body=[Return(value=Num(n=1))],
                                                    orelse=[Return(value=BinOp(left=Name(id='n',
                                                                                         ctx=Load()),
                                                                               op=Mult(),
                                                                               right=Call(func=Name(id='fact',
                                                                                                    ctx=Load()),
                                                                                          args=[BinOp(left=Name(id='n',
                                                                                                                ctx=Load()),
                                                                                                      op=Sub(),
                                                                                                      right=Num(n=1))],
                                                                                          keywords=[],
                                                                                          starargs=None,
                                                                                          kwargs=None)))])],
                                           decorator_list=[]),
                               If(test=Compare(left=Name(id='x',
                                                         ctx=Load()),
                                               ops=[GtE()],
                                               comparators=[Num(n=0)]),
                                  body=[Return(value=Call(func=Name(id='fact',
                                                                    ctx=Load()),
                                                          args=[Name(id='x',
                                                                     ctx=Load())],
                                                          keywords=[],
                                                          starargs=None,
                                                          kwargs=None))],
                                  orelse=[Raise(type=Name(id='ValueError',
                                                          ctx=Load()),
                                                inst=Str(s='x must be >=0'),
                                                tback=None)])],
                         decorator_list=[]),
             Print(dest=None,
                   values=[Call(func=Name(id='f',
                                          ctx=Load()),
                                args=[Num(n=6)],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Num(n=720),
                           Compare(left=Call(func=Name(id='f',
                                                       ctx=Load()),
                                             args=[Num(n=6)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Num(n=720)])],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\ntestLambdas')],
                   nl=True),
             Assign(targets=[Name(id='f1',
                                  ctx=Store())],
                    value=Lambda(args=arguments(args=[Name(id='x',
                                                           ctx=Param())],
                                                vararg=None,
                                                kwarg=None,
                                                defaults=[]),
                                 body=Lambda(args=arguments(args=[Name(id='y',
                                                                       ctx=Param())],
                                                            vararg=None,
                                                            kwarg=None,
                                                            defaults=[]),
                                             body=BinOp(left=Name(id='x',
                                                                  ctx=Load()),
                                                        op=Add(),
                                                        right=Name(id='y',
                                                                   ctx=Load()))))),
             Assign(targets=[Name(id='inc',
                                  ctx=Store())],
                    value=Call(func=Name(id='f1',
                                         ctx=Load()),
                               args=[Num(n=1)],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Assign(targets=[Name(id='plus10',
                                  ctx=Store())],
                    value=Call(func=Name(id='f1',
                                         ctx=Load()),
                               args=[Num(n=10)],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Call(func=Name(id='inc',
                                          ctx=Load()),
                                args=[Num(n=1)],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Num(n=2),
                           Compare(left=Call(func=Name(id='inc',
                                                       ctx=Load()),
                                             args=[Num(n=1)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Num(n=2)])],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='inc',
                                          ctx=Load()),
                                args=[Num(n=-4)],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Num(n=-3),
                           Compare(left=Call(func=Name(id='inc',
                                                       ctx=Load()),
                                             args=[Num(n=-4)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Num(n=-3)])],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='plus10',
                                          ctx=Load()),
                                args=[Num(n=8)],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Num(n=18),
                           Compare(left=Call(func=Name(id='plus10',
                                                       ctx=Load()),
                                             args=[Num(n=8)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Num(n=18)])],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='plus10',
                                          ctx=Load()),
                                args=[Num(n=-2)],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Num(n=8),
                           Compare(left=Call(func=Name(id='plus10',
                                                       ctx=Load()),
                                             args=[Num(n=-2)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Num(n=8)])],
                   nl=True),
             Assign(targets=[Name(id='f3',
                                  ctx=Store())],
                    value=Lambda(args=arguments(args=[Name(id='x',
                                                           ctx=Param())],
                                                vararg=None,
                                                kwarg=None,
                                                defaults=[]),
                                 body=Lambda(args=arguments(args=[Name(id='y',
                                                                       ctx=Param())],
                                                            vararg=None,
                                                            kwarg=None,
                                                            defaults=[]),
                                             body=BinOp(left=Name(id='global_x',
                                                                  ctx=Load()),
                                                        op=Add(),
                                                        right=Name(id='y',
                                                                   ctx=Load()))))),
             Assign(targets=[Name(id='global_x',
                                  ctx=Store())],
                    value=Num(n=1)),
             Assign(targets=[Name(id='inc',
                                  ctx=Store())],
                    value=Call(func=Name(id='f3',
                                         ctx=Load()),
                               args=[Name(id='None',
                                          ctx=Load())],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Call(func=Name(id='inc',
                                          ctx=Load()),
                                args=[Num(n=2)],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Num(n=3),
                           Compare(left=Call(func=Name(id='inc',
                                                       ctx=Load()),
                                             args=[Num(n=2)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Num(n=3)])],
                   nl=True)])
