Module(body=[Print(dest=None,
                   values=[Str(s='\nintegers')],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Name(id='hash',
                                                       ctx=Load()),
                                             args=[Call(func=Name(id='int',
                                                                  ctx=Load()),
                                                        args=[],
                                                        keywords=[],
                                                        starargs=None,
                                                        kwargs=None)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Call(func=Name(id='hash',
                                                               ctx=Load()),
                                                     args=[Call(func=Name(id='int',
                                                                          ctx=Load()),
                                                                args=[],
                                                                keywords=[],
                                                                starargs=None,
                                                                kwargs=None)],
                                                     keywords=[],
                                                     starargs=None,
                                                     kwargs=None)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Name(id='hash',
                                                       ctx=Load()),
                                             args=[Num(n=332)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Call(func=Name(id='hash',
                                                               ctx=Load()),
                                                     args=[Num(n=332)],
                                                     keywords=[],
                                                     starargs=None,
                                                     kwargs=None)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Name(id='hash',
                                                       ctx=Load()),
                                             args=[Num(n=-47)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Call(func=Name(id='hash',
                                                               ctx=Load()),
                                                     args=[Num(n=-47)],
                                                     keywords=[],
                                                     starargs=None,
                                                     kwargs=None)])],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\nlong integers')],
                   nl=True),
             Assign(targets=[Name(id='big',
                                  ctx=Store())],
                    value=Num(n=123456789123456789123456789123456789123456789)),
             Print(dest=None,
                   values=[Compare(left=Call(func=Name(id='hash',
                                                       ctx=Load()),
                                             args=[Call(func=Name(id='long',
                                                                  ctx=Load()),
                                                        args=[],
                                                        keywords=[],
                                                        starargs=None,
                                                        kwargs=None)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Call(func=Name(id='hash',
                                                               ctx=Load()),
                                                     args=[Call(func=Name(id='long',
                                                                          ctx=Load()),
                                                                args=[],
                                                                keywords=[],
                                                                starargs=None,
                                                                kwargs=None)],
                                                     keywords=[],
                                                     starargs=None,
                                                     kwargs=None)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Name(id='hash',
                                                       ctx=Load()),
                                             args=[Num(n=12)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Call(func=Name(id='hash',
                                                               ctx=Load()),
                                                     args=[Num(n=12)],
                                                     keywords=[],
                                                     starargs=None,
                                                     kwargs=None)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Name(id='hash',
                                                       ctx=Load()),
                                             args=[Name(id='big',
                                                        ctx=Load())],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Call(func=Name(id='hash',
                                                               ctx=Load()),
                                                     args=[Name(id='big',
                                                                ctx=Load())],
                                                     keywords=[],
                                                     starargs=None,
                                                     kwargs=None)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Name(id='hash',
                                                       ctx=Load()),
                                             args=[UnaryOp(op=USub(),
                                                           operand=Name(id='big',
                                                                        ctx=Load()))],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Call(func=Name(id='hash',
                                                               ctx=Load()),
                                                     args=[UnaryOp(op=USub(),
                                                                   operand=Name(id='big',
                                                                                ctx=Load()))],
                                                     keywords=[],
                                                     starargs=None,
                                                     kwargs=None)])],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\nfloating points')],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Name(id='hash',
                                                       ctx=Load()),
                                             args=[Call(func=Name(id='float',
                                                                  ctx=Load()),
                                                        args=[],
                                                        keywords=[],
                                                        starargs=None,
                                                        kwargs=None)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Call(func=Name(id='hash',
                                                               ctx=Load()),
                                                     args=[Call(func=Name(id='float',
                                                                          ctx=Load()),
                                                                args=[],
                                                                keywords=[],
                                                                starargs=None,
                                                                kwargs=None)],
                                                     keywords=[],
                                                     starargs=None,
                                                     kwargs=None)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Name(id='hash',
                                                       ctx=Load()),
                                             args=[Num(n=33.2)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Call(func=Name(id='hash',
                                                               ctx=Load()),
                                                     args=[Num(n=33.2)],
                                                     keywords=[],
                                                     starargs=None,
                                                     kwargs=None)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Name(id='hash',
                                                       ctx=Load()),
                                             args=[Num(n=0.05)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Call(func=Name(id='hash',
                                                               ctx=Load()),
                                                     args=[Num(n=0.05)],
                                                     keywords=[],
                                                     starargs=None,
                                                     kwargs=None)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Name(id='hash',
                                                       ctx=Load()),
                                             args=[Num(n=-11.85)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Call(func=Name(id='hash',
                                                               ctx=Load()),
                                                     args=[Num(n=-11.85)],
                                                     keywords=[],
                                                     starargs=None,
                                                     kwargs=None)])],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\nstrings')],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Name(id='hash',
                                                       ctx=Load()),
                                             args=[Str(s='')],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Call(func=Name(id='hash',
                                                               ctx=Load()),
                                                     args=[Str(s='')],
                                                     keywords=[],
                                                     starargs=None,
                                                     kwargs=None)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Name(id='hash',
                                                       ctx=Load()),
                                             args=[Str(s='hello')],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Call(func=Name(id='hash',
                                                               ctx=Load()),
                                                     args=[Str(s='hello')],
                                                     keywords=[],
                                                     starargs=None,
                                                     kwargs=None)])],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\ntuples')],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Name(id='hash',
                                                       ctx=Load()),
                                             args=[Tuple(elts=[],
                                                         ctx=Load())],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Call(func=Name(id='hash',
                                                               ctx=Load()),
                                                     args=[Tuple(elts=[],
                                                                 ctx=Load())],
                                                     keywords=[],
                                                     starargs=None,
                                                     kwargs=None)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Name(id='hash',
                                                       ctx=Load()),
                                             args=[Tuple(elts=[Num(n=1),
                                                               Num(n=2),
                                                               Num(n=3)],
                                                         ctx=Load())],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Call(func=Name(id='hash',
                                                               ctx=Load()),
                                                     args=[Tuple(elts=[Num(n=1),
                                                                       Num(n=2),
                                                                       Num(n=3)],
                                                                 ctx=Load())],
                                                     keywords=[],
                                                     starargs=None,
                                                     kwargs=None)])],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\nintegers and floating point')],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Name(id='hash',
                                                       ctx=Load()),
                                             args=[Num(n=1)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Call(func=Name(id='hash',
                                                               ctx=Load()),
                                                     args=[Num(n=1.0)],
                                                     keywords=[],
                                                     starargs=None,
                                                     kwargs=None)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Name(id='hash',
                                                       ctx=Load()),
                                             args=[Num(n=1)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Call(func=Name(id='hash',
                                                               ctx=Load()),
                                                     args=[Num(n=1)],
                                                     keywords=[],
                                                     starargs=None,
                                                     kwargs=None)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Name(id='hash',
                                                       ctx=Load()),
                                             args=[Num(n=1.0)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Call(func=Name(id='hash',
                                                               ctx=Load()),
                                                     args=[Num(n=1)],
                                                     keywords=[],
                                                     starargs=None,
                                                     kwargs=None)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Name(id='hash',
                                                       ctx=Load()),
                                             args=[Num(n=1)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq(),
                                        Eq()],
                                   comparators=[Call(func=Name(id='hash',
                                                               ctx=Load()),
                                                     args=[Num(n=1)],
                                                     keywords=[],
                                                     starargs=None,
                                                     kwargs=None),
                                                Call(func=Name(id='hash',
                                                               ctx=Load()),
                                                     args=[Num(n=1.0)],
                                                     keywords=[],
                                                     starargs=None,
                                                     kwargs=None)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Name(id='hash',
                                                       ctx=Load()),
                                             args=[Num(n=-5)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq(),
                                        Eq()],
                                   comparators=[Call(func=Name(id='hash',
                                                               ctx=Load()),
                                                     args=[Num(n=-5)],
                                                     keywords=[],
                                                     starargs=None,
                                                     kwargs=None),
                                                Call(func=Name(id='hash',
                                                               ctx=Load()),
                                                     args=[Num(n=-5.0)],
                                                     keywords=[],
                                                     starargs=None,
                                                     kwargs=None)])],
                   nl=True),
             Assign(targets=[Name(id='d',
                                  ctx=Store())],
                    value=Dict(keys=[Num(n=1),
                                     Num(n=3),
                                     Num(n=-5.0)],
                               values=[Num(n=2),
                                       Num(n=4),
                                       Num(n=6)])),
             Print(dest=None,
                   values=[Compare(left=Subscript(value=Name(id='d',
                                                             ctx=Load()),
                                                  slice=Index(value=Num(n=1)),
                                                  ctx=Load()),
                                   ops=[Eq(),
                                        Eq()],
                                   comparators=[Subscript(value=Name(id='d',
                                                                     ctx=Load()),
                                                          slice=Index(value=Num(n=1.0)),
                                                          ctx=Load()),
                                                Subscript(value=Name(id='d',
                                                                     ctx=Load()),
                                                          slice=Index(value=Num(n=1)),
                                                          ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Subscript(value=Name(id='d',
                                                             ctx=Load()),
                                                  slice=Index(value=Num(n=-5)),
                                                  ctx=Load()),
                                   ops=[Eq(),
                                        Eq()],
                                   comparators=[Subscript(value=Name(id='d',
                                                                     ctx=Load()),
                                                          slice=Index(value=Num(n=-5.0)),
                                                          ctx=Load()),
                                                Subscript(value=Name(id='d',
                                                                     ctx=Load()),
                                                          slice=Index(value=Num(n=-5)),
                                                          ctx=Load())])],
                   nl=True)])
