Module(body=[FunctionDef(name='helper',
                         args=arguments(args=[Name(id='got',
                                                   ctx=Param()),
                                              Name(id='expect',
                                                   ctx=Param())],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[]),
                         body=[If(test=Compare(left=Name(id='got',
                                                         ctx=Load()),
                                               ops=[Eq()],
                                               comparators=[Name(id='expect',
                                                                 ctx=Load())]),
                                  body=[Print(dest=None,
                                              values=[Name(id='True',
                                                           ctx=Load())],
                                              nl=True)],
                                  orelse=[Print(dest=None,
                                                values=[Name(id='False',
                                                             ctx=Load()),
                                                        Name(id='expect',
                                                             ctx=Load()),
                                                        Name(id='got',
                                                             ctx=Load())],
                                                nl=True)])],
                         decorator_list=[]),
             Print(dest=None,
                   values=[Str(s='\nstr.capitalize')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='hello world'),
                                                       attr='capitalize',
                                                       ctx=Load()),
                                        args=[],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='Hello world')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='HELLO WorlD'),
                                                       attr='capitalize',
                                                       ctx=Load()),
                                        args=[],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='Hello world')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Str(s='\nstr.center')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='12345'),
                                                       attr='center',
                                                       ctx=Load()),
                                        args=[Num(n=7)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s=' 12345 ')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='12345'),
                                                       attr='center',
                                                       ctx=Load()),
                                        args=[Num(n=8)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s=' 12345  ')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Str(s='\nstr.count')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='abcd abcba '),
                                                       attr='count',
                                                       ctx=Load()),
                                        args=[Str(s='abc')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Num(n=2)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='aaaaaaaaaaa '),
                                                       attr='count',
                                                       ctx=Load()),
                                        args=[Str(s='aa')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Num(n=5)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Str(s='\nstr.find')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='hello world'),
                                                       attr='find',
                                                       ctx=Load()),
                                        args=[Str(s='l')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Num(n=2)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='hello world'),
                                                       attr='find',
                                                       ctx=Load()),
                                        args=[Str(s='X')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Num(n=-1)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='hello world'),
                                                       attr='find',
                                                       ctx=Load()),
                                        args=[Str(s='l'),
                                              Num(n=3)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Num(n=3)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Str(s='\nstr.index')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='hello world'),
                                                       attr='index',
                                                       ctx=Load()),
                                        args=[Str(s='l')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Num(n=2)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='hello world'),
                                                       attr='index',
                                                       ctx=Load()),
                                        args=[Str(s='l'),
                                              Num(n=3)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Num(n=3)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Str(s='\nstr.isdigit')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='hello'),
                                                       attr='isdigit',
                                                       ctx=Load()),
                                        args=[],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Name(id='False',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='1234'),
                                                       attr='isdigit',
                                                       ctx=Load()),
                                        args=[],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Name(id='True',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s=''),
                                                       attr='isdigit',
                                                       ctx=Load()),
                                        args=[],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Name(id='False',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='123.45'),
                                                       attr='isdigit',
                                                       ctx=Load()),
                                        args=[],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Name(id='False',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Str(s='\nstr.join')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='-'),
                                                       attr='join',
                                                       ctx=Load()),
                                        args=[Str(s='1234')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='1-2-3-4')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='-'),
                                                       attr='join',
                                                       ctx=Load()),
                                        args=[Tuple(elts=[Str(s='1'),
                                                          Str(s='2'),
                                                          Str(s='3'),
                                                          Str(s='4')],
                                                    ctx=Load())],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='1-2-3-4')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='-'),
                                                       attr='join',
                                                       ctx=Load()),
                                        args=[List(elts=[Str(s='1'),
                                                         Str(s='2'),
                                                         Str(s='3'),
                                                         Str(s='4')],
                                                   ctx=Load())],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='1-2-3-4')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Str(s='\nstr.ljust')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='12345'),
                                                       attr='ljust',
                                                       ctx=Load()),
                                        args=[Num(n=8)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='12345   ')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='   12345'),
                                                       attr='ljust',
                                                       ctx=Load()),
                                        args=[Num(n=8)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='   12345')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Str(s='\nstr.lower')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='HELLO'),
                                                       attr='lower',
                                                       ctx=Load()),
                                        args=[],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='hello')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='Hello woRLd!'),
                                                       attr='lower',
                                                       ctx=Load()),
                                        args=[],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='hello world!')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='hello'),
                                                       attr='lower',
                                                       ctx=Load()),
                                        args=[],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='hello')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s=''),
                                                       attr='lower',
                                                       ctx=Load()),
                                        args=[],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Str(s='\nstr.lstrip')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='    hello'),
                                                       attr='lstrip',
                                                       ctx=Load()),
                                        args=[],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='hello')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='     '),
                                                       attr='lstrip',
                                                       ctx=Load()),
                                        args=[],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Str(s='\nstr.partition')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='hello'),
                                                       attr='partition',
                                                       ctx=Load()),
                                        args=[Str(s='h')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Tuple(elts=[Str(s=''),
                                               Str(s='h'),
                                               Str(s='ello')],
                                         ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='hello'),
                                                       attr='partition',
                                                       ctx=Load()),
                                        args=[Str(s='l')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Tuple(elts=[Str(s='he'),
                                               Str(s='l'),
                                               Str(s='lo')],
                                         ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='hello'),
                                                       attr='partition',
                                                       ctx=Load()),
                                        args=[Str(s='o')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Tuple(elts=[Str(s='hell'),
                                               Str(s='o'),
                                               Str(s='')],
                                         ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='hello'),
                                                       attr='partition',
                                                       ctx=Load()),
                                        args=[Str(s='x')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Tuple(elts=[Str(s='hello'),
                                               Str(s=''),
                                               Str(s='')],
                                         ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Str(s='\nstr.replace')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='hello'),
                                                       attr='replace',
                                                       ctx=Load()),
                                        args=[Str(s='l'),
                                              Str(s='L')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='heLLo')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='hello wOrld!'),
                                                       attr='replace',
                                                       ctx=Load()),
                                        args=[Str(s='o'),
                                              Str(s='')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='hell wOrld!')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s=''),
                                                       attr='replace',
                                                       ctx=Load()),
                                        args=[Str(s=''),
                                              Str(s='hello')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='hello')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='hello'),
                                                       attr='replace',
                                                       ctx=Load()),
                                        args=[Str(s=''),
                                              Str(s='!')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='!h!e!l!l!o!')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='abcabcaaaabc'),
                                                       attr='replace',
                                                       ctx=Load()),
                                        args=[Str(s='abc'),
                                              Str(s='123')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='123123aaa123')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Str(s='\nstr.rfind')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='hello world'),
                                                       attr='rfind',
                                                       ctx=Load()),
                                        args=[Str(s='l')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Num(n=9)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='hello world'),
                                                       attr='rfind',
                                                       ctx=Load()),
                                        args=[Str(s='X')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Num(n=-1)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='hello world'),
                                                       attr='rfind',
                                                       ctx=Load()),
                                        args=[Str(s='l'),
                                              Num(n=3)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Num(n=9)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Str(s='\nstr.rindex')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='hello world'),
                                                       attr='rindex',
                                                       ctx=Load()),
                                        args=[Str(s='l')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Num(n=9)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='hello world'),
                                                       attr='rindex',
                                                       ctx=Load()),
                                        args=[Str(s='l'),
                                              Num(n=3)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Num(n=9)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Str(s='\nstr.rjust')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='12345'),
                                                       attr='rjust',
                                                       ctx=Load()),
                                        args=[Num(n=8)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='   12345')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='12345   '),
                                                       attr='rjust',
                                                       ctx=Load()),
                                        args=[Num(n=8)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='12345   ')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Str(s='\nstr.rpartition')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='hello'),
                                                       attr='rpartition',
                                                       ctx=Load()),
                                        args=[Str(s='h')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Tuple(elts=[Str(s=''),
                                               Str(s='h'),
                                               Str(s='ello')],
                                         ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='hello'),
                                                       attr='rpartition',
                                                       ctx=Load()),
                                        args=[Str(s='l')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Tuple(elts=[Str(s='hel'),
                                               Str(s='l'),
                                               Str(s='o')],
                                         ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='hello'),
                                                       attr='rpartition',
                                                       ctx=Load()),
                                        args=[Str(s='o')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Tuple(elts=[Str(s='hell'),
                                               Str(s='o'),
                                               Str(s='')],
                                         ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='hello'),
                                                       attr='rpartition',
                                                       ctx=Load()),
                                        args=[Str(s='x')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Tuple(elts=[Str(s=''),
                                               Str(s=''),
                                               Str(s='hello')],
                                         ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Str(s='\nstr.rstrip')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='hello    '),
                                                       attr='rstrip',
                                                       ctx=Load()),
                                        args=[],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='hello')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='     '),
                                                       attr='rstrip',
                                                       ctx=Load()),
                                        args=[],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Str(s='\nstr.split')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s=''),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s=''),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Name(id='None',
                                                   ctx=Load())],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='hello'),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='hello')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='hello'),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Name(id='None',
                                                   ctx=Load())],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='hello')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='hello world   ! '),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='hello'),
                                              Str(s='world'),
                                              Str(s='!')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s=''),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Str(s='a')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s=''),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Str(s='a'),
                                              Num(n=1)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='hello'),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Str(s='l')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='he'),
                                              Str(s=''),
                                              Str(s='o')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='hello'),
                                                       attr='split',
                                                       ctx=Load()),
                                        args=[Str(s='l'),
                                              Num(n=1)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   List(elts=[Str(s='he'),
                                              Str(s='lo')],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Str(s='\nstr.strip')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='    hello    '),
                                                       attr='strip',
                                                       ctx=Load()),
                                        args=[],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='hello')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='     '),
                                                       attr='strip',
                                                       ctx=Load()),
                                        args=[],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Str(s='\nstr.upper')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='hello'),
                                                       attr='upper',
                                                       ctx=Load()),
                                        args=[],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='HELLO')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='Hello woRLd!'),
                                                       attr='upper',
                                                       ctx=Load()),
                                        args=[],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='HELLO WORLD!')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='HELLO'),
                                                       attr='upper',
                                                       ctx=Load()),
                                        args=[],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='HELLO')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s=''),
                                                       attr='upper',
                                                       ctx=Load()),
                                        args=[],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='')],
                             keywords=[],
                             starargs=None,
                             kwargs=None))])
