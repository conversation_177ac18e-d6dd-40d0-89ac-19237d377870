Module(body=[Assign(targets=[Name(id='l1',
                                  ctx=Store())],
                    value=List(elts=[Num(n=42)],
                               ctx=Load())),
             Assign(targets=[Name(id='l2',
                                  ctx=Store())],
                    value=Name(id='l1',
                               ctx=Load())),
             AugAssign(target=Name(id='l1',
                                   ctx=Store()),
                       op=Add(),
                       value=List(elts=[Num(n=99)],
                                  ctx=Load())),
             Print(dest=None,
                   values=[Name(id='l1',
                                ctx=Load()),
                           Name(id='l2',
                                ctx=Load())],
                   nl=True),
             AugAssign(target=Name(id='l1',
                                   ctx=Store()),
                       op=Add(),
                       value=Name(id='l1',
                                  ctx=Load())),
             Print(dest=None,
                   values=[Name(id='l1',
                                ctx=Load()),
                           Name(id='l2',
                                ctx=Load())],
                   nl=True)])
