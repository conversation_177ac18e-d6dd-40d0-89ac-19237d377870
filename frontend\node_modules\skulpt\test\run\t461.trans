Module(body=[Import(names=[alias(name='re',
                                 asname=None)]),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='^\\s*$'),
                                      Str(s='')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='\\s*|a'),
                                      Str(s='   a  b')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='a|\\s*'),
                                      Str(s='   a  b')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='\\s*|a'),
                                      Str(s='   ba  b')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='a|\\s*'),
                                      Str(s='   ba  b')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True)])
