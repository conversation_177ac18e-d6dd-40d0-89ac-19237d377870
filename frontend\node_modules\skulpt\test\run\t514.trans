Module(body=[TryExcept(body=[Print(dest=None,
                                   values=[BinOp(left=Str(s='a'),
                                                 op=Mult(),
                                                 right=Str(s='b'))],
                                   nl=True)],
                       handlers=[ExceptHandler(type=Name(id='TypeError',
                                                         ctx=Load()),
                                               name=Name(id='e',
                                                         ctx=Store()),
                                               body=[Print(dest=None,
                                                           values=[Name(id='e',
                                                                        ctx=Load())],
                                                           nl=True)])],
                       orelse=[]),
             TryExcept(body=[Print(dest=None,
                                   values=[BinOp(left=Str(s='a'),
                                                 op=Mult(),
                                                 right=Num(n=3.4))],
                                   nl=True)],
                       handlers=[ExceptHandler(type=Name(id='TypeError',
                                                         ctx=Load()),
                                               name=Name(id='e',
                                                         ctx=Store()),
                                               body=[Print(dest=None,
                                                           values=[Name(id='e',
                                                                        ctx=Load())],
                                                           nl=True)])],
                       orelse=[]),
             TryExcept(body=[Print(dest=None,
                                   values=[BinOp(left=Num(n=3.4),
                                                 op=Mult(),
                                                 right=Str(s='b'))],
                                   nl=True)],
                       handlers=[ExceptHandler(type=Name(id='TypeError',
                                                         ctx=Load()),
                                               name=Name(id='e',
                                                         ctx=Store()),
                                               body=[Print(dest=None,
                                                           values=[Name(id='e',
                                                                        ctx=Load())],
                                                           nl=True)])],
                       orelse=[]),
             TryExcept(body=[Print(dest=None,
                                   values=[BinOp(left=Str(s='a'),
                                                 op=Mult(),
                                                 right=List(elts=[Num(n=2)],
                                                            ctx=Load()))],
                                   nl=True)],
                       handlers=[ExceptHandler(type=Name(id='TypeError',
                                                         ctx=Load()),
                                               name=Name(id='e',
                                                         ctx=Store()),
                                               body=[Print(dest=None,
                                                           values=[Name(id='e',
                                                                        ctx=Load())],
                                                           nl=True)])],
                       orelse=[]),
             TryExcept(body=[Print(dest=None,
                                   values=[BinOp(left=List(elts=[Num(n=2)],
                                                           ctx=Load()),
                                                 op=Mult(),
                                                 right=Str(s='b'))],
                                   nl=True)],
                       handlers=[ExceptHandler(type=Name(id='TypeError',
                                                         ctx=Load()),
                                               name=Name(id='e',
                                                         ctx=Store()),
                                               body=[Print(dest=None,
                                                           values=[Name(id='e',
                                                                        ctx=Load())],
                                                           nl=True)])],
                       orelse=[])])
