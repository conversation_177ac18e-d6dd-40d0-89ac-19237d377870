Module(body=[Print(dest=None,
                   values=[Call(func=Name(id='int',
                                          ctx=Load()),
                                args=[Str(s='0x13'),
                                      Num(n=16)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='int',
                                          ctx=Load()),
                                args=[Str(s='0x13'),
                                      Num(n=0)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='int',
                                          ctx=Load()),
                                args=[Str(s='0b0110'),
                                      Num(n=0)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='int',
                                          ctx=Load()),
                                args=[Num(n=3.2)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             FunctionDef(name='foo',
                         args=arguments(args=[Name(id='y',
                                                   ctx=Param()),
                                              Name(id='x',
                                                   ctx=Param())],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[Str(s='K'),
                                                  Str(s='Z')]),
                         body=[Print(dest=None,
                                     values=[BinOp(left=Name(id='x',
                                                             ctx=Load()),
                                                   op=Add(),
                                                   right=Name(id='y',
                                                              ctx=Load()))],
                                     nl=True)],
                         decorator_list=[]),
             Expr(value=Call(func=Name(id='foo',
                                       ctx=Load()),
                             args=[],
                             keywords=[keyword(arg='x',
                                               value=Str(s='P'))],
                             starargs=None,
                             kwargs=None))])
