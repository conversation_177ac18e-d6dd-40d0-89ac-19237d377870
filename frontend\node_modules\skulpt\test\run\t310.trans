Module(body=[Assign(targets=[Name(id='s',
                                  ctx=Store())],
                    value=Call(func=Name(id='set',
                                         ctx=Load()),
                               args=[List(elts=[Num(n=2),
                                                Num(n=3),
                                                Num(n=4)],
                                          ctx=Load())],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Assign(targets=[Name(id='t',
                                  ctx=Store())],
                    value=Call(func=Name(id='set',
                                         ctx=Load()),
                               args=[List(elts=[Num(n=3),
                                                Num(n=4),
                                                Num(n=5)],
                                          ctx=Load())],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Assign(targets=[Name(id='u',
                                  ctx=Store())],
                    value=Call(func=Name(id='set',
                                         ctx=Load()),
                               args=[List(elts=[Num(n=1),
                                                Num(n=3),
                                                Num(n=5)],
                                          ctx=Load())],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Assign(targets=[Name(id='a',
                                  ctx=Store())],
                    value=Call(func=Attribute(value=Name(id='s',
                                                         ctx=Load()),
                                              attr='intersection',
                                              ctx=Load()),
                               args=[Name(id='t',
                                          ctx=Load())],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Assign(targets=[Name(id='b',
                                  ctx=Store())],
                    value=Call(func=Attribute(value=Name(id='u',
                                                         ctx=Load()),
                                              attr='intersection',
                                              ctx=Load()),
                               args=[Name(id='s',
                                          ctx=Load())],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Assign(targets=[Name(id='c',
                                  ctx=Store())],
                    value=Call(func=Attribute(value=Name(id='u',
                                                         ctx=Load()),
                                              attr='intersection',
                                              ctx=Load()),
                               args=[Name(id='t',
                                          ctx=Load())],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Name(id='a',
                                ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Name(id='b',
                                ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Name(id='c',
                                ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='a',
                                             ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Call(func=Name(id='set',
                                                               ctx=Load()),
                                                     args=[List(elts=[Num(n=3),
                                                                      Num(n=4)],
                                                                ctx=Load())],
                                                     keywords=[],
                                                     starargs=None,
                                                     kwargs=None)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='b',
                                             ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Call(func=Name(id='set',
                                                               ctx=Load()),
                                                     args=[List(elts=[Num(n=3)],
                                                                ctx=Load())],
                                                     keywords=[],
                                                     starargs=None,
                                                     kwargs=None)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='c',
                                             ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Call(func=Name(id='set',
                                                               ctx=Load()),
                                                     args=[List(elts=[Num(n=3),
                                                                      Num(n=5)],
                                                                ctx=Load())],
                                                     keywords=[],
                                                     starargs=None,
                                                     kwargs=None)])],
                   nl=True),
             Assign(targets=[Name(id='d',
                                  ctx=Store())],
                    value=Call(func=Attribute(value=Name(id='s',
                                                         ctx=Load()),
                                              attr='intersection',
                                              ctx=Load()),
                               args=[Name(id='t',
                                          ctx=Load()),
                                     Name(id='u',
                                          ctx=Load())],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Name(id='d',
                                ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='d',
                                             ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Call(func=Name(id='set',
                                                               ctx=Load()),
                                                     args=[List(elts=[Num(n=3)],
                                                                ctx=Load())],
                                                     keywords=[],
                                                     starargs=None,
                                                     kwargs=None)])],
                   nl=True)])
