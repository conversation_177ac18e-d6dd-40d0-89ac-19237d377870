Module(body=[Assign(targets=[Name(id='x',
                                  ctx=Store())],
                    value=Dict(keys=[Str(s='foo')],
                               values=[Num(n=1)])),
             Print(dest=None,
                   values=[Name(id='x',
                                ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Subscript(value=Name(id='x',
                                                ctx=Load()),
                                     slice=Index(value=Str(s='foo')),
                                     ctx=Load())],
                   nl=True),
             Assign(targets=[Subscript(value=Name(id='x',
                                                  ctx=Load()),
                                       slice=Index(value=Str(s='foo')),
                                       ctx=Store())],
                    value=Name(id='None',
                               ctx=Load())),
             Print(dest=None,
                   values=[Name(id='x',
                                ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Subscript(value=Name(id='x',
                                                ctx=Load()),
                                     slice=Index(value=Str(s='foo')),
                                     ctx=Load())],
                   nl=True)])
