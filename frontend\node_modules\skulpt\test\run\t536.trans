Module(body=[FunctionDef(name='f',
                         args=arguments(args=[],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[]),
                         body=[Assign(targets=[Name(id='pool',
                                                    ctx=Store())],
                                      value=Tuple(elts=[Num(n=8),
                                                        Num(n=9)],
                                                  ctx=Load())),
                               Print(dest=None,
                                     values=[Call(func=Name(id='type',
                                                            ctx=Load()),
                                                  args=[Name(id='pool',
                                                             ctx=Load())],
                                                  keywords=[],
                                                  starargs=None,
                                                  kwargs=None)],
                                     nl=True),
                               Expr(value=Yield(value=Call(func=Name(id='list',
                                                                     ctx=Load()),
                                                           args=[GeneratorExp(elt=Subscript(value=Name(id='pool',
                                                                                                       ctx=Load()),
                                                                                            slice=Index(value=Name(id='i',
                                                                                                                   ctx=Load())),
                                                                                            ctx=Load()),
                                                                              generators=[comprehension(target=Name(id='i',
                                                                                                                    ctx=Store()),
                                                                                                        iter=Call(func=Name(id='range',
                                                                                                                            ctx=Load()),
                                                                                                                  args=[Num(n=2)],
                                                                                                                  keywords=[],
                                                                                                                  starargs=None,
                                                                                                                  kwargs=None),
                                                                                                        ifs=[])])],
                                                           keywords=[],
                                                           starargs=None,
                                                           kwargs=None))),
                               Print(dest=None,
                                     values=[Call(func=Name(id='type',
                                                            ctx=Load()),
                                                  args=[Name(id='pool',
                                                             ctx=Load())],
                                                  keywords=[],
                                                  starargs=None,
                                                  kwargs=None)],
                                     nl=True)],
                         decorator_list=[]),
             Print(dest=None,
                   values=[Call(func=Name(id='list',
                                          ctx=Load()),
                                args=[Call(func=Name(id='f',
                                                     ctx=Load()),
                                           args=[],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True)])
