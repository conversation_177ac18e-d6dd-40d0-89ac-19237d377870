Module(body=[FunctionDef(name='helper',
                         args=arguments(args=[Name(id='got',
                                                   ctx=Param()),
                                              Name(id='expected',
                                                   ctx=Param())],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[]),
                         body=[If(test=Compare(left=Name(id='got',
                                                         ctx=Load()),
                                               ops=[Eq()],
                                               comparators=[Name(id='expected',
                                                                 ctx=Load())]),
                                  body=[Print(dest=None,
                                              values=[Name(id='True',
                                                           ctx=Load())],
                                              nl=True)],
                                  orelse=[Print(dest=None,
                                                values=[Name(id='False',
                                                             ctx=Load()),
                                                        Name(id='expected',
                                                             ctx=Load()),
                                                        Name(id='got',
                                                             ctx=Load())],
                                                nl=True)])],
                         decorator_list=[]),
             Print(dest=None,
                   values=[Str(s='\nstr.strip')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='hello'),
                                                       attr='strip',
                                                       ctx=Load()),
                                        args=[],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='hello')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='hello'),
                                                       attr='strip',
                                                       ctx=Load()),
                                        args=[Str(s='')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='hello')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='  hello  '),
                                                       attr='strip',
                                                       ctx=Load()),
                                        args=[],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='hello')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='  hello  '),
                                                       attr='strip',
                                                       ctx=Load()),
                                        args=[Str(s='')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='  hello  ')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='..hello..'),
                                                       attr='strip',
                                                       ctx=Load()),
                                        args=[],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='..hello..')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='..hello..'),
                                                       attr='strip',
                                                       ctx=Load()),
                                        args=[Str(s='.')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='hello')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='abcz'),
                                                       attr='strip',
                                                       ctx=Load()),
                                        args=[Str(s='a-z')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='bc')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='z alpha z'),
                                                       attr='strip',
                                                       ctx=Load()),
                                        args=[Str(s='a-z')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s=' alpha ')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='hello world'),
                                                       attr='strip',
                                                       ctx=Load()),
                                        args=[Str(s='^[a-z]*.\\s+.*')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='hello world')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='[$]hello-^'),
                                                       attr='strip',
                                                       ctx=Load()),
                                        args=[Str(s='^[a-z]$')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='hello')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Str(s='\nstr.lstrip')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='hello'),
                                                       attr='lstrip',
                                                       ctx=Load()),
                                        args=[],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='hello')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='hello'),
                                                       attr='lstrip',
                                                       ctx=Load()),
                                        args=[Str(s='')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='hello')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='  hello  '),
                                                       attr='lstrip',
                                                       ctx=Load()),
                                        args=[],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='hello  ')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='  hello  '),
                                                       attr='lstrip',
                                                       ctx=Load()),
                                        args=[Str(s='')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='  hello  ')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='..hello..'),
                                                       attr='lstrip',
                                                       ctx=Load()),
                                        args=[],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='..hello..')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='..hello..'),
                                                       attr='lstrip',
                                                       ctx=Load()),
                                        args=[Str(s='.')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='hello..')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='abcz'),
                                                       attr='lstrip',
                                                       ctx=Load()),
                                        args=[Str(s='a-z')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='bcz')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='z alpha z'),
                                                       attr='lstrip',
                                                       ctx=Load()),
                                        args=[Str(s='a-z')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s=' alpha z')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='hello world'),
                                                       attr='lstrip',
                                                       ctx=Load()),
                                        args=[Str(s='^[a-z]*.\\s+.*')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='hello world')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='[$]hello-^'),
                                                       attr='lstrip',
                                                       ctx=Load()),
                                        args=[Str(s='^[a-z]$')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='hello-^')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Str(s='\nstr.rstrip')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='hello'),
                                                       attr='rstrip',
                                                       ctx=Load()),
                                        args=[],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='hello')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='hello'),
                                                       attr='rstrip',
                                                       ctx=Load()),
                                        args=[Str(s='')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='hello')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='  hello  '),
                                                       attr='rstrip',
                                                       ctx=Load()),
                                        args=[],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='  hello')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='  hello  '),
                                                       attr='rstrip',
                                                       ctx=Load()),
                                        args=[Str(s='')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='  hello  ')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='..hello..'),
                                                       attr='rstrip',
                                                       ctx=Load()),
                                        args=[],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='..hello..')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='..hello..'),
                                                       attr='rstrip',
                                                       ctx=Load()),
                                        args=[Str(s='.')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='..hello')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='abcz'),
                                                       attr='rstrip',
                                                       ctx=Load()),
                                        args=[Str(s='a-z')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='abc')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='z alpha z'),
                                                       attr='rstrip',
                                                       ctx=Load()),
                                        args=[Str(s='a-z')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='z alpha ')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='hello world'),
                                                       attr='rstrip',
                                                       ctx=Load()),
                                        args=[Str(s='^[a-z]*.\\s+.*')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='hello world')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='[$]hello-^'),
                                                       attr='rstrip',
                                                       ctx=Load()),
                                        args=[Str(s='^[a-z]$')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='[$]hello')],
                             keywords=[],
                             starargs=None,
                             kwargs=None))])
