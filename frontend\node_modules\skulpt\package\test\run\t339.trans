Module(body=[Import(names=[alias(name='re',
                                 asname=None)]),
             Assign(targets=[Name(id='val',
                                  ctx=Store())],
                    value=Call(func=Attribute(value=Name(id='re',
                                                         ctx=Load()),
                                              attr='findall',
                                              ctx=Load()),
                               args=[Str(s='From'),
                                     Str(s='dlkjdsljkdlkdsjlk')],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Name(id='val',
                                ctx=Load())],
                   nl=True),
             If(test=Compare(left=Call(func=Name(id='len',
                                                 ctx=Load()),
                                       args=[Name(id='val',
                                                  ctx=Load())],
                                       keywords=[],
                                       starargs=None,
                                       kwargs=None),
                             ops=[Eq()],
                             comparators=[Num(n=0)]),
                body=[Print(dest=None,
                            values=[Str(s='Correct 1')],
                            nl=True)],
                orelse=[Print(dest=None,
                              values=[Str(s='InCorrect 1')],
                              nl=True)]),
             Assign(targets=[Name(id='val',
                                  ctx=Store())],
                    value=Call(func=Attribute(value=Name(id='re',
                                                         ctx=Load()),
                                              attr='findall',
                                              ctx=Load()),
                               args=[Str(s='From'),
                                     Str(s='dlkjd From kdsjlk')],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Name(id='val',
                                ctx=Load())],
                   nl=True),
             If(test=Compare(left=Call(func=Name(id='len',
                                                 ctx=Load()),
                                       args=[Name(id='val',
                                                  ctx=Load())],
                                       keywords=[],
                                       starargs=None,
                                       kwargs=None),
                             ops=[Eq()],
                             comparators=[Num(n=1)]),
                body=[Print(dest=None,
                            values=[Str(s='Correct 2')],
                            nl=True)],
                orelse=[Print(dest=None,
                              values=[Str(s='InCorrect 2')],
                              nl=True)]),
             Assign(targets=[Name(id='val',
                                  ctx=Store())],
                    value=Call(func=Attribute(value=Name(id='re',
                                                         ctx=Load()),
                                              attr='findall',
                                              ctx=Load()),
                               args=[Str(s='From'),
                                     Str(s='From dlkjd From kdsjlk')],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Name(id='val',
                                ctx=Load())],
                   nl=True),
             If(test=Compare(left=Call(func=Name(id='len',
                                                 ctx=Load()),
                                       args=[Name(id='val',
                                                  ctx=Load())],
                                       keywords=[],
                                       starargs=None,
                                       kwargs=None),
                             ops=[Eq()],
                             comparators=[Num(n=2)]),
                body=[Print(dest=None,
                            values=[Str(s='Correct 3')],
                            nl=True)],
                orelse=[Print(dest=None,
                              values=[Str(s='InCorrect 3')],
                              nl=True)]),
             Assign(targets=[Name(id='val',
                                  ctx=Store())],
                    value=Call(func=Attribute(value=Name(id='re',
                                                         ctx=Load()),
                                              attr='findall',
                                              ctx=Load()),
                               args=[Str(s='[0-9]+/[0-9]+'),
                                     Str(s='1/2 1/3 3/4 1/8 fred 10/0')],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Name(id='val',
                                ctx=Load())],
                   nl=True),
             If(test=Compare(left=Call(func=Name(id='len',
                                                 ctx=Load()),
                                       args=[Name(id='val',
                                                  ctx=Load())],
                                       keywords=[],
                                       starargs=None,
                                       kwargs=None),
                             ops=[Eq()],
                             comparators=[Num(n=5)]),
                body=[Print(dest=None,
                            values=[Str(s='Correct 4')],
                            nl=True)],
                orelse=[Print(dest=None,
                              values=[Str(s='InCorrect 4')],
                              nl=True)]),
             Assign(targets=[Name(id='val',
                                  ctx=Store())],
                    value=Call(func=Attribute(value=Name(id='re',
                                                         ctx=Load()),
                                              attr='search',
                                              ctx=Load()),
                               args=[Str(s='From'),
                                     Str(s='dlkjdsljkdlkdsjlk')],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             If(test=Compare(left=Name(id='val',
                                       ctx=Load()),
                             ops=[Is()],
                             comparators=[Name(id='None',
                                               ctx=Load())]),
                body=[Print(dest=None,
                            values=[Str(s='Correct 5')],
                            nl=True)],
                orelse=[Print(dest=None,
                              values=[Str(s='InCorrect 5'),
                                      Name(id='val',
                                           ctx=Load())],
                              nl=True)]),
             Assign(targets=[Name(id='val',
                                  ctx=Store())],
                    value=Call(func=Attribute(value=Name(id='re',
                                                         ctx=Load()),
                                              attr='search',
                                              ctx=Load()),
                               args=[Str(s='From'),
                                     Str(s='dlkjd From kdsjlk')],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             If(test=Compare(left=Name(id='val',
                                       ctx=Load()),
                             ops=[IsNot()],
                             comparators=[Name(id='None',
                                               ctx=Load())]),
                body=[Print(dest=None,
                            values=[Str(s='Correct 6')],
                            nl=True)],
                orelse=[Print(dest=None,
                              values=[Str(s='InCorrect 6'),
                                      Name(id='val',
                                           ctx=Load())],
                              nl=True)]),
             Assign(targets=[Name(id='val',
                                  ctx=Store())],
                    value=Call(func=Attribute(value=Name(id='re',
                                                         ctx=Load()),
                                              attr='search',
                                              ctx=Load()),
                               args=[Str(s='From'),
                                     Str(s='From dlkjd From kdsjlk')],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             If(test=Compare(left=Name(id='val',
                                       ctx=Load()),
                             ops=[IsNot()],
                             comparators=[Name(id='None',
                                               ctx=Load())]),
                body=[Print(dest=None,
                            values=[Str(s='Correct 7')],
                            nl=True)],
                orelse=[Print(dest=None,
                              values=[Str(s='InCorrect 7'),
                                      Name(id='val',
                                           ctx=Load())],
                              nl=True)]),
             Assign(targets=[Name(id='val',
                                  ctx=Store())],
                    value=Call(func=Attribute(value=Name(id='re',
                                                         ctx=Load()),
                                              attr='match',
                                              ctx=Load()),
                               args=[Str(s='From'),
                                     Str(s='dlkjdsljkdlkdsjlk')],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             If(test=Compare(left=Name(id='val',
                                       ctx=Load()),
                             ops=[Is()],
                             comparators=[Name(id='None',
                                               ctx=Load())]),
                body=[Print(dest=None,
                            values=[Str(s='Correct 8')],
                            nl=True)],
                orelse=[Print(dest=None,
                              values=[Str(s='InCorrect 8'),
                                      Name(id='val',
                                           ctx=Load())],
                              nl=True)]),
             Assign(targets=[Name(id='val',
                                  ctx=Store())],
                    value=Call(func=Attribute(value=Name(id='re',
                                                         ctx=Load()),
                                              attr='match',
                                              ctx=Load()),
                               args=[Str(s='From'),
                                     Str(s='dlkjd From kdsjlk')],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             If(test=Compare(left=Name(id='val',
                                       ctx=Load()),
                             ops=[Is()],
                             comparators=[Name(id='None',
                                               ctx=Load())]),
                body=[Print(dest=None,
                            values=[Str(s='Correct 9')],
                            nl=True)],
                orelse=[Print(dest=None,
                              values=[Str(s='InCorrect 9'),
                                      Name(id='val',
                                           ctx=Load())],
                              nl=True)]),
             Assign(targets=[Name(id='val',
                                  ctx=Store())],
                    value=Call(func=Attribute(value=Name(id='re',
                                                         ctx=Load()),
                                              attr='match',
                                              ctx=Load()),
                               args=[Str(s='From'),
                                     Str(s='From dlkjd From kdsjlk')],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             If(test=Compare(left=Name(id='val',
                                       ctx=Load()),
                             ops=[IsNot()],
                             comparators=[Name(id='None',
                                               ctx=Load())]),
                body=[Print(dest=None,
                            values=[Str(s='Correct 10')],
                            nl=True)],
                orelse=[Print(dest=None,
                              values=[Str(s='InCorrect 10'),
                                      Name(id='val',
                                           ctx=Load())],
                              nl=True)])])
