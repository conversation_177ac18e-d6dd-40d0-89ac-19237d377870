import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { useLessonStore } from '../store/lessonStore';
import { useAuthStore } from '../store/authStore';
import CodeEditor from '../components/CodeEditor/CodeEditor';
import { 
  ArrowLeft, 
  ArrowRight, 
  Play, 
  RotateCcw, 
  CheckCircle, 
  XCircle,
  Lightbulb,
  BookOpen,
  Clock,
  Target
} from 'lucide-react';

const LessonView: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { lessons, currentLesson, setCurrentLesson, completeLesson } = useLessonStore();
  const { user, updateUser } = useAuthStore();
  
  const [currentExerciseIndex, setCurrentExerciseIndex] = useState(0);
  const [code, setCode] = useState('');
  const [output, setOutput] = useState('');
  const [isRunning, setIsRunning] = useState(false);
  const [showHints, setShowHints] = useState(false);
  const [exerciseCompleted, setExerciseCompleted] = useState(false);
  const [lessonStartTime] = useState(Date.now());

  useEffect(() => {
    if (id) {
      setCurrentLesson(id);
    }
  }, [id, setCurrentLesson]);

  useEffect(() => {
    if (currentLesson && currentLesson.exercises.length > 0) {
      const exercise = currentLesson.exercises[currentExerciseIndex];
      setCode(exercise.starterCode);
      setOutput('');
      setExerciseCompleted(false);
      setShowHints(false);
    }
  }, [currentLesson, currentExerciseIndex]);

  if (!currentLesson) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="text-6xl mb-4">🤔</div>
          <h2 className="text-kid-xl font-bold text-gray-900 mb-2">Lesson not found</h2>
          <p className="text-gray-600 mb-4">The lesson you're looking for doesn't exist.</p>
          <Link to="/lessons" className="btn-primary">
            <ArrowLeft className="mr-2" size={16} />
            Back to Lessons
          </Link>
        </div>
      </div>
    );
  }

  const currentExercise = currentLesson.exercises[currentExerciseIndex];
  const isLastExercise = currentExerciseIndex === currentLesson.exercises.length - 1;
  const nextLesson = lessons.find(l => l.order === currentLesson.order + 1);

  const runCode = async () => {
    setIsRunning(true);
    try {
      // This would integrate with Skulpt.js for Python execution
      // For now, we'll simulate code execution
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Simple check if the code matches the expected solution
      const normalizedCode = code.trim().replace(/\s+/g, ' ');
      const normalizedSolution = currentExercise.solution.trim().replace(/\s+/g, ' ');
      
      if (normalizedCode.includes('print(') && normalizedCode.includes('Hello')) {
        setOutput('Hello, World!');
        setExerciseCompleted(true);
      } else {
        setOutput('Try using the print() function to display text!');
      }
    } catch (error) {
      setOutput('Error: Something went wrong. Check your code and try again.');
    } finally {
      setIsRunning(false);
    }
  };

  const resetCode = () => {
    setCode(currentExercise.starterCode);
    setOutput('');
    setExerciseCompleted(false);
  };

  const nextExercise = () => {
    if (currentExerciseIndex < currentLesson.exercises.length - 1) {
      setCurrentExerciseIndex(currentExerciseIndex + 1);
    }
  };

  const completeCurrentLesson = async () => {
    const timeSpent = Math.round((Date.now() - lessonStartTime) / 1000 / 60); // minutes
    const score = 100; // For now, assume perfect score
    
    await completeLesson(currentLesson.id, score, timeSpent);
    
    // Update user points
    if (user) {
      updateUser({ 
        points: (user.points || 0) + 10,
        lessonsCompleted: (user.lessonsCompleted || 0) + 1
      });
    }

    if (nextLesson) {
      navigate(`/lessons/${nextLesson.id}`);
    } else {
      navigate('/dashboard');
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 sticky top-0 z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Link 
                to="/lessons"
                className="flex items-center text-gray-600 hover:text-primary-600 transition-colors"
              >
                <ArrowLeft size={20} className="mr-1" />
                Back to Lessons
              </Link>
              <div className="h-6 w-px bg-gray-300" />
              <h1 className="text-kid-lg font-bold text-gray-900">
                {currentLesson.title}
              </h1>
            </div>
            
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2 text-kid-sm text-gray-600">
                <Clock size={16} />
                <span>{currentLesson.estimatedTime} min</span>
              </div>
              <div className="flex items-center space-x-2 text-kid-sm text-gray-600">
                <Target size={16} />
                <span>Exercise {currentExerciseIndex + 1} of {currentLesson.exercises.length}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Left Panel - Lesson Content */}
          <div className="space-y-6">
            {/* Lesson Content */}
            <div className="card">
              <div className="flex items-center space-x-2 mb-4">
                <BookOpen className="text-primary-500" size={24} />
                <h2 className="text-kid-xl font-bold text-gray-900">Lesson Content</h2>
              </div>
              <div className="prose prose-kid max-w-none">
                <div dangerouslySetInnerHTML={{ __html: currentLesson.content.replace(/\n/g, '<br>') }} />
              </div>
            </div>

            {/* Current Exercise */}
            <div className="card">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-kid-lg font-bold text-gray-900">
                  Exercise {currentExerciseIndex + 1}: {currentExercise.title}
                </h3>
                {exerciseCompleted && (
                  <div className="flex items-center space-x-2 text-success-600">
                    <CheckCircle size={20} />
                    <span className="font-medium">Completed!</span>
                  </div>
                )}
              </div>
              
              <p className="text-gray-700 mb-4">{currentExercise.description}</p>
              
              {/* Hints */}
              <div className="mb-4">
                <button
                  onClick={() => setShowHints(!showHints)}
                  className="flex items-center space-x-2 text-warning-600 hover:text-warning-700 transition-colors"
                >
                  <Lightbulb size={16} />
                  <span className="text-kid-sm font-medium">
                    {showHints ? 'Hide Hints' : 'Show Hints'}
                  </span>
                </button>
                
                {showHints && (
                  <div className="mt-3 p-4 bg-warning-50 border border-warning-200 rounded-kid">
                    <h4 className="font-medium text-warning-800 mb-2">💡 Hints:</h4>
                    <ul className="space-y-1 text-kid-sm text-warning-700">
                      {currentExercise.hints.map((hint, index) => (
                        <li key={index}>• {hint}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Right Panel - Code Editor */}
          <div className="space-y-6">
            {/* Code Editor */}
            <div className="card">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-kid-lg font-bold text-gray-900">Code Editor</h3>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={resetCode}
                    className="flex items-center space-x-2 px-3 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-kid transition-colors"
                  >
                    <RotateCcw size={16} />
                    <span className="text-kid-sm">Reset</span>
                  </button>
                  <button
                    onClick={runCode}
                    disabled={isRunning}
                    className="btn-primary flex items-center space-x-2 disabled:opacity-50"
                  >
                    <Play size={16} />
                    <span>{isRunning ? 'Running...' : 'Run Code'}</span>
                  </button>
                </div>
              </div>
              
              <CodeEditor
                value={code}
                onChange={setCode}
                language="python"
                height="300px"
              />
            </div>

            {/* Output */}
            <div className="card">
              <h3 className="text-kid-lg font-bold text-gray-900 mb-4">Output</h3>
              <div className="bg-gray-900 text-green-400 p-4 rounded-kid font-mono text-kid-sm min-h-[120px]">
                {output || 'Click "Run Code" to see the output here...'}
              </div>
            </div>

            {/* Navigation */}
            <div className="flex justify-between">
              <button
                onClick={() => setCurrentExerciseIndex(Math.max(0, currentExerciseIndex - 1))}
                disabled={currentExerciseIndex === 0}
                className="btn-secondary disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <ArrowLeft size={16} className="mr-2" />
                Previous
              </button>

              {exerciseCompleted && (
                <>
                  {!isLastExercise ? (
                    <button
                      onClick={nextExercise}
                      className="btn-primary"
                    >
                      Next Exercise
                      <ArrowRight size={16} className="ml-2" />
                    </button>
                  ) : (
                    <button
                      onClick={completeCurrentLesson}
                      className="btn-success"
                    >
                      Complete Lesson
                      <CheckCircle size={16} className="ml-2" />
                    </button>
                  )}
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LessonView;
