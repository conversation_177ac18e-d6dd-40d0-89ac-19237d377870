Module(body=[FunctionDef(name='test',
                         args=arguments(args=[Name(id='t',
                                                   ctx=Param())],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[]),
                         body=[Assign(targets=[Name(id='t',
                                                    ctx=Store())],
                                      value=BinOp(left=Str(s='O'),
                                                  op=Add(),
                                                  right=Name(id='t',
                                                             ctx=Load()))),
                               Print(dest=None,
                                     values=[Name(id='t',
                                                  ctx=Load())],
                                     nl=True)],
                         decorator_list=[]),
             Expr(value=Call(func=Name(id='test',
                                       ctx=Load()),
                             args=[Str(s='K')],
                             keywords=[],
                             starargs=None,
                             kwargs=None))])
