Module(body=[Print(dest=None,
                   values=[BinOp(left=Str(s='formatting with just %d argument'),
                                 op=Mod(),
                                 right=Num(n=1))],
                   nl=True),
             Print(dest=None,
                   values=[BinOp(left=Str(s='%d %i %o %x %X %e %E %f %F'),
                                 op=Mod(),
                                 right=Tuple(elts=[Num(n=12),
                                                   Num(n=-12),
                                                   Num(n=-7),
                                                   <PERSON>um(n=74),
                                                   <PERSON>um(n=-74),
                                                   <PERSON>um(n=23000000000.0),
                                                   Num(n=2.3e-10),
                                                   Num(n=1.23),
                                                   Num(n=-1.23)],
                                             ctx=Load()))],
                   nl=True),
             Print(dest=None,
                   values=[BinOp(left=Str(s='%g %G %g %G'),
                                 op=Mod(),
                                 right=Tuple(elts=[Num(n=1.23e-06),
                                                   Num(n=1.23e-06),
                                                   <PERSON>um(n=1.4),
                                                   <PERSON>um(n=-1.4)],
                                             ctx=Load()))],
                   nl=True),
             Print(dest=None,
                   values=[BinOp(left=Str(s='%r is a repr and %s is a string'),
                                 op=Mod(),
                                 right=Tuple(elts=[Str(s='this'),
                                                   Str(s='this')],
                                             ctx=Load()))],
                   nl=True),
             Print(dest=None,
                   values=[BinOp(left=Str(s='I can also use a %(structure)s to format.'),
                                 op=Mod(),
                                 right=Dict(keys=[Str(s='structure')],
                                            values=[Str(s='dictionary')]))],
                   nl=True)])
