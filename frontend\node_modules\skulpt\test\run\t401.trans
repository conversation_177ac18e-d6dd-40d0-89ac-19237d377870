Module(body=[Print(dest=None,
                   values=[Call(func=Name(id='abs',
                                          ctx=Load()),
                                args=[Num(n=-1)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='abs',
                                          ctx=Load()),
                                args=[Num(n=1)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='abs',
                                          ctx=Load()),
                                args=[Num(n=0)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='abs',
                                          ctx=Load()),
                                args=[Num(n=-0.0)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='abs',
                                          ctx=Load()),
                                args=[Num(n=-4.6)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='abs',
                                          ctx=Load()),
                                args=[Num(n=4.6)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True)])
