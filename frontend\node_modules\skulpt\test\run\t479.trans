Module(body=[Expr(value=Call(func=Name(id='reduce',
                                       ctx=Load()),
                             args=[Lambda(args=arguments(args=[Name(id='x',
                                                                    ctx=Param()),
                                                               Name(id='y',
                                                                    ctx=Param())],
                                                         vararg=None,
                                                         kwarg=None,
                                                         defaults=[]),
                                          body=BinOp(left=Name(id='x',
                                                               ctx=Load()),
                                                     op=Add(),
                                                     right=Name(id='y',
                                                                ctx=Load()))),
                                   List(elts=[],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None))])
