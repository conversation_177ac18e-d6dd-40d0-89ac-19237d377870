Module(body=[Import(names=[alias(name='math',
                                 asname=None)]),
             Print(dest=None,
                   values=[Str(s='\nmath.ceil(x)')],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Attribute(value=Name(id='math',
                                                                       ctx=Load()),
                                                            attr='ceil',
                                                            ctx=Load()),
                                             args=[Num(n=0.0)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Num(n=0)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Attribute(value=Name(id='math',
                                                                       ctx=Load()),
                                                            attr='ceil',
                                                            ctx=Load()),
                                             args=[Num(n=-0.1)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Num(n=0)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Attribute(value=Name(id='math',
                                                                       ctx=Load()),
                                                            attr='ceil',
                                                            ctx=Load()),
                                             args=[Num(n=-0.4)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Num(n=0)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Attribute(value=Name(id='math',
                                                                       ctx=Load()),
                                                            attr='ceil',
                                                            ctx=Load()),
                                             args=[Num(n=-0.5)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Num(n=0)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Attribute(value=Name(id='math',
                                                                       ctx=Load()),
                                                            attr='ceil',
                                                            ctx=Load()),
                                             args=[Num(n=-0.9)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Num(n=0)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Attribute(value=Name(id='math',
                                                                       ctx=Load()),
                                                            attr='ceil',
                                                            ctx=Load()),
                                             args=[Num(n=0.1)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Num(n=1)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Attribute(value=Name(id='math',
                                                                       ctx=Load()),
                                                            attr='ceil',
                                                            ctx=Load()),
                                             args=[Num(n=0.4)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Num(n=1)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Attribute(value=Name(id='math',
                                                                       ctx=Load()),
                                                            attr='ceil',
                                                            ctx=Load()),
                                             args=[Num(n=0.5)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Num(n=1)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Attribute(value=Name(id='math',
                                                                       ctx=Load()),
                                                            attr='ceil',
                                                            ctx=Load()),
                                             args=[Num(n=0.9)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Num(n=1)])],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\nmath.fabs(x)')],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Attribute(value=Name(id='math',
                                                                       ctx=Load()),
                                                            attr='fabs',
                                                            ctx=Load()),
                                             args=[Num(n=-1)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Num(n=1.0)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Attribute(value=Name(id='math',
                                                                       ctx=Load()),
                                                            attr='fabs',
                                                            ctx=Load()),
                                             args=[Num(n=1)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Num(n=1.0)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Attribute(value=Name(id='math',
                                                                       ctx=Load()),
                                                            attr='fabs',
                                                            ctx=Load()),
                                             args=[Num(n=0)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Num(n=0.0)])],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\nmath.floor(x)')],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Attribute(value=Name(id='math',
                                                                       ctx=Load()),
                                                            attr='floor',
                                                            ctx=Load()),
                                             args=[Num(n=0.0)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Num(n=0)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Attribute(value=Name(id='math',
                                                                       ctx=Load()),
                                                            attr='floor',
                                                            ctx=Load()),
                                             args=[Num(n=-0.1)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Num(n=-1)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Attribute(value=Name(id='math',
                                                                       ctx=Load()),
                                                            attr='floor',
                                                            ctx=Load()),
                                             args=[Num(n=-0.4)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Num(n=-1)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Attribute(value=Name(id='math',
                                                                       ctx=Load()),
                                                            attr='floor',
                                                            ctx=Load()),
                                             args=[Num(n=-0.5)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Num(n=-1)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Attribute(value=Name(id='math',
                                                                       ctx=Load()),
                                                            attr='floor',
                                                            ctx=Load()),
                                             args=[Num(n=-0.9)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Num(n=-1)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Attribute(value=Name(id='math',
                                                                       ctx=Load()),
                                                            attr='floor',
                                                            ctx=Load()),
                                             args=[Num(n=0.1)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Num(n=0)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Attribute(value=Name(id='math',
                                                                       ctx=Load()),
                                                            attr='floor',
                                                            ctx=Load()),
                                             args=[Num(n=0.4)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Num(n=0)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Attribute(value=Name(id='math',
                                                                       ctx=Load()),
                                                            attr='floor',
                                                            ctx=Load()),
                                             args=[Num(n=0.5)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Num(n=0)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Attribute(value=Name(id='math',
                                                                       ctx=Load()),
                                                            attr='floor',
                                                            ctx=Load()),
                                             args=[Num(n=0.9)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Num(n=0)])],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\nmath.trunc(x)')],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Attribute(value=Name(id='math',
                                                                       ctx=Load()),
                                                            attr='trunc',
                                                            ctx=Load()),
                                             args=[Num(n=12.34)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Num(n=12)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Attribute(value=Name(id='math',
                                                                       ctx=Load()),
                                                            attr='trunc',
                                                            ctx=Load()),
                                             args=[Num(n=-12.34)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Num(n=-12)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Attribute(value=Name(id='math',
                                                                       ctx=Load()),
                                                            attr='trunc',
                                                            ctx=Load()),
                                             args=[Num(n=567000000.0)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Num(n=567000000)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Attribute(value=Name(id='math',
                                                                       ctx=Load()),
                                                            attr='trunc',
                                                            ctx=Load()),
                                             args=[Num(n=-567000000.0)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Num(n=-567000000)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Attribute(value=Name(id='math',
                                                                       ctx=Load()),
                                                            attr='trunc',
                                                            ctx=Load()),
                                             args=[Num(n=5.67e-08)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Num(n=0)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Attribute(value=Name(id='math',
                                                                       ctx=Load()),
                                                            attr='trunc',
                                                            ctx=Load()),
                                             args=[Num(n=-5.67e-08)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Num(n=0)])],
                   nl=True)])
