Module(body=[Print(dest=None,
                   values=[Str(s='One')],
                   nl=True),
             If(test=Compare(left=Num(n=1),
                             ops=[Gt()],
                             comparators=[Num(n=10)]),
                body=[Expr(value=Name(id='exit',
                                      ctx=Load()))],
                orelse=[]),
             Print(dest=None,
                   values=[Str(s='Two')],
                   nl=True),
             If(test=Compare(left=Num(n=1),
                             ops=[Gt()],
                             comparators=[Num(n=10)]),
                body=[Expr(value=Name(id='quit',
                                      ctx=Load()))],
                orelse=[]),
             Print(dest=None,
                   values=[Str(s='Three')],
                   nl=True),
             If(test=Compare(left=Num(n=1),
                             ops=[Gt()],
                             comparators=[Num(n=10)]),
                body=[Expr(value=Call(func=Name(id='exit',
                                                ctx=Load()),
                                      args=[Num(n=-1)],
                                      keywords=[],
                                      starargs=None,
                                      kwargs=None))],
                orelse=[]),
             Print(dest=None,
                   values=[Str(s='Four')],
                   nl=True),
             If(test=Compare(left=Num(n=1),
                             ops=[Gt()],
                             comparators=[Num(n=10)]),
                body=[Expr(value=Call(func=Name(id='quit',
                                                ctx=Load()),
                                      args=[Num(n=42)],
                                      keywords=[],
                                      starargs=None,
                                      kwargs=None))],
                orelse=[]),
             Print(dest=None,
                   values=[Str(s='Five')],
                   nl=True),
             If(test=Compare(left=Num(n=1),
                             ops=[Lt()],
                             comparators=[Num(n=10)]),
                body=[Expr(value=Call(func=Name(id='quit',
                                                ctx=Load()),
                                      args=[],
                                      keywords=[],
                                      starargs=None,
                                      kwargs=None))],
                orelse=[]),
             Print(dest=None,
                   values=[Str(s='This should never appear')],
                   nl=True)])
