Module(body=[Print(dest=None,
                   values=[Str(s='----Start 01')],
                   nl=True),
             TryExcept(body=[Print(dest=None,
                                   values=[Str(s='First try')],
                                   nl=True),
                             TryExcept(body=[Print(dest=None,
                                                   values=[Str(s='Second try - should see Second except next')],
                                                   nl=True),
                                             Assign(targets=[Name(id='i',
                                                                  ctx=Store())],
                                                    value=Call(func=Name(id='int',
                                                                         ctx=Load()),
                                                               args=[Str(s='badint')],
                                                               keywords=[],
                                                               starargs=None,
                                                               kwargs=None)),
                                             Print(dest=None,
                                                   values=[Str(s='Second try - should not see this')],
                                                   nl=True)],
                                       handlers=[ExceptHandler(type=None,
                                                               name=None,
                                                               body=[Print(dest=None,
                                                                           values=[Str(s='Second except')],
                                                                           nl=True)])],
                                       orelse=[]),
                             Print(dest=None,
                                   values=[Str(s='First try - should see First except next')],
                                   nl=True),
                             Assign(targets=[Name(id='i',
                                                  ctx=Store())],
                                    value=Call(func=Name(id='float',
                                                         ctx=Load()),
                                               args=[Str(s='otherbadint')],
                                               keywords=[],
                                               starargs=None,
                                               kwargs=None)),
                             Print(dest=None,
                                   values=[Str(s='First try - should not see this')],
                                   nl=True)],
                       handlers=[ExceptHandler(type=None,
                                               name=None,
                                               body=[Print(dest=None,
                                                           values=[Str(s='First except')],
                                                           nl=True)])],
                       orelse=[]),
             Print(dest=None,
                   values=[Str(s='----End 01')],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='----Start 02')],
                   nl=True),
             TryExcept(body=[Print(dest=None,
                                   values=[Str(s='First try')],
                                   nl=True),
                             TryExcept(body=[Print(dest=None,
                                                   values=[Str(s='Second try')],
                                                   nl=True)],
                                       handlers=[ExceptHandler(type=None,
                                                               name=None,
                                                               body=[Print(dest=None,
                                                                           values=[Str(s='Second except - should not see this')],
                                                                           nl=True)])],
                                       orelse=[]),
                             Print(dest=None,
                                   values=[Str(s='First try - should see First except next')],
                                   nl=True),
                             Assign(targets=[Name(id='i',
                                                  ctx=Store())],
                                    value=Call(func=Name(id='float',
                                                         ctx=Load()),
                                               args=[Str(s='otherbadint')],
                                               keywords=[],
                                               starargs=None,
                                               kwargs=None)),
                             Print(dest=None,
                                   values=[Str(s='First try - should not see this')],
                                   nl=True)],
                       handlers=[ExceptHandler(type=None,
                                               name=None,
                                               body=[Print(dest=None,
                                                           values=[Str(s='First except')],
                                                           nl=True)])],
                       orelse=[]),
             Print(dest=None,
                   values=[Str(s='----End 02')],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='----Start 03')],
                   nl=True),
             TryExcept(body=[Print(dest=None,
                                   values=[Str(s='First try')],
                                   nl=True),
                             TryExcept(body=[Print(dest=None,
                                                   values=[Str(s='Second try')],
                                                   nl=True)],
                                       handlers=[ExceptHandler(type=None,
                                                               name=None,
                                                               body=[Print(dest=None,
                                                                           values=[Str(s='Second except - should not see this')],
                                                                           nl=True)])],
                                       orelse=[]),
                             Print(dest=None,
                                   values=[Str(s='First try - after inner try')],
                                   nl=True)],
                       handlers=[ExceptHandler(type=None,
                                               name=None,
                                               body=[Print(dest=None,
                                                           values=[Str(s='First except - should not see this')],
                                                           nl=True)])],
                       orelse=[]),
             Print(dest=None,
                   values=[Str(s='----End 03')],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='----Start 04')],
                   nl=True),
             TryExcept(body=[Print(dest=None,
                                   values=[Str(s='First try - shuold see First Except next')],
                                   nl=True),
                             Assign(targets=[Name(id='i',
                                                  ctx=Store())],
                                    value=Call(func=Name(id='int',
                                                         ctx=Load()),
                                               args=[Str(s='first')],
                                               keywords=[],
                                               starargs=None,
                                               kwargs=None)),
                             Print(dest=None,
                                   values=[Str(s='First try - should not see this')],
                                   nl=True)],
                       handlers=[ExceptHandler(type=None,
                                               name=None,
                                               body=[Print(dest=None,
                                                           values=[Str(s='First except')],
                                                           nl=True),
                                                     TryExcept(body=[Print(dest=None,
                                                                           values=[Str(s='Second try - should see Second except next')],
                                                                           nl=True),
                                                                     Assign(targets=[Name(id='i',
                                                                                          ctx=Store())],
                                                                            value=Call(func=Name(id='int',
                                                                                                 ctx=Load()),
                                                                                       args=[Str(s='badint')],
                                                                                       keywords=[],
                                                                                       starargs=None,
                                                                                       kwargs=None)),
                                                                     Print(dest=None,
                                                                           values=[Str(s='Second try - should not see this')],
                                                                           nl=True)],
                                                               handlers=[ExceptHandler(type=None,
                                                                                       name=None,
                                                                                       body=[Print(dest=None,
                                                                                                   values=[Str(s='Second except')],
                                                                                                   nl=True)])],
                                                               orelse=[]),
                                                     Print(dest=None,
                                                           values=[Str(s='First except - After inner try/except')],
                                                           nl=True)])],
                       orelse=[]),
             Print(dest=None,
                   values=[Str(s='----End 04')],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='----Start 05')],
                   nl=True),
             TryExcept(body=[Print(dest=None,
                                   values=[Str(s='First try')],
                                   nl=True),
                             TryExcept(body=[Print(dest=None,
                                                   values=[Str(s='Second try - should see Second except next')],
                                                   nl=True),
                                             Assign(targets=[Name(id='i',
                                                                  ctx=Store())],
                                                    value=Call(func=Name(id='int',
                                                                         ctx=Load()),
                                                               args=[Str(s='badint')],
                                                               keywords=[],
                                                               starargs=None,
                                                               kwargs=None)),
                                             Print(dest=None,
                                                   values=[Str(s='Second try - should not see this')],
                                                   nl=True)],
                                       handlers=[ExceptHandler(type=None,
                                                               name=None,
                                                               body=[Print(dest=None,
                                                                           values=[Str(s='Second except - should see First except next')],
                                                                           nl=True),
                                                                     Assign(targets=[Name(id='i',
                                                                                          ctx=Store())],
                                                                            value=Call(func=Name(id='float',
                                                                                                 ctx=Load()),
                                                                                       args=[Str(s='otherbadint')],
                                                                                       keywords=[],
                                                                                       starargs=None,
                                                                                       kwargs=None)),
                                                                     Print(dest=None,
                                                                           values=[Str(s='Second except - should not see this')],
                                                                           nl=True)])],
                                       orelse=[]),
                             Print(dest=None,
                                   values=[Str(s='First try - should not see this')],
                                   nl=True)],
                       handlers=[ExceptHandler(type=None,
                                               name=None,
                                               body=[Print(dest=None,
                                                           values=[Str(s='First except')],
                                                           nl=True)])],
                       orelse=[]),
             Print(dest=None,
                   values=[Str(s='----End 05')],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='----Start 06')],
                   nl=True),
             TryExcept(body=[Print(dest=None,
                                   values=[Str(s='First try')],
                                   nl=True),
                             If(test=Compare(left=Num(n=123),
                                             ops=[Lt()],
                                             comparators=[Num(n=12345)]),
                                body=[If(test=Compare(left=Num(n=456),
                                                      ops=[Lt()],
                                                      comparators=[Num(n=4567)]),
                                         body=[Print(dest=None,
                                                     values=[Str(s='You should see this')],
                                                     nl=True)],
                                         orelse=[Print(dest=None,
                                                       values=[Str(s='You should not see this (inner)')],
                                                       nl=True)])],
                                orelse=[Print(dest=None,
                                              values=[Str(s='You should not see this')],
                                              nl=True)]),
                             Print(dest=None,
                                   values=[Str(s='First try - near the end')],
                                   nl=True)],
                       handlers=[ExceptHandler(type=None,
                                               name=None,
                                               body=[Print(dest=None,
                                                           values=[Str(s='First except - should not see this')],
                                                           nl=True)])],
                       orelse=[]),
             Print(dest=None,
                   values=[Str(s='----End 06')],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='----Start 07')],
                   nl=True),
             TryExcept(body=[Print(dest=None,
                                   values=[Str(s='First try')],
                                   nl=True),
                             If(test=Compare(left=Num(n=123),
                                             ops=[Lt()],
                                             comparators=[Num(n=12345)]),
                                body=[If(test=Compare(left=Num(n=456),
                                                      ops=[Lt()],
                                                      comparators=[Num(n=4567)]),
                                         body=[Print(dest=None,
                                                     values=[Str(s='Next you should see First except')],
                                                     nl=True),
                                               Assign(targets=[Name(id='i',
                                                                    ctx=Store())],
                                                      value=Call(func=Name(id='int',
                                                                           ctx=Load()),
                                                                 args=[Str(s='badint')],
                                                                 keywords=[],
                                                                 starargs=None,
                                                                 kwargs=None))],
                                         orelse=[Print(dest=None,
                                                       values=[Str(s='You should not see this (inner)')],
                                                       nl=True)])],
                                orelse=[Print(dest=None,
                                              values=[Str(s='You should not see this')],
                                              nl=True)]),
                             Print(dest=None,
                                   values=[Str(s='First try - near the end - you should not see this')],
                                   nl=True)],
                       handlers=[ExceptHandler(type=None,
                                               name=None,
                                               body=[Print(dest=None,
                                                           values=[Str(s='First except - should see this')],
                                                           nl=True)])],
                       orelse=[]),
             Print(dest=None,
                   values=[Str(s='----End 07')],
                   nl=True)])
