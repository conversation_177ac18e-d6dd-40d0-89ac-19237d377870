Module(body=[Import(names=[alias(name='random',
                                 asname=None)]),
             Print(dest=None,
                   values=[Call(func=Name(id='type',
                                          ctx=Load()),
                                args=[Call(func=Attribute(value=Name(id='random',
                                                                     ctx=Load()),
                                                          attr='random',
                                                          ctx=Load()),
                                           args=[],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='type',
                                          ctx=Load()),
                                args=[Call(func=Attribute(value=Name(id='random',
                                                                     ctx=Load()),
                                                          attr='randrange',
                                                          ctx=Load()),
                                           args=[Num(n=10)],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='type',
                                          ctx=Load()),
                                args=[Call(func=Attribute(value=Name(id='random',
                                                                     ctx=Load()),
                                                          attr='randrange',
                                                          ctx=Load()),
                                           args=[Num(n=1),
                                                 Num(n=10)],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='type',
                                          ctx=Load()),
                                args=[Call(func=Attribute(value=Name(id='random',
                                                                     ctx=Load()),
                                                          attr='randrange',
                                                          ctx=Load()),
                                           args=[Num(n=1),
                                                 Num(n=10),
                                                 Num(n=3)],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True)])
