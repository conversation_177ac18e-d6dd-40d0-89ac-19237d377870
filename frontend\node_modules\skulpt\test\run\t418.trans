Module(body=[Print(dest=None,
                   values=[Str(s='\nlists')],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='min',
                                          ctx=Load()),
                                args=[List(elts=[Num(n=1),
                                                 Num(n=2),
                                                 Num(n=3),
                                                 Num(n=4)],
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='min',
                                          ctx=Load()),
                                args=[List(elts=[Num(n=2),
                                                 Num(n=1)],
                                           ctx=Load()),
                                      List(elts=[Num(n=1),
                                                 Num(n=2)],
                                           ctx=Load()),
                                      List(elts=[Num(n=1),
                                                 Num(n=1)],
                                           ctx=Load()),
                                      List(elts=[Num(n=1),
                                                 Num(n=1),
                                                 Num(n=0)],
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\ntuples')],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='min',
                                          ctx=Load()),
                                args=[Tuple(elts=[Num(n=1),
                                                  Num(n=2),
                                                  Num(n=3),
                                                  Num(n=4)],
                                            ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='min',
                                          ctx=Load()),
                                args=[Tuple(elts=[Num(n=2),
                                                  Num(n=1)],
                                            ctx=Load()),
                                      Tuple(elts=[Num(n=1),
                                                  Num(n=2)],
                                            ctx=Load()),
                                      Tuple(elts=[Num(n=1),
                                                  Num(n=1)],
                                            ctx=Load()),
                                      Tuple(elts=[Num(n=1),
                                                  Num(n=1),
                                                  Num(n=0)],
                                            ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\ndictionaries')],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='min',
                                          ctx=Load()),
                                args=[Dict(keys=[Num(n=1),
                                                 Num(n=3),
                                                 Num(n=5)],
                                           values=[Num(n=2),
                                                   Num(n=4),
                                                   Num(n=6)])],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='min',
                                          ctx=Load()),
                                args=[Dict(keys=[Num(n=1),
                                                 Num(n=3),
                                                 Num(n=5)],
                                           values=[Num(n=6),
                                                   Num(n=4),
                                                   Num(n=2)])],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True)])
