import React from 'react';
import { useAuthStore } from '../store/authStore';
import { useLessonStore } from '../store/lessonStore';
import { 
  User, 
  Mail, 
  Star, 
  Trophy, 
  BookOpen, 
  Calendar,
  TrendingUp,
  Award,
  Clock,
  Target
} from 'lucide-react';

const Profile: React.FC = () => {
  const { user } = useAuthStore();
  const { progress } = useLessonStore();

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="text-6xl mb-4">🤔</div>
          <h2 className="text-kid-xl font-bold text-gray-900 mb-2">No user found</h2>
          <p className="text-gray-600">Please log in to view your profile.</p>
        </div>
      </div>
    );
  }

  const completedLessons = progress.filter(p => p.completed).length;
  const totalTimeSpent = progress.reduce((total, p) => total + (p.timeSpent || 0), 0);
  const averageScore = progress.length > 0 
    ? Math.round(progress.reduce((total, p) => total + (p.score || 0), 0) / progress.length)
    : 0;

  const badges = [
    { name: 'First Steps', icon: '👶', description: 'Completed your first lesson', earned: completedLessons >= 1 },
    { name: 'Getting Started', icon: '🚀', description: 'Completed 3 lessons', earned: completedLessons >= 3 },
    { name: 'Python Explorer', icon: '🔍', description: 'Completed 5 lessons', earned: completedLessons >= 5 },
    { name: 'Code Warrior', icon: '⚔️', description: 'Completed 10 lessons', earned: completedLessons >= 10 },
    { name: 'Perfect Score', icon: '💯', description: 'Got 100% on a lesson', earned: progress.some(p => p.score === 100) },
    { name: 'Speed Demon', icon: '⚡', description: 'Completed a lesson in under 5 minutes', earned: progress.some(p => p.timeSpent < 5) },
    { name: 'Persistent', icon: '🎯', description: 'Completed 5 lessons in a row', earned: user.currentStreak >= 5 },
    { name: 'Python Master', icon: '🏆', description: 'Completed all lessons', earned: completedLessons >= 20 },
  ];

  const earnedBadges = badges.filter(badge => badge.earned);
  const recentActivity = progress
    .filter(p => p.completedAt)
    .sort((a, b) => new Date(b.completedAt!).getTime() - new Date(a.completedAt!).getTime())
    .slice(0, 5);

  return (
    <div className="min-h-screen py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Profile Header */}
        <div className="card mb-8">
          <div className="flex items-center space-x-6">
            <div className="w-24 h-24 bg-gradient-to-br from-primary-400 to-secondary-400 rounded-kid-xl flex items-center justify-center">
              <User className="text-white" size={48} />
            </div>
            
            <div className="flex-1">
              <h1 className="text-kid-3xl font-bold text-gray-900 mb-2">
                {user.username}
              </h1>
              <div className="flex items-center space-x-2 text-gray-600 mb-4">
                <Mail size={16} />
                <span>{user.email || 'Guest User'}</span>
              </div>
              
              <div className="flex items-center space-x-6">
                <div className="flex items-center space-x-2">
                  <Star className="text-yellow-500" size={20} />
                  <span className="font-bold text-gray-900">{user.points || 0} points</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Trophy className="text-warning-500" size={20} />
                  <span className="font-bold text-gray-900">{earnedBadges.length} badges</span>
                </div>
                <div className="flex items-center space-x-2">
                  <TrendingUp className="text-success-500" size={20} />
                  <span className="font-bold text-gray-900">{user.currentStreak || 0} day streak</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Stats Overview */}
          <div className="lg:col-span-2 space-y-8">
            {/* Learning Stats */}
            <div className="card">
              <h2 className="text-kid-xl font-bold text-gray-900 mb-6 flex items-center">
                <Target className="mr-2 text-primary-500" size={24} />
                Learning Statistics
              </h2>
              
              <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                <div className="text-center">
                  <div className="w-16 h-16 bg-primary-100 rounded-kid-xl mx-auto mb-3 flex items-center justify-center">
                    <BookOpen className="text-primary-600" size={32} />
                  </div>
                  <div className="text-2xl font-bold text-gray-900 mb-1">{completedLessons}</div>
                  <div className="text-gray-600 text-kid-sm">Lessons Completed</div>
                </div>
                
                <div className="text-center">
                  <div className="w-16 h-16 bg-secondary-100 rounded-kid-xl mx-auto mb-3 flex items-center justify-center">
                    <Clock className="text-secondary-600" size={32} />
                  </div>
                  <div className="text-2xl font-bold text-gray-900 mb-1">{totalTimeSpent}</div>
                  <div className="text-gray-600 text-kid-sm">Minutes Learned</div>
                </div>
                
                <div className="text-center">
                  <div className="w-16 h-16 bg-success-100 rounded-kid-xl mx-auto mb-3 flex items-center justify-center">
                    <Target className="text-success-600" size={32} />
                  </div>
                  <div className="text-2xl font-bold text-gray-900 mb-1">{averageScore}%</div>
                  <div className="text-gray-600 text-kid-sm">Average Score</div>
                </div>
                
                <div className="text-center">
                  <div className="w-16 h-16 bg-warning-100 rounded-kid-xl mx-auto mb-3 flex items-center justify-center">
                    <TrendingUp className="text-warning-600" size={32} />
                  </div>
                  <div className="text-2xl font-bold text-gray-900 mb-1">{user.level || 1}</div>
                  <div className="text-gray-600 text-kid-sm">Current Level</div>
                </div>
              </div>
            </div>

            {/* Recent Activity */}
            <div className="card">
              <h2 className="text-kid-xl font-bold text-gray-900 mb-6 flex items-center">
                <Calendar className="mr-2 text-secondary-500" size={24} />
                Recent Activity
              </h2>
              
              {recentActivity.length > 0 ? (
                <div className="space-y-4">
                  {recentActivity.map((activity, index) => (
                    <div key={index} className="flex items-center space-x-4 p-4 bg-gray-50 rounded-kid">
                      <div className="w-10 h-10 bg-success-100 rounded-kid flex items-center justify-center">
                        <BookOpen className="text-success-600" size={20} />
                      </div>
                      <div className="flex-1">
                        <h3 className="font-medium text-gray-900">Completed Lesson {activity.lessonId}</h3>
                        <div className="flex items-center space-x-4 text-kid-sm text-gray-600">
                          <span>Score: {activity.score}%</span>
                          <span>Time: {activity.timeSpent} min</span>
                          <span>{new Date(activity.completedAt!).toLocaleDateString()}</span>
                        </div>
                      </div>
                      <div className="flex items-center space-x-1 text-success-600">
                        <Star size={16} />
                        <span className="font-medium">+10</span>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <BookOpen size={48} className="mx-auto mb-3 opacity-50" />
                  <p>No activity yet</p>
                  <p className="text-kid-sm">Complete some lessons to see your progress here!</p>
                </div>
              )}
            </div>
          </div>

          {/* Badges */}
          <div className="space-y-8">
            <div className="card">
              <h2 className="text-kid-xl font-bold text-gray-900 mb-6 flex items-center">
                <Award className="mr-2 text-warning-500" size={24} />
                Badges ({earnedBadges.length}/{badges.length})
              </h2>
              
              <div className="grid grid-cols-2 gap-4">
                {badges.map((badge, index) => (
                  <div 
                    key={index}
                    className={`text-center p-4 rounded-kid-lg border-2 transition-all duration-200 ${
                      badge.earned 
                        ? 'border-warning-200 bg-warning-50 shadow-kid' 
                        : 'border-gray-200 bg-gray-50 opacity-50'
                    }`}
                  >
                    <div className="text-3xl mb-2">{badge.icon}</div>
                    <h3 className="font-medium text-gray-900 text-kid-sm mb-1">{badge.name}</h3>
                    <p className="text-kid-xs text-gray-600 mb-2">{badge.description}</p>
                    {badge.earned ? (
                      <div className="text-success-600 text-kid-xs font-medium">Earned! ✓</div>
                    ) : (
                      <div className="text-gray-400 text-kid-xs">Not earned yet</div>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* Quick Actions */}
            <div className="card">
              <h2 className="text-kid-xl font-bold text-gray-900 mb-4">Quick Actions</h2>
              <div className="space-y-3">
                <button className="w-full btn-primary text-left">
                  <BookOpen className="mr-2" size={16} />
                  Continue Learning
                </button>
                <button className="w-full btn-secondary text-left">
                  <Trophy className="mr-2" size={16} />
                  View Dashboard
                </button>
                <button className="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-3 px-4 rounded-kid-lg transition-colors text-left">
                  <User className="mr-2" size={16} />
                  Edit Profile
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Profile;
