Module(body=[Assign(targets=[Name(id='a',
                                  ctx=Store())],
                    value=List(elts=[Num(n=100),
                                     Num(n=101),
                                     Num(n=102),
                                     Num(n=103),
                                     Num(n=104),
                                     Num(n=105),
                                     Num(n=106),
                                     Num(n=107)],
                               ctx=Load())),
             Delete(targets=[Subscript(value=Name(id='a',
                                                  ctx=Load()),
                                       slice=Index(value=Num(n=0)),
                                       ctx=Del())]),
             Print(dest=None,
                   values=[Name(id='a',
                                ctx=Load())],
                   nl=True)])
