Module(body=[Assign(targets=[Name(id='i',
                                  ctx=Store())],
                    value=Str(s='(')),
             Print(dest=None,
                   values=[Compare(left=Name(id='i',
                                             ctx=Load()),
                                   ops=[NotIn()],
                                   comparators=[Str(s='+-*/)')])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='i',
                                             ctx=Load()),
                                   ops=[In()],
                                   comparators=[Str(s='+-*/')])],
                   nl=True)])
