Module(body=[Print(dest=None,
                   values=[Str(s='\nSTRINGS')],
                   nl=True),
             Assign(targets=[Name(id='s',
                                  ctx=Store())],
                    value=Str(s='hello')),
             Print(dest=None,
                   values=[Compare(left=Compare(left=Str(s='h'),
                                                ops=[In()],
                                                comparators=[Name(id='s',
                                                                  ctx=Load())]),
                                   ops=[Eq()],
                                   comparators=[Name(id='True',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Compare(left=Str(s='H'),
                                                ops=[In()],
                                                comparators=[Name(id='s',
                                                                  ctx=Load())]),
                                   ops=[Eq()],
                                   comparators=[Name(id='False',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Compare(left=Str(s='e'),
                                                ops=[NotIn()],
                                                comparators=[Name(id='s',
                                                                  ctx=Load())]),
                                   ops=[Eq()],
                                   comparators=[Name(id='False',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Compare(left=Str(s='L'),
                                                ops=[NotIn()],
                                                comparators=[Name(id='s',
                                                                  ctx=Load())]),
                                   ops=[Eq()],
                                   comparators=[Name(id='True',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=BinOp(left=Str(s='hello'),
                                              op=Add(),
                                              right=Str(s=' world')),
                                   ops=[Eq()],
                                   comparators=[Str(s='hello world')])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=BinOp(left=Str(s='a'),
                                              op=Mult(),
                                              right=Num(n=3)),
                                   ops=[Eq()],
                                   comparators=[Str(s='aaa')])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=BinOp(left=Num(n=2),
                                              op=Mult(),
                                              right=Str(s='hello')),
                                   ops=[Eq()],
                                   comparators=[Str(s='hellohello')])],
                   nl=True),
             Assign(targets=[Name(id='s',
                                  ctx=Store())],
                    value=Str(s='01234')),
             Print(dest=None,
                   values=[Compare(left=Subscript(value=Name(id='s',
                                                             ctx=Load()),
                                                  slice=Index(value=Num(n=4)),
                                                  ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Str(s='4')])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Subscript(value=Name(id='s',
                                                             ctx=Load()),
                                                  slice=Index(value=Num(n=-1)),
                                                  ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Str(s='4')])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Subscript(value=Name(id='s',
                                                             ctx=Load()),
                                                  slice=Slice(lower=Num(n=0),
                                                              upper=Num(n=3),
                                                              step=None),
                                                  ctx=Load()),
                                   ops=[Eq(),
                                        Eq(),
                                        Eq()],
                                   comparators=[Subscript(value=Name(id='s',
                                                                     ctx=Load()),
                                                          slice=Slice(lower=None,
                                                                      upper=Num(n=3),
                                                                      step=None),
                                                          ctx=Load()),
                                                Subscript(value=Name(id='s',
                                                                     ctx=Load()),
                                                          slice=Slice(lower=Name(id='None',
                                                                                 ctx=Load()),
                                                                      upper=Num(n=3),
                                                                      step=None),
                                                          ctx=Load()),
                                                Str(s='012')])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Subscript(value=Name(id='s',
                                                             ctx=Load()),
                                                  slice=Slice(lower=Num(n=0),
                                                              upper=None,
                                                              step=None),
                                                  ctx=Load()),
                                   ops=[Eq(),
                                        Eq(),
                                        Eq()],
                                   comparators=[Subscript(value=Name(id='s',
                                                                     ctx=Load()),
                                                          slice=Slice(lower=Num(n=0),
                                                                      upper=Name(id='None',
                                                                                 ctx=Load()),
                                                                      step=None),
                                                          ctx=Load()),
                                                Subscript(value=Name(id='s',
                                                                     ctx=Load()),
                                                          slice=Slice(lower=None,
                                                                      upper=None,
                                                                      step=None),
                                                          ctx=Load()),
                                                Str(s='01234')])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Subscript(value=Name(id='s',
                                                             ctx=Load()),
                                                  slice=Slice(lower=Num(n=1),
                                                              upper=Num(n=3),
                                                              step=None),
                                                  ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Str(s='12')])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Subscript(value=Name(id='s',
                                                             ctx=Load()),
                                                  slice=Slice(lower=Num(n=-1),
                                                              upper=Num(n=3),
                                                              step=None),
                                                  ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Str(s='')])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Subscript(value=Name(id='s',
                                                             ctx=Load()),
                                                  slice=Slice(lower=Num(n=-3),
                                                              upper=Num(n=3),
                                                              step=None),
                                                  ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Str(s='2')])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Subscript(value=Name(id='s',
                                                             ctx=Load()),
                                                  slice=Slice(lower=Num(n=-4),
                                                              upper=Num(n=-1),
                                                              step=None),
                                                  ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Str(s='123')])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Subscript(value=Name(id='s',
                                                             ctx=Load()),
                                                  slice=Slice(lower=Num(n=0),
                                                              upper=Num(n=5),
                                                              step=Num(n=1)),
                                                  ctx=Load()),
                                   ops=[Eq(),
                                        Eq(),
                                        Eq(),
                                        Eq(),
                                        Eq(),
                                        Eq(),
                                        Eq(),
                                        Eq()],
                                   comparators=[Subscript(value=Name(id='s',
                                                                     ctx=Load()),
                                                          slice=Slice(lower=None,
                                                                      upper=Num(n=5),
                                                                      step=Num(n=1)),
                                                          ctx=Load()),
                                                Subscript(value=Name(id='s',
                                                                     ctx=Load()),
                                                          slice=Slice(lower=Num(n=0),
                                                                      upper=None,
                                                                      step=Num(n=1)),
                                                          ctx=Load()),
                                                Subscript(value=Name(id='s',
                                                                     ctx=Load()),
                                                          slice=Slice(lower=Num(n=0),
                                                                      upper=Num(n=5),
                                                                      step=Name(id='None',
                                                                                ctx=Load())),
                                                          ctx=Load()),
                                                Subscript(value=Name(id='s',
                                                                     ctx=Load()),
                                                          slice=Slice(lower=Num(n=0),
                                                                      upper=None,
                                                                      step=Name(id='None',
                                                                                ctx=Load())),
                                                          ctx=Load()),
                                                Subscript(value=Name(id='s',
                                                                     ctx=Load()),
                                                          slice=Slice(lower=None,
                                                                      upper=Num(n=5),
                                                                      step=Name(id='None',
                                                                                ctx=Load())),
                                                          ctx=Load()),
                                                Subscript(value=Name(id='s',
                                                                     ctx=Load()),
                                                          slice=Slice(lower=None,
                                                                      upper=None,
                                                                      step=Num(n=1)),
                                                          ctx=Load()),
                                                Subscript(value=Name(id='s',
                                                                     ctx=Load()),
                                                          slice=Slice(lower=None,
                                                                      upper=None,
                                                                      step=Name(id='None',
                                                                                ctx=Load())),
                                                          ctx=Load()),
                                                Str(s='01234')])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Subscript(value=Name(id='s',
                                                             ctx=Load()),
                                                  slice=Slice(lower=None,
                                                              upper=None,
                                                              step=Num(n=-1)),
                                                  ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Str(s='43210')])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Subscript(value=Name(id='s',
                                                             ctx=Load()),
                                                  slice=Slice(lower=Num(n=4),
                                                              upper=Num(n=2),
                                                              step=Num(n=-1)),
                                                  ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Str(s='43')])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Subscript(value=Name(id='s',
                                                             ctx=Load()),
                                                  slice=Slice(lower=Num(n=-1),
                                                              upper=Num(n=2),
                                                              step=Num(n=-2)),
                                                  ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Str(s='4')])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Name(id='len',
                                                       ctx=Load()),
                                             args=[Name(id='s',
                                                        ctx=Load())],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Num(n=5)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Name(id='min',
                                                       ctx=Load()),
                                             args=[Name(id='s',
                                                        ctx=Load())],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Str(s='0')])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Name(id='max',
                                                       ctx=Load()),
                                             args=[Name(id='s',
                                                        ctx=Load())],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Str(s='4')])],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\nLISTS')],
                   nl=True),
             Assign(targets=[Name(id='l',
                                  ctx=Store())],
                    value=List(elts=[Num(n=0),
                                     Num(n=1),
                                     Num(n=2),
                                     Num(n=3),
                                     Num(n=4)],
                               ctx=Load())),
             Print(dest=None,
                   values=[Compare(left=Compare(left=Num(n=0),
                                                ops=[In()],
                                                comparators=[Name(id='l',
                                                                  ctx=Load())]),
                                   ops=[Eq()],
                                   comparators=[Name(id='True',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Compare(left=Num(n=5),
                                                ops=[In()],
                                                comparators=[Name(id='l',
                                                                  ctx=Load())]),
                                   ops=[Eq()],
                                   comparators=[Name(id='False',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Compare(left=Num(n=4),
                                                ops=[NotIn()],
                                                comparators=[Name(id='l',
                                                                  ctx=Load())]),
                                   ops=[Eq()],
                                   comparators=[Name(id='False',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Compare(left=Str(s='hello'),
                                                ops=[NotIn()],
                                                comparators=[Name(id='l',
                                                                  ctx=Load())]),
                                   ops=[Eq()],
                                   comparators=[Name(id='True',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=BinOp(left=List(elts=[Num(n=0),
                                                              Num(n=1),
                                                              Num(n=2)],
                                                        ctx=Load()),
                                              op=Add(),
                                              right=List(elts=[Num(n=3),
                                                               Num(n=4)],
                                                         ctx=Load())),
                                   ops=[Eq()],
                                   comparators=[Name(id='l',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=BinOp(left=List(elts=[Num(n=0)],
                                                        ctx=Load()),
                                              op=Mult(),
                                              right=Num(n=3)),
                                   ops=[Eq()],
                                   comparators=[List(elts=[Num(n=0),
                                                           Num(n=0),
                                                           Num(n=0)],
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=BinOp(left=Num(n=2),
                                              op=Mult(),
                                              right=List(elts=[Num(n=1),
                                                               Num(n=2)],
                                                         ctx=Load())),
                                   ops=[Eq()],
                                   comparators=[List(elts=[Num(n=1),
                                                           Num(n=2),
                                                           Num(n=1),
                                                           Num(n=2)],
                                                     ctx=Load())])],
                   nl=True),
             Assign(targets=[Name(id='l2',
                                  ctx=Store())],
                    value=BinOp(left=List(elts=[List(elts=[],
                                                     ctx=Load())],
                                          ctx=Load()),
                                op=Mult(),
                                right=Num(n=3))),
             Expr(value=Call(func=Attribute(value=Subscript(value=Name(id='l2',
                                                                       ctx=Load()),
                                                            slice=Index(value=Num(n=0)),
                                                            ctx=Load()),
                                            attr='append',
                                            ctx=Load()),
                             args=[Num(n=3)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Compare(left=Name(id='l2',
                                             ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[List(elts=[List(elts=[Num(n=3)],
                                                                ctx=Load()),
                                                           List(elts=[Num(n=3)],
                                                                ctx=Load()),
                                                           List(elts=[Num(n=3)],
                                                                ctx=Load())],
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Subscript(value=Name(id='l',
                                                             ctx=Load()),
                                                  slice=Index(value=Num(n=4)),
                                                  ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Num(n=4)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Subscript(value=Name(id='l',
                                                             ctx=Load()),
                                                  slice=Index(value=Num(n=-1)),
                                                  ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Num(n=4)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Subscript(value=Name(id='l',
                                                             ctx=Load()),
                                                  slice=Slice(lower=Num(n=0),
                                                              upper=Num(n=3),
                                                              step=None),
                                                  ctx=Load()),
                                   ops=[Eq(),
                                        Eq(),
                                        Eq()],
                                   comparators=[Subscript(value=Name(id='l',
                                                                     ctx=Load()),
                                                          slice=Slice(lower=None,
                                                                      upper=Num(n=3),
                                                                      step=None),
                                                          ctx=Load()),
                                                Subscript(value=Name(id='l',
                                                                     ctx=Load()),
                                                          slice=Slice(lower=Name(id='None',
                                                                                 ctx=Load()),
                                                                      upper=Num(n=3),
                                                                      step=None),
                                                          ctx=Load()),
                                                List(elts=[Num(n=0),
                                                           Num(n=1),
                                                           Num(n=2)],
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Subscript(value=Name(id='l',
                                                             ctx=Load()),
                                                  slice=Slice(lower=Num(n=0),
                                                              upper=None,
                                                              step=None),
                                                  ctx=Load()),
                                   ops=[Eq(),
                                        Eq(),
                                        Eq()],
                                   comparators=[Subscript(value=Name(id='l',
                                                                     ctx=Load()),
                                                          slice=Slice(lower=Num(n=0),
                                                                      upper=Name(id='None',
                                                                                 ctx=Load()),
                                                                      step=None),
                                                          ctx=Load()),
                                                Subscript(value=Name(id='l',
                                                                     ctx=Load()),
                                                          slice=Slice(lower=None,
                                                                      upper=None,
                                                                      step=None),
                                                          ctx=Load()),
                                                List(elts=[Num(n=0),
                                                           Num(n=1),
                                                           Num(n=2),
                                                           Num(n=3),
                                                           Num(n=4)],
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Subscript(value=Name(id='l',
                                                             ctx=Load()),
                                                  slice=Slice(lower=Num(n=1),
                                                              upper=Num(n=3),
                                                              step=None),
                                                  ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[List(elts=[Num(n=1),
                                                           Num(n=2)],
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Subscript(value=Name(id='l',
                                                             ctx=Load()),
                                                  slice=Slice(lower=Num(n=-1),
                                                              upper=Num(n=3),
                                                              step=None),
                                                  ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[List(elts=[],
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Subscript(value=Name(id='l',
                                                             ctx=Load()),
                                                  slice=Slice(lower=Num(n=-3),
                                                              upper=Num(n=3),
                                                              step=None),
                                                  ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[List(elts=[Num(n=2)],
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Subscript(value=Name(id='l',
                                                             ctx=Load()),
                                                  slice=Slice(lower=Num(n=-4),
                                                              upper=Num(n=-1),
                                                              step=None),
                                                  ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[List(elts=[Num(n=1),
                                                           Num(n=2),
                                                           Num(n=3)],
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Subscript(value=Name(id='l',
                                                             ctx=Load()),
                                                  slice=Slice(lower=Num(n=0),
                                                              upper=Num(n=5),
                                                              step=Num(n=1)),
                                                  ctx=Load()),
                                   ops=[Eq(),
                                        Eq(),
                                        Eq(),
                                        Eq(),
                                        Eq(),
                                        Eq(),
                                        Eq(),
                                        Eq()],
                                   comparators=[Subscript(value=Name(id='l',
                                                                     ctx=Load()),
                                                          slice=Slice(lower=None,
                                                                      upper=Num(n=5),
                                                                      step=Num(n=1)),
                                                          ctx=Load()),
                                                Subscript(value=Name(id='l',
                                                                     ctx=Load()),
                                                          slice=Slice(lower=Num(n=0),
                                                                      upper=None,
                                                                      step=Num(n=1)),
                                                          ctx=Load()),
                                                Subscript(value=Name(id='l',
                                                                     ctx=Load()),
                                                          slice=Slice(lower=Num(n=0),
                                                                      upper=Num(n=5),
                                                                      step=Name(id='None',
                                                                                ctx=Load())),
                                                          ctx=Load()),
                                                Subscript(value=Name(id='l',
                                                                     ctx=Load()),
                                                          slice=Slice(lower=Num(n=0),
                                                                      upper=None,
                                                                      step=Name(id='None',
                                                                                ctx=Load())),
                                                          ctx=Load()),
                                                Subscript(value=Name(id='l',
                                                                     ctx=Load()),
                                                          slice=Slice(lower=None,
                                                                      upper=Num(n=5),
                                                                      step=Name(id='None',
                                                                                ctx=Load())),
                                                          ctx=Load()),
                                                Subscript(value=Name(id='l',
                                                                     ctx=Load()),
                                                          slice=Slice(lower=None,
                                                                      upper=None,
                                                                      step=Num(n=1)),
                                                          ctx=Load()),
                                                Subscript(value=Name(id='l',
                                                                     ctx=Load()),
                                                          slice=Slice(lower=None,
                                                                      upper=None,
                                                                      step=Name(id='None',
                                                                                ctx=Load())),
                                                          ctx=Load()),
                                                List(elts=[Num(n=0),
                                                           Num(n=1),
                                                           Num(n=2),
                                                           Num(n=3),
                                                           Num(n=4)],
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Subscript(value=Name(id='l',
                                                             ctx=Load()),
                                                  slice=Slice(lower=None,
                                                              upper=None,
                                                              step=Num(n=-1)),
                                                  ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[List(elts=[Num(n=4),
                                                           Num(n=3),
                                                           Num(n=2),
                                                           Num(n=1),
                                                           Num(n=0)],
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Subscript(value=Name(id='l',
                                                             ctx=Load()),
                                                  slice=Slice(lower=Num(n=4),
                                                              upper=Num(n=2),
                                                              step=Num(n=-1)),
                                                  ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[List(elts=[Num(n=4),
                                                           Num(n=3)],
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Subscript(value=Name(id='l',
                                                             ctx=Load()),
                                                  slice=Slice(lower=Num(n=-1),
                                                              upper=Num(n=2),
                                                              step=Num(n=-2)),
                                                  ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[List(elts=[Num(n=4)],
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Name(id='len',
                                                       ctx=Load()),
                                             args=[Name(id='l',
                                                        ctx=Load())],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Num(n=5)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Name(id='min',
                                                       ctx=Load()),
                                             args=[Name(id='l',
                                                        ctx=Load())],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Num(n=0)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Name(id='max',
                                                       ctx=Load()),
                                             args=[Name(id='l',
                                                        ctx=Load())],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Num(n=4)])],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\nTUPLES')],
                   nl=True),
             Assign(targets=[Name(id='t',
                                  ctx=Store())],
                    value=Tuple(elts=[Num(n=0),
                                      Num(n=1),
                                      Num(n=2),
                                      Num(n=3),
                                      Num(n=4)],
                                ctx=Load())),
             Print(dest=None,
                   values=[Compare(left=Compare(left=Num(n=0),
                                                ops=[In()],
                                                comparators=[Name(id='t',
                                                                  ctx=Load())]),
                                   ops=[Eq()],
                                   comparators=[Name(id='True',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Compare(left=Num(n=5),
                                                ops=[In()],
                                                comparators=[Name(id='t',
                                                                  ctx=Load())]),
                                   ops=[Eq()],
                                   comparators=[Name(id='False',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Compare(left=Num(n=4),
                                                ops=[NotIn()],
                                                comparators=[Name(id='t',
                                                                  ctx=Load())]),
                                   ops=[Eq()],
                                   comparators=[Name(id='False',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Compare(left=Str(s='hello'),
                                                ops=[NotIn()],
                                                comparators=[Name(id='t',
                                                                  ctx=Load())]),
                                   ops=[Eq()],
                                   comparators=[Name(id='True',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=BinOp(left=Tuple(elts=[Num(n=0),
                                                               Num(n=1),
                                                               Num(n=2)],
                                                         ctx=Load()),
                                              op=Add(),
                                              right=Tuple(elts=[Num(n=3),
                                                                Num(n=4)],
                                                          ctx=Load())),
                                   ops=[Eq()],
                                   comparators=[Name(id='t',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=BinOp(left=Tuple(elts=[Num(n=0)],
                                                         ctx=Load()),
                                              op=Mult(),
                                              right=Num(n=3)),
                                   ops=[Eq()],
                                   comparators=[Tuple(elts=[Num(n=0),
                                                            Num(n=0),
                                                            Num(n=0)],
                                                      ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=BinOp(left=Num(n=2),
                                              op=Mult(),
                                              right=Tuple(elts=[Num(n=1),
                                                                Num(n=2)],
                                                          ctx=Load())),
                                   ops=[Eq()],
                                   comparators=[Tuple(elts=[Num(n=1),
                                                            Num(n=2),
                                                            Num(n=1),
                                                            Num(n=2)],
                                                      ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Subscript(value=Name(id='t',
                                                             ctx=Load()),
                                                  slice=Index(value=Num(n=4)),
                                                  ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Num(n=4)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Subscript(value=Name(id='t',
                                                             ctx=Load()),
                                                  slice=Index(value=Num(n=-1)),
                                                  ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Num(n=4)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Subscript(value=Name(id='t',
                                                             ctx=Load()),
                                                  slice=Slice(lower=Num(n=0),
                                                              upper=Num(n=3),
                                                              step=None),
                                                  ctx=Load()),
                                   ops=[Eq(),
                                        Eq(),
                                        Eq()],
                                   comparators=[Subscript(value=Name(id='t',
                                                                     ctx=Load()),
                                                          slice=Slice(lower=None,
                                                                      upper=Num(n=3),
                                                                      step=None),
                                                          ctx=Load()),
                                                Subscript(value=Name(id='t',
                                                                     ctx=Load()),
                                                          slice=Slice(lower=Name(id='None',
                                                                                 ctx=Load()),
                                                                      upper=Num(n=3),
                                                                      step=None),
                                                          ctx=Load()),
                                                Tuple(elts=[Num(n=0),
                                                            Num(n=1),
                                                            Num(n=2)],
                                                      ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Subscript(value=Name(id='t',
                                                             ctx=Load()),
                                                  slice=Slice(lower=Num(n=0),
                                                              upper=None,
                                                              step=None),
                                                  ctx=Load()),
                                   ops=[Eq(),
                                        Eq(),
                                        Eq()],
                                   comparators=[Subscript(value=Name(id='t',
                                                                     ctx=Load()),
                                                          slice=Slice(lower=Num(n=0),
                                                                      upper=Name(id='None',
                                                                                 ctx=Load()),
                                                                      step=None),
                                                          ctx=Load()),
                                                Subscript(value=Name(id='t',
                                                                     ctx=Load()),
                                                          slice=Slice(lower=None,
                                                                      upper=None,
                                                                      step=None),
                                                          ctx=Load()),
                                                Tuple(elts=[Num(n=0),
                                                            Num(n=1),
                                                            Num(n=2),
                                                            Num(n=3),
                                                            Num(n=4)],
                                                      ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Subscript(value=Name(id='t',
                                                             ctx=Load()),
                                                  slice=Slice(lower=Num(n=1),
                                                              upper=Num(n=3),
                                                              step=None),
                                                  ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Tuple(elts=[Num(n=1),
                                                            Num(n=2)],
                                                      ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Subscript(value=Name(id='t',
                                                             ctx=Load()),
                                                  slice=Slice(lower=Num(n=-1),
                                                              upper=Num(n=3),
                                                              step=None),
                                                  ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Tuple(elts=[],
                                                      ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Subscript(value=Name(id='t',
                                                             ctx=Load()),
                                                  slice=Slice(lower=Num(n=-3),
                                                              upper=Num(n=3),
                                                              step=None),
                                                  ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Tuple(elts=[Num(n=2)],
                                                      ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Subscript(value=Name(id='t',
                                                             ctx=Load()),
                                                  slice=Slice(lower=Num(n=-4),
                                                              upper=Num(n=-1),
                                                              step=None),
                                                  ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Tuple(elts=[Num(n=1),
                                                            Num(n=2),
                                                            Num(n=3)],
                                                      ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Subscript(value=Name(id='t',
                                                             ctx=Load()),
                                                  slice=Slice(lower=Num(n=0),
                                                              upper=Num(n=5),
                                                              step=Num(n=1)),
                                                  ctx=Load()),
                                   ops=[Eq(),
                                        Eq(),
                                        Eq(),
                                        Eq(),
                                        Eq(),
                                        Eq(),
                                        Eq(),
                                        Eq()],
                                   comparators=[Subscript(value=Name(id='t',
                                                                     ctx=Load()),
                                                          slice=Slice(lower=None,
                                                                      upper=Num(n=5),
                                                                      step=Num(n=1)),
                                                          ctx=Load()),
                                                Subscript(value=Name(id='t',
                                                                     ctx=Load()),
                                                          slice=Slice(lower=Num(n=0),
                                                                      upper=None,
                                                                      step=Num(n=1)),
                                                          ctx=Load()),
                                                Subscript(value=Name(id='t',
                                                                     ctx=Load()),
                                                          slice=Slice(lower=Num(n=0),
                                                                      upper=Num(n=5),
                                                                      step=Name(id='None',
                                                                                ctx=Load())),
                                                          ctx=Load()),
                                                Subscript(value=Name(id='t',
                                                                     ctx=Load()),
                                                          slice=Slice(lower=Num(n=0),
                                                                      upper=None,
                                                                      step=Name(id='None',
                                                                                ctx=Load())),
                                                          ctx=Load()),
                                                Subscript(value=Name(id='t',
                                                                     ctx=Load()),
                                                          slice=Slice(lower=None,
                                                                      upper=Num(n=5),
                                                                      step=Name(id='None',
                                                                                ctx=Load())),
                                                          ctx=Load()),
                                                Subscript(value=Name(id='t',
                                                                     ctx=Load()),
                                                          slice=Slice(lower=None,
                                                                      upper=None,
                                                                      step=Num(n=1)),
                                                          ctx=Load()),
                                                Subscript(value=Name(id='t',
                                                                     ctx=Load()),
                                                          slice=Slice(lower=None,
                                                                      upper=None,
                                                                      step=Name(id='None',
                                                                                ctx=Load())),
                                                          ctx=Load()),
                                                Tuple(elts=[Num(n=0),
                                                            Num(n=1),
                                                            Num(n=2),
                                                            Num(n=3),
                                                            Num(n=4)],
                                                      ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Subscript(value=Name(id='t',
                                                             ctx=Load()),
                                                  slice=Slice(lower=None,
                                                              upper=None,
                                                              step=Num(n=-1)),
                                                  ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Tuple(elts=[Num(n=4),
                                                            Num(n=3),
                                                            Num(n=2),
                                                            Num(n=1),
                                                            Num(n=0)],
                                                      ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Subscript(value=Name(id='t',
                                                             ctx=Load()),
                                                  slice=Slice(lower=Num(n=4),
                                                              upper=Num(n=2),
                                                              step=Num(n=-1)),
                                                  ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Tuple(elts=[Num(n=4),
                                                            Num(n=3)],
                                                      ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Subscript(value=Name(id='t',
                                                             ctx=Load()),
                                                  slice=Slice(lower=Num(n=-1),
                                                              upper=Num(n=2),
                                                              step=Num(n=-2)),
                                                  ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Tuple(elts=[Num(n=4)],
                                                      ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Name(id='len',
                                                       ctx=Load()),
                                             args=[Name(id='t',
                                                        ctx=Load())],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Num(n=5)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Name(id='min',
                                                       ctx=Load()),
                                             args=[Name(id='t',
                                                        ctx=Load())],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Num(n=0)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Name(id='max',
                                                       ctx=Load()),
                                             args=[Name(id='t',
                                                        ctx=Load())],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Num(n=4)])],
                   nl=True)])
