Module(body=[Print(dest=None,
                   values=[Call(func=Attribute(value=Str(s='abcdefghijklmnopqrstuvwxyz'),
                                               attr='replace',
                                               ctx=Load()),
                                args=[Str(s='w'),
                                      Str(s='')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Str(s='abcdefg'),
                                               attr='replace',
                                               ctx=Load()),
                                args=[Str(s='abc'),
                                      Str(s='xyz')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Str(s='...abcdefg\\!@#$%^&*()_=+'),
                                               attr='replace',
                                               ctx=Load()),
                                args=[Str(s='\\'),
                                      Str(s='xyz')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True)])
