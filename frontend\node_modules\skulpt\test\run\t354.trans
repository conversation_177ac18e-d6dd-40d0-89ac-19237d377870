Module(body=[ClassDef(name='A',
                      bases=[],
                      body=[Assign(targets=[Name(id='val1',
                                                 ctx=Store())],
                                   value=Str(s='A')),
                            FunctionDef(name='__init__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param()),
                                                             Name(id='v',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Assign(targets=[Attribute(value=Name(id='self',
                                                                                   ctx=Load()),
                                                                        attr='val1',
                                                                        ctx=Store())],
                                                     value=Name(id='v',
                                                                ctx=Load()))],
                                        decorator_list=[]),
                            FunctionDef(name='do',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Print(dest=None,
                                                    values=[Attribute(value=Attribute(value=Name(id='self',
                                                                                                 ctx=Load()),
                                                                                      attr='__class__',
                                                                                      ctx=Load()),
                                                                      attr='val1',
                                                                      ctx=Load())],
                                                    nl=True),
                                              Print(dest=None,
                                                    values=[Attribute(value=Name(id='self',
                                                                                 ctx=Load()),
                                                                      attr='val1',
                                                                      ctx=Load())],
                                                    nl=True)],
                                        decorator_list=[]),
                            FunctionDef(name='update',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param()),
                                                             Name(id='newv',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Assign(targets=[Attribute(value=Name(id='self',
                                                                                   ctx=Load()),
                                                                        attr='val1',
                                                                        ctx=Store())],
                                                     value=Name(id='newv',
                                                                ctx=Load()))],
                                        decorator_list=[])],
                      decorator_list=[]),
             Print(dest=None,
                   values=[Str(s='===A===')],
                   nl=True),
             Assign(targets=[Name(id='a',
                                  ctx=Store())],
                    value=Call(func=Name(id='A',
                                         ctx=Load()),
                               args=[Str(s='sa')],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Expr(value=Call(func=Attribute(value=Name(id='a',
                                                       ctx=Load()),
                                            attr='do',
                                            ctx=Load()),
                             args=[],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Attribute(value=Name(id='a',
                                                       ctx=Load()),
                                            attr='update',
                                            ctx=Load()),
                             args=[Str(s='sa-new')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Attribute(value=Name(id='a',
                                                       ctx=Load()),
                                            attr='do',
                                            ctx=Load()),
                             args=[],
                             keywords=[],
                             starargs=None,
                             kwargs=None))])
