# Development Setup Guide

This guide will help you set up the Python Learning Platform for local development.

## Prerequisites

- Node.js 18+ and npm
- Python 3.9+
- Git

## Quick Start with Docker (Recommended)

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd pywebsite
   ```

2. **Start with Docker Compose**
   ```bash
   docker-compose up
   ```

3. **Access the application**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:5000
   - Database: PostgreSQL on port 5432

## Manual Setup

### Backend Setup

1. **Navigate to backend directory**
   ```bash
   cd backend
   ```

2. **Create virtual environment**
   ```bash
   python -m venv venv
   
   # Windows
   venv\Scripts\activate
   
   # macOS/Linux
   source venv/bin/activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

5. **Initialize database**
   ```bash
   python app.py
   ```

6. **Run the development server**
   ```bash
   python app.py
   ```

The backend will be available at http://localhost:5000

### Frontend Setup

1. **Navigate to frontend directory**
   ```bash
   cd frontend
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start development server**
   ```bash
   npm run dev
   ```

The frontend will be available at http://localhost:5173

## Environment Variables

### Backend (.env)

```env
FLASK_ENV=development
FLASK_DEBUG=1
SECRET_KEY=your-secret-key-here
JWT_SECRET_KEY=your-jwt-secret-key-here
DATABASE_URL=sqlite:///pylearning.db
```

### Frontend

The frontend automatically connects to the backend at `http://localhost:5000`. You can change this by setting the `VITE_API_URL` environment variable.

## Database

### SQLite (Development)
The application uses SQLite by default for development. The database file will be created automatically at `backend/pylearning.db`.

### PostgreSQL (Production)
For production, update the `DATABASE_URL` in your `.env` file:
```env
DATABASE_URL=postgresql://username:password@localhost:5432/pylearning
```

## API Documentation

### Authentication Endpoints

- `POST /api/auth/register` - Register new user
- `POST /api/auth/login` - Login user
- `GET /api/auth/me` - Get current user info
- `POST /api/auth/refresh` - Refresh JWT token

### Lesson Endpoints

- `GET /api/lessons` - Get all lessons
- `GET /api/lessons/{id}` - Get specific lesson
- `POST /api/lessons/{id}/complete` - Mark lesson as complete
- `GET /api/lessons/progress` - Get user progress

### User Endpoints

- `GET /api/users/profile` - Get user profile
- `PUT /api/users/profile` - Update user profile
- `GET /api/users/badges` - Get user badges
- `GET /api/users/dashboard` - Get dashboard data

## Testing

### Backend Tests
```bash
cd backend
python -m pytest
```

### Frontend Tests
```bash
cd frontend
npm test
```

## Code Structure

### Backend Structure
```
backend/
├── app.py              # Main Flask application
├── models.py           # Database models
├── seed_data.py        # Sample data creation
├── requirements.txt    # Python dependencies
├── routes/            # API route handlers
│   ├── auth.py        # Authentication routes
│   ├── lessons.py     # Lesson routes
│   └── users.py       # User routes
└── migrations/        # Database migrations
```

### Frontend Structure
```
frontend/
├── src/
│   ├── components/    # Reusable React components
│   ├── pages/         # Page components
│   ├── store/         # Zustand state management
│   ├── App.tsx        # Main app component
│   └── main.tsx       # Entry point
├── public/            # Static assets
└── package.json       # Node.js dependencies
```

## Common Issues

### Port Already in Use
If you get a "port already in use" error:
- Backend: Change the port in `app.py` (default: 5000)
- Frontend: Change the port in `vite.config.ts` (default: 5173)

### Database Connection Issues
- Make sure your database is running
- Check your `DATABASE_URL` environment variable
- For SQLite, ensure the backend directory is writable

### CORS Issues
- The backend is configured to allow requests from `localhost:3000` and `localhost:5173`
- If using different ports, update the CORS configuration in `app.py`

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## Support

For development issues, please check:
1. This documentation
2. The project's GitHub issues
3. The API documentation at http://localhost:5000 when running
