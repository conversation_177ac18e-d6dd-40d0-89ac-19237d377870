Module(body=[Print(dest=None,
                   values=[Call(func=Name(id='float',
                                          ctx=Load()),
                                args=[],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Call(func=Name(id='type',
                                          ctx=Load()),
                                args=[Call(func=Name(id='float',
                                                     ctx=Load()),
                                           args=[Num(n=0)],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='float',
                                          ctx=Load()),
                                args=[Num(n=1)],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Call(func=Name(id='type',
                                          ctx=Load()),
                                args=[Call(func=Name(id='float',
                                                     ctx=Load()),
                                           args=[Num(n=1)],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='float',
                                          ctx=Load()),
                                args=[BinOp(left=Num(n=3),
                                            op=Div(),
                                            right=Num(n=2))],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Call(func=Name(id='type',
                                          ctx=Load()),
                                args=[Call(func=Name(id='float',
                                                     ctx=Load()),
                                           args=[BinOp(left=Num(n=3),
                                                       op=Div(),
                                                       right=Num(n=2))],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='float',
                                          ctx=Load()),
                                args=[Num(n=123456789)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='float',
                                          ctx=Load()),
                                args=[Num(n=1.234)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='float',
                                          ctx=Load()),
                                args=[BinOp(left=Num(n=3),
                                            op=Div(),
                                            right=Num(n=2.0))],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='float',
                                          ctx=Load()),
                                args=[Str(s='12.3')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='float',
                                          ctx=Load()),
                                args=[Str(s='  0.5 ')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='float',
                                          ctx=Load()),
                                args=[BinOp(left=Str(s='0.'),
                                            op=Add(),
                                            right=BinOp(left=Str(s='123456789'),
                                                        op=Mult(),
                                                        right=Num(n=3)))],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='float',
                                          ctx=Load()),
                                args=[BinOp(left=Str(s='123456789'),
                                            op=Mult(),
                                            right=Num(n=3))],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='float',
                                          ctx=Load()),
                                args=[Str(s='nan')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='float',
                                          ctx=Load()),
                                args=[Str(s='-nan')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='float',
                                          ctx=Load()),
                                args=[Str(s='NAN')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='float',
                                          ctx=Load()),
                                args=[Str(s='-NAN')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='float',
                                          ctx=Load()),
                                args=[Str(s='+nAn')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='float',
                                          ctx=Load()),
                                args=[Str(s='inf')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='float',
                                          ctx=Load()),
                                args=[Str(s='-inf')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='float',
                                          ctx=Load()),
                                args=[Str(s='INF')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='float',
                                          ctx=Load()),
                                args=[Str(s='-INF')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='float',
                                          ctx=Load()),
                                args=[Str(s='+inF')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             TryExcept(body=[Print(dest=None,
                                   values=[Call(func=Name(id='float',
                                                          ctx=Load()),
                                                args=[Str(s='734L')],
                                                keywords=[],
                                                starargs=None,
                                                kwargs=None)],
                                   nl=True),
                             Print(dest=None,
                                   values=[Str(s="You shouldn't see this.")],
                                   nl=True)],
                       handlers=[ExceptHandler(type=Name(id='ValueError',
                                                         ctx=Load()),
                                               name=None,
                                               body=[Print(dest=None,
                                                           values=[Call(func=Name(id='float',
                                                                                  ctx=Load()),
                                                                        args=[Str(s='734')],
                                                                        keywords=[],
                                                                        starargs=None,
                                                                        kwargs=None)],
                                                           nl=True)])],
                       orelse=[])])
