Module(body=[FunctionDef(name='helper',
                         args=arguments(args=[Name(id='x',
                                                   ctx=Param()),
                                              Name(id='y',
                                                   ctx=Param())],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[]),
                         body=[Print(dest=None,
                                     values=[],
                                     nl=True),
                               Print(dest=None,
                                     values=[Str(s='compare'),
                                             Name(id='x',
                                                  ctx=Load()),
                                             Str(s='and'),
                                             Name(id='y',
                                                  ctx=Load())],
                                     nl=True),
                               Print(dest=None,
                                     values=[Compare(left=Name(id='x',
                                                               ctx=Load()),
                                                     ops=[Is()],
                                                     comparators=[Name(id='y',
                                                                       ctx=Load())]),
                                             Compare(left=Name(id='x',
                                                               ctx=Load()),
                                                     ops=[IsNot()],
                                                     comparators=[Name(id='y',
                                                                       ctx=Load())])],
                                     nl=True),
                               Print(dest=None,
                                     values=[Compare(left=Name(id='x',
                                                               ctx=Load()),
                                                     ops=[Eq()],
                                                     comparators=[Name(id='y',
                                                                       ctx=Load())]),
                                             Compare(left=Name(id='x',
                                                               ctx=Load()),
                                                     ops=[NotEq()],
                                                     comparators=[Name(id='y',
                                                                       ctx=Load())])],
                                     nl=True),
                               Print(dest=None,
                                     values=[Compare(left=Name(id='x',
                                                               ctx=Load()),
                                                     ops=[Lt()],
                                                     comparators=[Name(id='y',
                                                                       ctx=Load())]),
                                             Compare(left=Name(id='x',
                                                               ctx=Load()),
                                                     ops=[LtE()],
                                                     comparators=[Name(id='y',
                                                                       ctx=Load())])],
                                     nl=True),
                               Print(dest=None,
                                     values=[Compare(left=Name(id='x',
                                                               ctx=Load()),
                                                     ops=[Gt()],
                                                     comparators=[Name(id='y',
                                                                       ctx=Load())]),
                                             Compare(left=Name(id='x',
                                                               ctx=Load()),
                                                     ops=[GtE()],
                                                     comparators=[Name(id='y',
                                                                       ctx=Load())])],
                                     nl=True)],
                         decorator_list=[]),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Name(id='None',
                                        ctx=Load()),
                                   Name(id='None',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Name(id='None',
                                        ctx=Load()),
                                   Name(id='True',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Name(id='None',
                                        ctx=Load()),
                                   Name(id='False',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Name(id='True',
                                        ctx=Load()),
                                   Name(id='True',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Name(id='True',
                                        ctx=Load()),
                                   Name(id='False',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Name(id='True',
                                        ctx=Load()),
                                   Name(id='None',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Name(id='False',
                                        ctx=Load()),
                                   Name(id='False',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Name(id='False',
                                        ctx=Load()),
                                   Name(id='True',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Name(id='False',
                                        ctx=Load()),
                                   Name(id='None',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None))])
