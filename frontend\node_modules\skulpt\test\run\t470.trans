Module(body=[FunctionDef(name='helper',
                         args=arguments(args=[Name(id='got',
                                                   ctx=Param()),
                                              Name(id='expect',
                                                   ctx=Param())],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[]),
                         body=[If(test=Compare(left=Name(id='got',
                                                         ctx=Load()),
                                               ops=[Eq()],
                                               comparators=[Name(id='expect',
                                                                 ctx=Load())]),
                                  body=[Print(dest=None,
                                              values=[Name(id='True',
                                                           ctx=Load())],
                                              nl=True)],
                                  orelse=[Print(dest=None,
                                                values=[Name(id='False',
                                                             ctx=Load()),
                                                        Name(id='expect',
                                                             ctx=Load()),
                                                        Name(id='got',
                                                             ctx=Load())],
                                                nl=True)])],
                         decorator_list=[]),
             Print(dest=None,
                   values=[Str(s='\nstr.find')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='hello world'),
                                                       attr='find',
                                                       ctx=Load()),
                                        args=[Str(s='l'),
                                              Num(n=-2)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Num(n=9)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='hello world'),
                                                       attr='find',
                                                       ctx=Load()),
                                        args=[Str(s='l'),
                                              Num(n=4),
                                              Num(n=6)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Num(n=-1)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='hello world'),
                                                       attr='find',
                                                       ctx=Load()),
                                        args=[Str(s='o'),
                                              Num(n=2),
                                              Num(n=5)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Num(n=4)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='hello world'),
                                                       attr='find',
                                                       ctx=Load()),
                                        args=[Str(s='o'),
                                              Num(n=2),
                                              Num(n=-5)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Num(n=4)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='hello world'),
                                                       attr='find',
                                                       ctx=Load()),
                                        args=[Str(s='o'),
                                              Num(n=-8),
                                              Num(n=-5)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Num(n=4)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='hello world'),
                                                       attr='find',
                                                       ctx=Load()),
                                        args=[Str(s='o'),
                                              Num(n=-3),
                                              Num(n=-1)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Num(n=-1)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Str(s='\nstr.index')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='hello world'),
                                                       attr='index',
                                                       ctx=Load()),
                                        args=[Str(s='l'),
                                              Num(n=-2)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Num(n=9)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='hello world'),
                                                       attr='index',
                                                       ctx=Load()),
                                        args=[Str(s='o'),
                                              Num(n=2),
                                              Num(n=5)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Num(n=4)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='hello world'),
                                                       attr='index',
                                                       ctx=Load()),
                                        args=[Str(s='o'),
                                              Num(n=2),
                                              Num(n=-5)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Num(n=4)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='hello world'),
                                                       attr='index',
                                                       ctx=Load()),
                                        args=[Str(s='o'),
                                              Num(n=-8),
                                              Num(n=-5)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Num(n=4)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Str(s='\nstr.rfind')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='hello world'),
                                                       attr='rfind',
                                                       ctx=Load()),
                                        args=[Str(s='h'),
                                              Num(n=-2)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Num(n=-1)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='hello world'),
                                                       attr='rfind',
                                                       ctx=Load()),
                                        args=[Str(s='l'),
                                              Num(n=2),
                                              Num(n=4)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Num(n=3)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='hello world'),
                                                       attr='rfind',
                                                       ctx=Load()),
                                        args=[Str(s='l'),
                                              Num(n=2),
                                              Num(n=8)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Num(n=3)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='hello world'),
                                                       attr='rfind',
                                                       ctx=Load()),
                                        args=[Str(s='l'),
                                              Num(n=-1),
                                              Num(n=10)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Num(n=-1)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='hello world'),
                                                       attr='rfind',
                                                       ctx=Load()),
                                        args=[Str(s='l'),
                                              Num(n=1),
                                              Num(n=-3)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Num(n=3)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='hello world'),
                                                       attr='rfind',
                                                       ctx=Load()),
                                        args=[Str(s='l'),
                                              Num(n=-9),
                                              Num(n=-2)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Num(n=3)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Str(s='\nstr.rindex')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='hello world'),
                                                       attr='rindex',
                                                       ctx=Load()),
                                        args=[Str(s='l'),
                                              Num(n=-2)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Num(n=9)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='hello world'),
                                                       attr='rindex',
                                                       ctx=Load()),
                                        args=[Str(s='l'),
                                              Num(n=0),
                                              Num(n=-3)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Num(n=3)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='hello world'),
                                                       attr='rindex',
                                                       ctx=Load()),
                                        args=[Str(s='o'),
                                              Num(n=2),
                                              Num(n=7)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Num(n=4)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='hello world'),
                                                       attr='rindex',
                                                       ctx=Load()),
                                        args=[Str(s='o'),
                                              Num(n=2),
                                              Num(n=-2)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Num(n=7)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='hello world'),
                                                       attr='rindex',
                                                       ctx=Load()),
                                        args=[Str(s='o'),
                                              Num(n=-5),
                                              Num(n=-2)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Num(n=7)],
                             keywords=[],
                             starargs=None,
                             kwargs=None))])
