import React from 'react';
import { Link } from 'react-router-dom';
import { useAuthStore } from '../store/authStore';
import { 
  Code, 
  Zap, 
  Trophy, 
  Users, 
  BookOpen, 
  Star,
  Play,
  ArrowRight
} from 'lucide-react';

const Home: React.FC = () => {
  const { isAuthenticated, loginAsGuest } = useAuthStore();

  const features = [
    {
      icon: Code,
      title: 'Interactive Coding',
      description: 'Write and run Python code directly in your browser with instant feedback!'
    },
    {
      icon: BookOpen,
      title: 'Step-by-Step Lessons',
      description: 'Learn Python through fun, bite-sized lessons designed for young minds.'
    },
    {
      icon: Trophy,
      title: '<PERSON>ar<PERSON><PERSON><PERSON>',
      description: 'Collect points, unlock badges, and track your coding journey!'
    },
    {
      icon: Zap,
      title: 'Instant Results',
      description: 'See your code come to life immediately with our fast Python runner.'
    }
  ];

  const handleTryAsGuest = () => {
    loginAsGuest();
  };

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-primary-50 to-secondary-50 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
              Learn Python the 
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-primary-500 to-secondary-500">
                {' '}Fun Way!
              </span>
            </h1>
            <p className="text-kid-lg text-gray-600 mb-8 max-w-3xl mx-auto">
              Join thousands of kids learning to code with Python! Our interactive platform 
              makes programming fun and easy to understand, even for complete beginners.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              {isAuthenticated ? (
                <Link to="/dashboard" className="btn-primary text-kid-lg px-8 py-4">
                  <Play className="mr-2" size={20} />
                  Continue Learning
                </Link>
              ) : (
                <>
                  <Link to="/register" className="btn-primary text-kid-lg px-8 py-4">
                    <Star className="mr-2" size={20} />
                    Start Learning Free
                  </Link>
                  <button 
                    onClick={handleTryAsGuest}
                    className="btn-secondary text-kid-lg px-8 py-4"
                  >
                    <Play className="mr-2" size={20} />
                    Try as Guest
                  </button>
                </>
              )}
            </div>
            
            <div className="mt-8 text-kid-sm text-gray-500">
              ✨ No downloads required • Works on any device • 100% safe for kids
            </div>
          </div>
        </div>
        
        {/* Decorative Elements */}
        <div className="absolute top-10 left-10 text-6xl animate-bounce">🐍</div>
        <div className="absolute top-20 right-20 text-4xl animate-pulse">💻</div>
        <div className="absolute bottom-10 left-20 text-5xl animate-bounce delay-1000">🎯</div>
        <div className="absolute bottom-20 right-10 text-3xl animate-pulse delay-500">⭐</div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-kid-3xl font-bold text-gray-900 mb-4">
              Why Kids Love Learning Python Here
            </h2>
            <p className="text-kid-lg text-gray-600 max-w-2xl mx-auto">
              We've designed everything to be fun, safe, and educational for young coders.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => {
              const Icon = feature.icon;
              return (
                <div key={index} className="card text-center group hover:shadow-kid-lg transition-all duration-300">
                  <div className="w-16 h-16 bg-gradient-to-br from-primary-400 to-secondary-400 rounded-kid-xl mx-auto mb-4 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <Icon className="text-white" size={32} />
                  </div>
                  <h3 className="text-kid-xl font-bold text-gray-900 mb-3">
                    {feature.title}
                  </h3>
                  <p className="text-gray-600">
                    {feature.description}
                  </p>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-20 bg-gradient-to-r from-primary-500 to-secondary-500">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-3 gap-8 text-center text-white">
            <div>
              <div className="text-4xl font-bold mb-2">10,000+</div>
              <div className="text-kid-lg opacity-90">Happy Young Coders</div>
            </div>
            <div>
              <div className="text-4xl font-bold mb-2">20+</div>
              <div className="text-kid-lg opacity-90">Interactive Lessons</div>
            </div>
            <div>
              <div className="text-4xl font-bold mb-2">100%</div>
              <div className="text-kid-lg opacity-90">Safe & Ad-Free</div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      {!isAuthenticated && (
        <section className="py-20 bg-white">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-kid-3xl font-bold text-gray-900 mb-6">
              Ready to Start Your Coding Adventure?
            </h2>
            <p className="text-kid-lg text-gray-600 mb-8">
              Join our community of young programmers and start building amazing things with Python!
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link to="/register" className="btn-primary text-kid-lg px-8 py-4">
                Create Free Account
                <ArrowRight className="ml-2" size={20} />
              </Link>
              <button 
                onClick={handleTryAsGuest}
                className="btn-secondary text-kid-lg px-8 py-4"
              >
                Try 3 Lessons Free
              </button>
            </div>
            
            <div className="mt-6 text-kid-sm text-gray-500">
              No credit card required • Start coding in 30 seconds
            </div>
          </div>
        </section>
      )}
    </div>
  );
};

export default Home;
