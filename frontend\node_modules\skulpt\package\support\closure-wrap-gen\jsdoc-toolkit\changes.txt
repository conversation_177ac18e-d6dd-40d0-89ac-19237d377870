== 2.4.1 (unreleased) ==

  * Applied patch from allan.jardine to support quoted names in object literals. ( issue #293 )
  * Added support for a dash to separate a param name and description. ( issue #270 )
  * Fix for bug that sometmes caused warning to be emitted when using subscripted variable names. ( issue #278 )
  * Added support for the -P option to specify a plugins folder outside of the base dir. ( issue #268 )
  * Added better error message when a malformed template option is given. ( issue #271 )
  * Fix for bug that prevented borrowing overloaded members. ( issue #295 )

== 2.4.0 ==

  * Fixed bug that added mutiple symbols with the same name to docs.
  * Added support for the -m option to suppress warnings for multiple docs.
  * Added patch by brownsea42 to support quoted user variables on the command line. ( issue #281 )
  * Fixed bug that sometimes caused links to events to be incorrect. ( issue #292 )
  
== 2.3.3 ==

  * Fixed bug that made all fields declared with the @property tag static. ( issue #262 )
  * Minor fix to better handle trailing slash on path to template (from jwmetrocat). ( issue #237 )
  * Fix for @memberOf when applied to inner members. ( issue #264 )
  * Fix for @memberOf when applied to symbols documented with @name. ( issue #260 )
  * Applied patch from kunhualqk, fix for bug where @link to borrowed member did not resolve to parent class. ( issue #218 )
  * Fix for @requires not linking back to the required class
  * Added experimental support for @constructs to have an argument, the class name, when applied to a function assignment.
  
== 2.3.2 ==

 * Minor update to the usage notes and corrected the version number displayed in the output.

== 2.3.1 ==

 * Fixed HTML typo in allfiles template. ( issue #228 )
 * Modified template to display version information for classes.
 * Modified template to better support multiple methods with the same name.
 * Fixed bug that caused template to error when backtick characters appeared around class names.

== 2.3.0 ==

  * Added option -u, --unique to avoid bug that causes multiple symbols with names that differ only by case to overwrite each others output on case-insensitive filesystems. ( issue #162 )
  * Fixed bug where {@links} in @deprecated tags did not resolve. ( issue #220 )
  * Fixed bug that caused parens around a function to make it to be unrecognized. ( issue #213 )
  * Fixed bug prevented explicit links to named anchors from working (thanks katgao.pku). ( issue #215 )
  * Fixed bug that prevented full description from appearing in file overview. ( issue #224 )
  
== 2.2.1 ==

  * Fixed bug with class template, where sorting of methods was accidentally removed (thanks dezfowler).
  * Added missing test files for the @exports unit tests.

== 2.2.0 ==

  * Fixed bug that caused exception when given a folder containing non-js files, even with the x commandline option set to "js". ( issue #193 )
  * Fixed typo in index template [patch submitted by olle]. ( issue #198 )
  * Modified @borrows tag experimentally to allow for missing "as ..." clause.
  * Added support for the @exports tag, to allow one symbol to be documented as another.
  * Added support for the -S option to document code following the Secure Modules pattern.
  
== 2.1.0 ==

  * Added support for the @event tag.
  * Fixed bug that prevented the : character from appearing in symbol names.
  * Fixed bug that prevented underscored symbols marked with @public being tagged as private. (issue #184 )
  * Fixed bug that randomly affected the @memberOf tag when the name of the symbol did not include the parent name.
  * Fixed bug that prevented templates that were not in the jsdoc-toolkit folder from being found. ( issue #176 )
  * Added ability to check for trailing slash on template path. ( issue #177 )
  * Modified classDesc so that it no longer is appended with the constructor desc.
  * Fixed call to plugin onDocCommentSrc.
  * Added missing support for inline doc comments for function return types. ( issue #189 )
  * Added command line option -q, --quiet.
  * Added command line option -E, --exclude. ( issue #143 )
  * Added 2 more hooks for plugins. ( issue #163 )
  * Added support for extending built-ins. ( issue #160 )
  * Added "compact" option to JSDOC.JsPlate.prototype.process. ( issue #159 )
  * @augments no longer documents static members as inherited. ( issue #138 )
  * @link to a class now goes to the page for that class, not the constructor. ( issue #178 )
  * Warnings of mismatched curly brace now include filename. ( issue #166 )
  * Fixed bug affecting template paths loaded via a configuration file when the trailing slash is missing. ( issue #191 )
  * Minor optimizations.

== 2.0.2 ==

  * Fixed bug that sometimes caused an example of division in the source code to be interpretted as a regex by the JsDoc Toolkit analyzer. ( issue #158 )
  * Fixed a bug that prevented private variables marked as @public from appearing in the documentation. ( issue #161 )
  * Fixed bug that prevented variable names with underscored properties from appearing in summaries. ( issue #173 )

== 2.0.1 ==

  * Fixed bug that prevented @fileOverview tag from being recognized.
  * Added support for @fieldOf as a synonym for @field plus @memberOf.
  * Added support for @name tag in a @fileOverview comment to control the displayed name of the file.
  * Added support for multiple @example tags. ( issue #152 )
  * Modified style sheet of jsdoc template to make more readable. ( issue #151 )
  * Fixed bug that prevented @since documentation from displaying correctly when it appeared in a class. ( issue #150 )
  * Fixed bug that caused inhertited properties to sometimes not resolve correctly. ( issue #144 )
  * Modified so that trailing whitespace in @example is always trimmed. ( issue #153 )
  * Added support for elseif to JsPlate. (hat tip to fredck)
  * Added support for @location urls in the @overview comment to the jsdoc template.

== Changes From Versions 1.4.0 to 2.0.0 ==

  * Upgraded included version of Rhino from 1.6 to 1.7R1.
  * Removed circular references in parsed documentation objects.
  * Improved inheritance handling, now properties and events can be inherited same as methods.
  * Improved handling of cross-file relationships, now having two related objects in separate files is not a problem.
  * Improved ability to recognize membership of previously defined objects.
  * Added ability to redefine parsing behavior with plugins.
  * @methodOf is a synonym for @function and @memberOf.
  * Added @default to document default values of members that are objects.
  * Added ability to parse and refer to inner functions.
  * Fixed bug that appeared when calling a method to set properties of the instance referred to by "this".
  * Added ability to automatically create links to other symbols.
  * New "jsdoc" template now produces fully W3C valid XHTML.
  * Inline parameter type hint comments are now documented.
  * Fixed error: Locally scoped variables (declared with var) no longer appear as global.
  * It is now possible to run JsDoc Toolkit from any directory.
  * Added support for inline {@link ...} tags.
  * Added support for the -H command-line option to allow for custom content handlers.
  * Tag names @inherits and @scope changed to @borrows and @lends.
  ? Combining @constructor in a doclet with @lends now supported.
  * Multiple @lend tags now supported.
  * Added support for the @constructs tag, used inside a @lends block.
  * Added support for the @constant tag.
  * Fixed bug that prevented the use of [] as a default value.
  * Added support for the @field tag.
  * Added support for the @public tag (applied to inner functions).
  * @namespace tag can now be applied to functions, not just object literals.
  * Added support for the -s command line option to suppress source code output.
  * Added new unit test framework.
  * Underscored symbols are now treated as if they have a @private tag by default.
  * Improved support for anonymous constructors.
  * Added support for the nocode meta tag.
  