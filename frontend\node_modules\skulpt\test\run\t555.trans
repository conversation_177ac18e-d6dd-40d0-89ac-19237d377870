Module(body=[ImportFrom(module='time',
                        names=[alias(name='sleep',
                                     asname=None)],
                        level=0),
             FunctionDef(name='sleeping_generator',
                         args=arguments(args=[],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[]),
                         body=[For(target=Name(id='i',
                                               ctx=Store()),
                                   iter=Call(func=Name(id='range',
                                                       ctx=Load()),
                                             args=[Num(n=5)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   body=[Expr(value=Call(func=Name(id='sleep',
                                                                   ctx=Load()),
                                                         args=[Num(n=0.01)],
                                                         keywords=[],
                                                         starargs=None,
                                                         kwargs=None)),
                                         Expr(value=Yield(value=Name(id='i',
                                                                     ctx=Load())))],
                                   orelse=[])],
                         decorator_list=[]),
             Assign(targets=[Name(id='x',
                                  ctx=Store())],
                    value=GeneratorExp(elt=Name(id='i',
                                                ctx=Load()),
                                       generators=[comprehension(target=Name(id='i',
                                                                             ctx=Store()),
                                                                 iter=Call(func=Name(id='sleeping_generator',
                                                                                     ctx=Load()),
                                                                           args=[],
                                                                           keywords=[],
                                                                           starargs=None,
                                                                           kwargs=None),
                                                                 ifs=[])])),
             Print(dest=None,
                   values=[Call(func=Name(id='list',
                                          ctx=Load()),
                                args=[Name(id='x',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True)])
