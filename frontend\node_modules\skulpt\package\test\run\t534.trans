Module(body=[ClassDef(name='U',
                      bases=[Name(id='object',
                                  ctx=Load())],
                      body=[FunctionDef(name='__repr__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Return(value=Str(s='<U>'))],
                                        decorator_list=[]),
                            FunctionDef(name='__pos__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Return(value=Str(s='pos'))],
                                        decorator_list=[]),
                            FunctionDef(name='__neg__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Return(value=Str(s='neg'))],
                                        decorator_list=[]),
                            FunctionDef(name='__invert__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Return(value=Str(s='invert'))],
                                        decorator_list=[])],
                      decorator_list=[]),
             Print(dest=None,
                   values=[Call(func=Name(id='U',
                                          ctx=Load()),
                                args=[],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[UnaryOp(op=USub(),
                                   operand=Call(func=Name(id='U',
                                                          ctx=Load()),
                                                args=[],
                                                keywords=[],
                                                starargs=None,
                                                kwargs=None))],
                   nl=True),
             Print(dest=None,
                   values=[UnaryOp(op=UAdd(),
                                   operand=Call(func=Name(id='U',
                                                          ctx=Load()),
                                                args=[],
                                                keywords=[],
                                                starargs=None,
                                                kwargs=None))],
                   nl=True),
             Print(dest=None,
                   values=[UnaryOp(op=Invert(),
                                   operand=Call(func=Name(id='U',
                                                          ctx=Load()),
                                                args=[],
                                                keywords=[],
                                                starargs=None,
                                                kwargs=None))],
                   nl=True),
             ClassDef(name='E',
                      bases=[Name(id='object',
                                  ctx=Load())],
                      body=[FunctionDef(name='__repr__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Return(value=Str(s='<U>'))],
                                        decorator_list=[])],
                      decorator_list=[]),
             TryExcept(body=[Print(dest=None,
                                   values=[UnaryOp(op=UAdd(),
                                                   operand=Call(func=Name(id='E',
                                                                          ctx=Load()),
                                                                args=[],
                                                                keywords=[],
                                                                starargs=None,
                                                                kwargs=None))],
                                   nl=True)],
                       handlers=[ExceptHandler(type=Name(id='TypeError',
                                                         ctx=Load()),
                                               name=None,
                                               body=[Print(dest=None,
                                                           values=[Str(s='no +')],
                                                           nl=True)])],
                       orelse=[])])
