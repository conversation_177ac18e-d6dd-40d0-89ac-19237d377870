Module(body=[ClassDef(name='SuperClass',
                      bases=[],
                      body=[FunctionDef(name='apply',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Return(value=Str(s='SuperClass'))],
                                        decorator_list=[])],
                      decorator_list=[]),
             ClassDef(name='SubClassA',
                      bases=[Name(id='SuperClass',
                                  ctx=Load())],
                      body=[FunctionDef(name='apply',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Return(value=Str(s='SubClassA'))],
                                        decorator_list=[])],
                      decorator_list=[]),
             Assign(targets=[Name(id='x',
                                  ctx=Store())],
                    value=Call(func=Name(id='SubClassA',
                                         ctx=Load()),
                               args=[],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='x',
                                                          ctx=Load()),
                                               attr='apply',
                                               ctx=Load()),
                                args=[],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True)])
