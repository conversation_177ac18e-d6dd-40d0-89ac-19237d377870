Module(body=[Assign(targets=[Name(id='l',
                                  ctx=Store())],
                    value=List(elts=[Num(n=1),
                                     Num(n=2),
                                     Num(n=3),
                                     Num(n=4),
                                     Num(n=1),
                                     Num(n=1)],
                               ctx=Load())),
             Print(dest=None,
                   values=[Name(id='l',
                                ctx=Load())],
                   nl=True),
             Assign(targets=[Name(id='s',
                                  ctx=Store())],
                    value=Call(func=Name(id='set',
                                         ctx=Load()),
                               args=[Name(id='l',
                                          ctx=Load())],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Name(id='s',
                                ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='# self')],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='# forwards')],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='s',
                                                          ctx=Load()),
                                               attr='isdisjoint',
                                               ctx=Load()),
                                args=[Name(id='s',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='s',
                                             ctx=Load()),
                                   ops=[Gt()],
                                   comparators=[Name(id='s',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='s',
                                                          ctx=Load()),
                                               attr='issuperset',
                                               ctx=Load()),
                                args=[Name(id='s',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='s',
                                             ctx=Load()),
                                   ops=[GtE()],
                                   comparators=[Name(id='s',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='s',
                                             ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Name(id='s',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='s',
                                             ctx=Load()),
                                   ops=[NotEq()],
                                   comparators=[Name(id='s',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='s',
                                                          ctx=Load()),
                                               attr='issubset',
                                               ctx=Load()),
                                args=[Name(id='s',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='s',
                                             ctx=Load()),
                                   ops=[LtE()],
                                   comparators=[Name(id='s',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='s',
                                             ctx=Load()),
                                   ops=[Lt()],
                                   comparators=[Name(id='s',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='# backwards')],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='s',
                                                          ctx=Load()),
                                               attr='isdisjoint',
                                               ctx=Load()),
                                args=[Name(id='s',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='s',
                                             ctx=Load()),
                                   ops=[Gt()],
                                   comparators=[Name(id='s',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='s',
                                                          ctx=Load()),
                                               attr='issuperset',
                                               ctx=Load()),
                                args=[Name(id='s',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='s',
                                             ctx=Load()),
                                   ops=[GtE()],
                                   comparators=[Name(id='s',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='s',
                                             ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Name(id='s',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='s',
                                             ctx=Load()),
                                   ops=[NotEq()],
                                   comparators=[Name(id='s',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='s',
                                                          ctx=Load()),
                                               attr='issubset',
                                               ctx=Load()),
                                args=[Name(id='s',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='s',
                                             ctx=Load()),
                                   ops=[LtE()],
                                   comparators=[Name(id='s',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='s',
                                             ctx=Load()),
                                   ops=[Lt()],
                                   comparators=[Name(id='s',
                                                     ctx=Load())])],
                   nl=True)])
