Module(body=[Print(dest=None,
                   values=[Call(func=Name(id='int',
                                          ctx=Load()),
                                args=[Name(id='True',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='int',
                                          ctx=Load()),
                                args=[Name(id='False',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='float',
                                          ctx=Load()),
                                args=[Name(id='True',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='float',
                                          ctx=Load()),
                                args=[Name(id='False',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True)])
