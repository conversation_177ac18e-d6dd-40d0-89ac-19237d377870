Module(body=[ImportFrom(module='time',
                        names=[alias(name='sleep',
                                     asname=None)],
                        level=0),
             ClassDef(name='A',
                      bases=[Name(id='object',
                                  ctx=Load())],
                      body=[FunctionDef(name='__getattr__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param()),
                                                             Name(id='name',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Expr(value=Call(func=Name(id='sleep',
                                                                        ctx=Load()),
                                                              args=[Num(n=0.001)],
                                                              keywords=[],
                                                              starargs=None,
                                                              kwargs=None)),
                                              Print(dest=None,
                                                    values=[BinOp(left=Str(s='Getting %s'),
                                                                  op=Mod(),
                                                                  right=Name(id='name',
                                                                             ctx=Load()))],
                                                    nl=True),
                                              Return(value=Name(id='name',
                                                                ctx=Load()))],
                                        decorator_list=[]),
                            FunctionDef(name='__setattr__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param()),
                                                             Name(id='name',
                                                                  ctx=Param()),
                                                             Name(id='value',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Expr(value=Call(func=Name(id='sleep',
                                                                        ctx=Load()),
                                                              args=[Num(n=0.001)],
                                                              keywords=[],
                                                              starargs=None,
                                                              kwargs=None)),
                                              Print(dest=None,
                                                    values=[BinOp(left=Str(s='Setting %s to %s'),
                                                                  op=Mod(),
                                                                  right=Tuple(elts=[Name(id='name',
                                                                                         ctx=Load()),
                                                                                    Name(id='value',
                                                                                         ctx=Load())],
                                                                              ctx=Load()))],
                                                    nl=True),
                                              Expr(value=Call(func=Attribute(value=Name(id='object',
                                                                                        ctx=Load()),
                                                                             attr='__setattr__',
                                                                             ctx=Load()),
                                                              args=[Name(id='self',
                                                                         ctx=Load()),
                                                                    Name(id='name',
                                                                         ctx=Load()),
                                                                    Name(id='value',
                                                                         ctx=Load())],
                                                              keywords=[],
                                                              starargs=None,
                                                              kwargs=None))],
                                        decorator_list=[])],
                      decorator_list=[]),
             ClassDef(name='B',
                      bases=[Name(id='A',
                                  ctx=Load())],
                      body=[Pass()],
                      decorator_list=[]),
             ClassDef(name='C',
                      bases=[Name(id='A',
                                  ctx=Load())],
                      body=[FunctionDef(name='__getattribute__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param()),
                                                             Name(id='name',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Expr(value=Call(func=Name(id='sleep',
                                                                        ctx=Load()),
                                                              args=[Num(n=0.001)],
                                                              keywords=[],
                                                              starargs=None,
                                                              kwargs=None)),
                                              Print(dest=None,
                                                    values=[BinOp(left=Str(s='Getting %s early'),
                                                                  op=Mod(),
                                                                  right=Name(id='name',
                                                                             ctx=Load()))],
                                                    nl=True),
                                              Return(value=Str(s='FOO'))],
                                        decorator_list=[])],
                      decorator_list=[]),
             Assign(targets=[Name(id='b',
                                  ctx=Store())],
                    value=Call(func=Name(id='B',
                                         ctx=Load()),
                               args=[],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[BinOp(left=Str(s='b.x = %s'),
                                 op=Mod(),
                                 right=Attribute(value=Name(id='b',
                                                            ctx=Load()),
                                                 attr='x',
                                                 ctx=Load()))],
                   nl=True),
             Assign(targets=[Attribute(value=Name(id='b',
                                                  ctx=Load()),
                                       attr='x',
                                       ctx=Store())],
                    value=Str(s='BAR')),
             Print(dest=None,
                   values=[BinOp(left=Str(s='b.x = %s'),
                                 op=Mod(),
                                 right=Attribute(value=Name(id='b',
                                                            ctx=Load()),
                                                 attr='x',
                                                 ctx=Load()))],
                   nl=True),
             Assign(targets=[Name(id='c',
                                  ctx=Store())],
                    value=Call(func=Name(id='C',
                                         ctx=Load()),
                               args=[],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[BinOp(left=Str(s='c.x = %s'),
                                 op=Mod(),
                                 right=Attribute(value=Name(id='c',
                                                            ctx=Load()),
                                                 attr='x',
                                                 ctx=Load()))],
                   nl=True),
             Assign(targets=[Attribute(value=Name(id='c',
                                                  ctx=Load()),
                                       attr='x',
                                       ctx=Store())],
                    value=Str(s='BAR')),
             Print(dest=None,
                   values=[BinOp(left=Str(s='c.x = %s'),
                                 op=Mod(),
                                 right=Attribute(value=Name(id='c',
                                                            ctx=Load()),
                                                 attr='x',
                                                 ctx=Load()))],
                   nl=True)])
