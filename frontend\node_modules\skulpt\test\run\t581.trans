Module(body=[Assign(targets=[Name(id='set_1',
                                  ctx=Store())],
                    value=Call(func=Name(id='set',
                                         ctx=Load()),
                               args=[List(elts=[Tuple(elts=[],
                                                      ctx=Load()),
                                                <PERSON><PERSON>(elts=[Num(n=1)],
                                                      ctx=Load()),
                                                <PERSON><PERSON>(elts=[Num(n=5)],
                                                      ctx=Load()),
                                                <PERSON><PERSON>(elts=[Num(n=1),
                                                            Num(n=2)],
                                                      ctx=Load()),
                                                <PERSON><PERSON>(elts=[Num(n=2),
                                                            Num(n=2)],
                                                      ctx=Load()),
                                                <PERSON><PERSON>(elts=[Num(n=1),
                                                            Num(n=2),
                                                            Num(n=2)],
                                                      ctx=Load())],
                                          ctx=Load())],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Assign(targets=[Name(id='set_2',
                                  ctx=Store())],
                    value=Call(func=Name(id='set',
                                         ctx=Load()),
                               args=[List(elts=[Tuple(elts=[],
                                                      ctx=Load()),
                                                <PERSON>ple(elts=[Num(n=1)],
                                                      ctx=Load()),
                                                Tuple(elts=[Num(n=2)],
                                                      ctx=Load()),
                                                Tuple(elts=[Num(n=1),
                                                            Num(n=2)],
                                                      ctx=Load()),
                                                Tuple(elts=[Num(n=2),
                                                            Num(n=2)],
                                                      ctx=Load()),
                                                Tuple(elts=[Num(n=1),
                                                            Num(n=2),
                                                            Num(n=2)],
                                                      ctx=Load())],
                                          ctx=Load())],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Compare(left=Name(id='set_1',
                                             ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Name(id='set_2',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='set_1',
                                             ctx=Load()),
                                   ops=[NotEq()],
                                   comparators=[Name(id='set_2',
                                                     ctx=Load())])],
                   nl=True),
             Assign(targets=[Name(id='set_1',
                                  ctx=Store())],
                    value=Call(func=Name(id='set',
                                         ctx=Load()),
                               args=[List(elts=[Tuple(elts=[],
                                                      ctx=Load()),
                                                Tuple(elts=[Num(n=1)],
                                                      ctx=Load()),
                                                Tuple(elts=[Num(n=2)],
                                                      ctx=Load()),
                                                Tuple(elts=[Num(n=1),
                                                            Num(n=2)],
                                                      ctx=Load()),
                                                Tuple(elts=[Num(n=2),
                                                            Num(n=2)],
                                                      ctx=Load()),
                                                Tuple(elts=[Num(n=1),
                                                            Num(n=2),
                                                            Num(n=2)],
                                                      ctx=Load())],
                                          ctx=Load())],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Assign(targets=[Name(id='set_2',
                                  ctx=Store())],
                    value=Call(func=Name(id='set',
                                         ctx=Load()),
                               args=[List(elts=[Tuple(elts=[],
                                                      ctx=Load()),
                                                Tuple(elts=[Num(n=1)],
                                                      ctx=Load()),
                                                Tuple(elts=[Num(n=2)],
                                                      ctx=Load()),
                                                Tuple(elts=[Num(n=1),
                                                            Num(n=2)],
                                                      ctx=Load()),
                                                Tuple(elts=[Num(n=2),
                                                            Num(n=2)],
                                                      ctx=Load()),
                                                Tuple(elts=[Num(n=1),
                                                            Num(n=2),
                                                            Num(n=2)],
                                                      ctx=Load())],
                                          ctx=Load())],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Compare(left=Name(id='set_1',
                                             ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Name(id='set_2',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='set_1',
                                             ctx=Load()),
                                   ops=[NotEq()],
                                   comparators=[Name(id='set_2',
                                                     ctx=Load())])],
                   nl=True)])
