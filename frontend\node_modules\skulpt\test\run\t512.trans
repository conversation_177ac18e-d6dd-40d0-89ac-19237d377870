Module(body=[Expr(value=Call(func=Name(id='sorted',
                                       ctx=Load()),
                             args=[List(elts=[Num(n=1),
                                              Num(n=2),
                                              Num(n=3)],
                                        ctx=Load())],
                             keywords=[keyword(arg='kesy',
                                               value=Lambda(args=arguments(args=[Name(id='x',
                                                                                      ctx=Param())],
                                                                           vararg=None,
                                                                           kwarg=None,
                                                                           defaults=[]),
                                                            body=UnaryOp(op=USub(),
                                                                         operand=Name(id='x',
                                                                                      ctx=Load())))),
                                       keyword(arg='reverse',
                                               value=Name(id='True',
                                                          ctx=Load()))],
                             starargs=None,
                             kwargs=None))])
