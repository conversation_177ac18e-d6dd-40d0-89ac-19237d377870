Module(body=[ClassDef(name='Counter',
                      bases=[],
                      body=[ClassDef(name='CounterIter',
                                     bases=[],
                                     body=[FunctionDef(name='__init__',
                                                       args=arguments(args=[Name(id='self',
                                                                                 ctx=Param()),
                                                                            Name(id='c',
                                                                                 ctx=Param())],
                                                                      vararg=None,
                                                                      kwarg=None,
                                                                      defaults=[]),
                                                       body=[Assign(targets=[Attribute(value=Name(id='self',
                                                                                                  ctx=Load()),
                                                                                       attr='c',
                                                                                       ctx=Store())],
                                                                    value=Name(id='c',
                                                                               ctx=Load())),
                                                             Assign(targets=[Attribute(value=Name(id='self',
                                                                                                  ctx=Load()),
                                                                                       attr='idx',
                                                                                       ctx=Store())],
                                                                    value=Num(n=0))],
                                                       decorator_list=[]),
                                           FunctionDef(name='__iter__',
                                                       args=arguments(args=[Name(id='self',
                                                                                 ctx=Param())],
                                                                      vararg=None,
                                                                      kwarg=None,
                                                                      defaults=[]),
                                                       body=[Return(value=Name(id='self',
                                                                               ctx=Load()))],
                                                       decorator_list=[]),
                                           FunctionDef(name='next',
                                                       args=arguments(args=[Name(id='self',
                                                                                 ctx=Param())],
                                                                      vararg=None,
                                                                      kwarg=None,
                                                                      defaults=[]),
                                                       body=[Assign(targets=[Name(id='n',
                                                                                  ctx=Store())],
                                                                    value=Attribute(value=Name(id='self',
                                                                                               ctx=Load()),
                                                                                    attr='idx',
                                                                                    ctx=Load())),
                                                             AugAssign(target=Attribute(value=Name(id='self',
                                                                                                   ctx=Load()),
                                                                                        attr='idx',
                                                                                        ctx=Store()),
                                                                       op=Add(),
                                                                       value=Num(n=1)),
                                                             If(test=Compare(left=Name(id='n',
                                                                                       ctx=Load()),
                                                                             ops=[Gt()],
                                                                             comparators=[Attribute(value=Attribute(value=Name(id='self',
                                                                                                                               ctx=Load()),
                                                                                                                    attr='c',
                                                                                                                    ctx=Load()),
                                                                                                    attr='stop',
                                                                                                    ctx=Load())]),
                                                                body=[Raise(type=Name(id='StopIteration',
                                                                                      ctx=Load()),
                                                                            inst=None,
                                                                            tback=None)],
                                                                orelse=[]),
                                                             Return(value=Name(id='n',
                                                                               ctx=Load()))],
                                                       decorator_list=[])],
                                     decorator_list=[]),
                            FunctionDef(name='__init__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param()),
                                                             Name(id='stop',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Assign(targets=[Attribute(value=Name(id='self',
                                                                                   ctx=Load()),
                                                                        attr='count',
                                                                        ctx=Store())],
                                                     value=Num(n=0)),
                                              Assign(targets=[Attribute(value=Name(id='self',
                                                                                   ctx=Load()),
                                                                        attr='stop',
                                                                        ctx=Store())],
                                                     value=Name(id='stop',
                                                                ctx=Load())),
                                              Assign(targets=[Attribute(value=Name(id='self',
                                                                                   ctx=Load()),
                                                                        attr='dict',
                                                                        ctx=Store())],
                                                     value=Dict(keys=[],
                                                                values=[]))],
                                        decorator_list=[]),
                            FunctionDef(name='__iter__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Return(value=Call(func=Attribute(value=Name(id='self',
                                                                                          ctx=Load()),
                                                                               attr='CounterIter',
                                                                               ctx=Load()),
                                                                args=[Name(id='self',
                                                                           ctx=Load())],
                                                                keywords=[],
                                                                starargs=None,
                                                                kwargs=None))],
                                        decorator_list=[]),
                            FunctionDef(name='__len__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Return(value=Attribute(value=Name(id='self',
                                                                                ctx=Load()),
                                                                     attr='count',
                                                                     ctx=Load()))],
                                        decorator_list=[]),
                            FunctionDef(name='__repr__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Return(value=BinOp(left=BinOp(left=BinOp(left=BinOp(left=Str(s='< Counter Object: ('),
                                                                                                  op=Add(),
                                                                                                  right=Call(func=Name(id='str',
                                                                                                                       ctx=Load()),
                                                                                                             args=[Attribute(value=Name(id='self',
                                                                                                                                        ctx=Load()),
                                                                                                                             attr='count',
                                                                                                                             ctx=Load())],
                                                                                                             keywords=[],
                                                                                                             starargs=None,
                                                                                                             kwargs=None)),
                                                                                       op=Add(),
                                                                                       right=Str(s=',')),
                                                                            op=Add(),
                                                                            right=Call(func=Name(id='str',
                                                                                                 ctx=Load()),
                                                                                       args=[Attribute(value=Name(id='self',
                                                                                                                  ctx=Load()),
                                                                                                       attr='stop',
                                                                                                       ctx=Load())],
                                                                                       keywords=[],
                                                                                       starargs=None,
                                                                                       kwargs=None)),
                                                                 op=Add(),
                                                                 right=Str(s=') >')))],
                                        decorator_list=[]),
                            FunctionDef(name='__str__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Return(value=BinOp(left=BinOp(left=BinOp(left=BinOp(left=Str(s='('),
                                                                                                  op=Add(),
                                                                                                  right=Call(func=Name(id='str',
                                                                                                                       ctx=Load()),
                                                                                                             args=[Attribute(value=Name(id='self',
                                                                                                                                        ctx=Load()),
                                                                                                                             attr='count',
                                                                                                                             ctx=Load())],
                                                                                                             keywords=[],
                                                                                                             starargs=None,
                                                                                                             kwargs=None)),
                                                                                       op=Add(),
                                                                                       right=Str(s=',')),
                                                                            op=Add(),
                                                                            right=Call(func=Name(id='str',
                                                                                                 ctx=Load()),
                                                                                       args=[Attribute(value=Name(id='self',
                                                                                                                  ctx=Load()),
                                                                                                       attr='stop',
                                                                                                       ctx=Load())],
                                                                                       keywords=[],
                                                                                       starargs=None,
                                                                                       kwargs=None)),
                                                                 op=Add(),
                                                                 right=Str(s=')')))],
                                        decorator_list=[]),
                            FunctionDef(name='__call__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param()),
                                                             Name(id='x',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[For(target=Name(id='i',
                                                              ctx=Store()),
                                                  iter=Call(func=Name(id='range',
                                                                      ctx=Load()),
                                                            args=[Name(id='x',
                                                                       ctx=Load())],
                                                            keywords=[],
                                                            starargs=None,
                                                            kwargs=None),
                                                  body=[If(test=Compare(left=BinOp(left=Name(id='i',
                                                                                             ctx=Load()),
                                                                                   op=Mod(),
                                                                                   right=Num(n=2)),
                                                                        ops=[NotEq()],
                                                                        comparators=[Num(n=0)]),
                                                           body=[Continue()],
                                                           orelse=[]),
                                                        Assign(targets=[Subscript(value=Attribute(value=Name(id='self',
                                                                                                             ctx=Load()),
                                                                                                  attr='dict',
                                                                                                  ctx=Load()),
                                                                                  slice=Index(value=Name(id='i',
                                                                                                         ctx=Load())),
                                                                                  ctx=Store())],
                                                               value=BinOp(left=Name(id='i',
                                                                                     ctx=Load()),
                                                                           op=Add(),
                                                                           right=Num(n=1)))],
                                                  orelse=[])],
                                        decorator_list=[]),
                            FunctionDef(name='__getitem__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param()),
                                                             Name(id='key',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[If(test=Compare(left=Name(id='key',
                                                                        ctx=Load()),
                                                              ops=[In()],
                                                              comparators=[Attribute(value=Name(id='self',
                                                                                                ctx=Load()),
                                                                                     attr='dict',
                                                                                     ctx=Load())]),
                                                 body=[Return(value=Subscript(value=Attribute(value=Name(id='self',
                                                                                                         ctx=Load()),
                                                                                              attr='dict',
                                                                                              ctx=Load()),
                                                                              slice=Index(value=Name(id='key',
                                                                                                     ctx=Load())),
                                                                              ctx=Load()))],
                                                 orelse=[]),
                                              Return(value=Num(n=-1))],
                                        decorator_list=[]),
                            FunctionDef(name='__setitem__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param()),
                                                             Name(id='key',
                                                                  ctx=Param()),
                                                             Name(id='value',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Assign(targets=[Subscript(value=Attribute(value=Name(id='self',
                                                                                                   ctx=Load()),
                                                                                        attr='dict',
                                                                                        ctx=Load()),
                                                                        slice=Index(value=Name(id='key',
                                                                                               ctx=Load())),
                                                                        ctx=Store())],
                                                     value=Name(id='value',
                                                                ctx=Load()))],
                                        decorator_list=[])],
                      decorator_list=[]),
             Assign(targets=[Name(id='a',
                                  ctx=Store())],
                    value=Call(func=Name(id='Counter',
                                         ctx=Load()),
                               args=[Num(n=10)],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             For(target=Name(id='x',
                             ctx=Store()),
                 iter=Name(id='a',
                           ctx=Load()),
                 body=[Print(dest=None,
                             values=[Name(id='x',
                                          ctx=Load())],
                             nl=True)],
                 orelse=[]),
             Print(dest=None,
                   values=[Call(func=Name(id='len',
                                          ctx=Load()),
                                args=[Name(id='a',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Name(id='a',
                                ctx=Load()),
                           Call(func=Name(id='str',
                                          ctx=Load()),
                                args=[Name(id='a',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Call(func=Name(id='repr',
                                          ctx=Load()),
                                args=[Name(id='a',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Expr(value=Call(func=Name(id='a',
                                       ctx=Load()),
                             args=[Num(n=20)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Subscript(value=Name(id='a',
                                                ctx=Load()),
                                     slice=Index(value=Num(n=5)),
                                     ctx=Load()),
                           Subscript(value=Name(id='a',
                                                ctx=Load()),
                                     slice=Index(value=Num(n=8)),
                                     ctx=Load()),
                           Subscript(value=Name(id='a',
                                                ctx=Load()),
                                     slice=Index(value=Num(n=30)),
                                     ctx=Load())],
                   nl=True),
             Assign(targets=[Subscript(value=Name(id='a',
                                                  ctx=Load()),
                                       slice=Index(value=Num(n=30)),
                                       ctx=Store())],
                    value=Str(s='thirty')),
             Print(dest=None,
                   values=[Subscript(value=Name(id='a',
                                                ctx=Load()),
                                     slice=Index(value=Num(n=30)),
                                     ctx=Load())],
                   nl=True),
             Assign(targets=[Name(id='b',
                                  ctx=Store())],
                    value=Call(func=Name(id='Counter',
                                         ctx=Load()),
                               args=[Num(n=5)],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Assign(targets=[Name(id='c',
                                  ctx=Store())],
                    value=Call(func=Name(id='Counter',
                                         ctx=Load()),
                               args=[Num(n=5)],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='list',
                                          ctx=Load()),
                                args=[Name(id='b',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='sum',
                                          ctx=Load()),
                                args=[Name(id='c',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='b',
                                                          ctx=Load()),
                                               attr='__len__',
                                               ctx=Load()),
                                args=[],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='b',
                                                          ctx=Load()),
                                               attr='__str__',
                                               ctx=Load()),
                                args=[],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='b',
                                                          ctx=Load()),
                                               attr='__repr__',
                                               ctx=Load()),
                                args=[],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Expr(value=Call(func=Attribute(value=Name(id='b',
                                                       ctx=Load()),
                                            attr='__call__',
                                            ctx=Load()),
                             args=[Num(n=10)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='b',
                                                          ctx=Load()),
                                               attr='__getitem__',
                                               ctx=Load()),
                                args=[Num(n=4)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='b',
                                                          ctx=Load()),
                                               attr='__getitem__',
                                               ctx=Load()),
                                args=[Num(n=15)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Expr(value=Call(func=Attribute(value=Name(id='b',
                                                       ctx=Load()),
                                            attr='__setitem__',
                                            ctx=Load()),
                             args=[Num(n=15),
                                   Str(s='hello')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='b',
                                                          ctx=Load()),
                                               attr='__getitem__',
                                               ctx=Load()),
                                args=[Num(n=15)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True)])
