Module(body=[Assign(targets=[Name(id='lst',
                                  ctx=Store())],
                    value=List(elts=[Num(n=3),
                                     Num(n=6),
                                     Num(n=2),
                                     Num(n=1),
                                     Num(n=0)],
                               ctx=Load())),
             Assign(targets=[Name(id='lst2',
                                  ctx=Store())],
                    value=List(elts=[Num(n=1),
                                     Num(n=2)],
                               ctx=Load())),
             Print(dest=None,
                   values=[Call(func=Name(id='map',
                                          ctx=Load()),
                                args=[Name(id='bool',
                                           ctx=Load()),
                                      Name(id='lst',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             FunctionDef(name='outer',
                         args=arguments(args=[],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[]),
                         body=[FunctionDef(name='inner',
                                           args=arguments(args=[Name(id='item',
                                                                     ctx=Param())],
                                                          vararg=None,
                                                          kwarg=None,
                                                          defaults=[]),
                                           body=[Return(value=BinOp(left=Num(n=2),
                                                                    op=Mult(),
                                                                    right=Name(id='item',
                                                                               ctx=Load())))],
                                           decorator_list=[]),
                               Print(dest=None,
                                     values=[Call(func=Name(id='map',
                                                            ctx=Load()),
                                                  args=[Name(id='inner',
                                                             ctx=Load()),
                                                        Name(id='lst',
                                                             ctx=Load())],
                                                  keywords=[],
                                                  starargs=None,
                                                  kwargs=None)],
                                     nl=True)],
                         decorator_list=[]),
             Expr(value=Call(func=Name(id='outer',
                                       ctx=Load()),
                             args=[],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Call(func=Name(id='map',
                                          ctx=Load()),
                                args=[Name(id='None',
                                           ctx=Load()),
                                      Name(id='lst',
                                           ctx=Load()),
                                      Name(id='lst2',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='filter',
                                          ctx=Load()),
                                args=[Name(id='bool',
                                           ctx=Load()),
                                      List(elts=[Num(n=0),
                                                 Num(n=1),
                                                 Str(s=''),
                                                 Name(id='False',
                                                      ctx=Load()),
                                                 Num(n=42),
                                                 List(elts=[Num(n=1)],
                                                      ctx=Load())],
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Assign(targets=[Name(id='adder',
                                  ctx=Store())],
                    value=Num(n=100)),
             FunctionDef(name='add',
                         args=arguments(args=[Name(id='x',
                                                   ctx=Param()),
                                              Name(id='y',
                                                   ctx=Param())],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[]),
                         body=[Return(value=BinOp(left=BinOp(left=Name(id='x',
                                                                       ctx=Load()),
                                                             op=Add(),
                                                             right=Name(id='y',
                                                                        ctx=Load())),
                                                  op=Add(),
                                                  right=Name(id='adder',
                                                             ctx=Load())))],
                         decorator_list=[]),
             Print(dest=None,
                   values=[Call(func=Name(id='reduce',
                                          ctx=Load()),
                                args=[Name(id='add',
                                           ctx=Load()),
                                      List(elts=[Num(n=3),
                                                 Num(n=-7),
                                                 Num(n=1)],
                                           ctx=Load()),
                                      Num(n=100)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True)])
