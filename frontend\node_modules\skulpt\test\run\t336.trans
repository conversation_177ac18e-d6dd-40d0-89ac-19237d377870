Module(body=[FunctionDef(name='test',
                         args=arguments(args=[Name(id='i',
                                                   ctx=Param())],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[]),
                         body=[Assign(targets=[Name(id='f',
                                                    ctx=Store())],
                                      value=Num(n=3)),
                               TryExcept(body=[Return(value=Compare(left=Name(id='f',
                                                                              ctx=Load()),
                                                                    ops=[Eq()],
                                                                    comparators=[Num(n=5)]))],
                                         handlers=[ExceptHandler(type=Name(id='ValueError',
                                                                           ctx=Load()),
                                                                 name=None,
                                                                 body=[Return(value=Name(id='True',
                                                                                         ctx=Load()))])],
                                         orelse=[])],
                         decorator_list=[]),
             If(test=Call(func=Name(id='test',
                                    ctx=Load()),
                          args=[Num(n=12)],
                          keywords=[],
                          starargs=None,
                          kwargs=None),
                body=[Print(dest=None,
                            values=[Str(s='Is true')],
                            nl=True)],
                orelse=[Print(dest=None,
                              values=[Str(s='Is false')],
                              nl=True)])])
