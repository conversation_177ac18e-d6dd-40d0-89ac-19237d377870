Module(body=[Print(dest=None,
                   values=[Subscript(value=List(elts=[Num(n=1),
                                                      Num(n=2)],
                                                ctx=Load()),
                                     slice=Index(value=Name(id='True',
                                                            ctx=Load())),
                                     ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Subscript(value=List(elts=[Num(n=1),
                                                      Num(n=2)],
                                                ctx=Load()),
                                     slice=Index(value=Name(id='False',
                                                            ctx=Load())),
                                     ctx=Load())],
                   nl=True)])
