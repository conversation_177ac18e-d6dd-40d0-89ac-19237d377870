Module(body=[Print(dest=None,
                   values=[Call(func=Name(id='float',
                                          ctx=Load()),
                                args=[Str(s='-inf')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Assign(targets=[Name(id='l',
                                  ctx=Store())],
                    value=List(elts=[Call(func=Name(id='float',
                                                    ctx=Load()),
                                          args=[Str(s='inf')],
                                          keywords=[],
                                          starargs=None,
                                          kwargs=None),
                                     Call(func=Name(id='float',
                                                    ctx=Load()),
                                          args=[Str(s='-inf')],
                                          keywords=[],
                                          starargs=None,
                                          kwargs=None)],
                               ctx=Load())),
             Print(dest=None,
                   values=[Name(id='l',
                                ctx=Load())],
                   nl=True)])
