{"name": "@tailwindcss/postcss", "version": "4.1.7", "description": "PostCSS plugin for Tailwind CSS, a utility-first CSS framework for rapidly building custom user interfaces", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/tailwindlabs/tailwindcss.git", "directory": "packages/@tailwindcss-postcss"}, "bugs": "https://github.com/tailwindlabs/tailwindcss/issues", "homepage": "https://tailwindcss.com", "files": ["dist/"], "publishConfig": {"provenance": true, "access": "public"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "dependencies": {"@alloc/quick-lru": "^5.2.0", "postcss": "^8.4.41", "@tailwindcss/node": "4.1.7", "@tailwindcss/oxide": "4.1.7", "tailwindcss": "4.1.7"}, "devDependencies": {"@types/node": "^20.14.8", "@types/postcss-import": "14.0.3", "dedent": "1.6.0", "postcss-import": "^16.1.0", "internal-example-plugin": "0.0.0"}, "scripts": {"lint": "tsc --noEmit", "build": "tsup-node", "dev": "pnpm run build -- --watch"}}