Module(body=[Import(names=[alias(name='random',
                                 asname=None)]),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='randint',
                                               ctx=Load()),
                                args=[Num(n=40),
                                      Num(n=70)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='randint',
                                               ctx=Load()),
                                args=[Num(n=-40),
                                      Num(n=-70)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True)])
