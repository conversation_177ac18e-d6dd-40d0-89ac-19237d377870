Module(body=[Assign(targets=[Name(id='d',
                                  ctx=Store())],
                    value=Dict(keys=[Dict(keys=[Num(n=1)],
                                          values=[Num(n=2)])],
                               values=[Num(n=3)])),
             Print(dest=None,
                   values=[Name(id='d',
                                ctx=Load())],
                   nl=True),
             Assign(targets=[Subscript(value=Name(id='d',
                                                  ctx=Load()),
                                       slice=Index(value=List(elts=[Num(n=4),
                                                                    Num(n=5)],
                                                              ctx=Load())),
                                       ctx=Store())],
                    value=Num(n=6))])
