Module(body=[Print(dest=None,
                   values=[Num(n=10)],
                   nl=True),
             Print(dest=None,
                   values=[Num(n=-10)],
                   nl=True),
             Print(dest=None,
                   values=[Num(n=255)],
                   nl=True),
             Print(dest=None,
                   values=[Num(n=-15)],
                   nl=True),
             Print(dest=None,
                   values=[Num(n=51)],
                   nl=True),
             Print(dest=None,
                   values=[Num(n=-51)],
                   nl=True),
             Print(dest=None,
                   values=[Num(n=51)],
                   nl=True),
             Print(dest=None,
                   values=[Num(n=-51)],
                   nl=True),
             Print(dest=None,
                   values=[Num(n=255)],
                   nl=True),
             Print(dest=None,
                   values=[Num(n=-255)],
                   nl=True),
             Print(dest=None,
                   values=[Num(n=0)],
                   nl=True),
             Print(dest=None,
                   values=[Num(n=0)],
                   nl=True)])
