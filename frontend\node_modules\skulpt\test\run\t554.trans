Module(body=[ImportFrom(module='time',
                        names=[alias(name='sleep',
                                     asname=None)],
                        level=0),
             ClassDef(name='A',
                      bases=[],
                      body=[FunctionDef(name='__getitem__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param()),
                                                             Name(id='item',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[If(test=Call(func=Name(id='isinstance',
                                                                     ctx=Load()),
                                                           args=[Name(id='item',
                                                                      ctx=Load()),
                                                                 Name(id='str',
                                                                      ctx=Load())],
                                                           keywords=[],
                                                           starargs=None,
                                                           kwargs=None),
                                                 body=[Print(dest=None,
                                                             values=[Str(s='item is a string, as it should be')],
                                                             nl=True)],
                                                 orelse=[]),
                                              Print(dest=None,
                                                    values=[BinOp(left=Str(s='Getting '),
                                                                  op=Add(),
                                                                  right=Name(id='item',
                                                                             ctx=Load()))],
                                                    nl=True),
                                              Return(value=Num(n=42))],
                                        decorator_list=[]),
                            FunctionDef(name='__setitem__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param()),
                                                             Name(id='item',
                                                                  ctx=Param()),
                                                             Name(id='value',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[If(test=Call(func=Name(id='isinstance',
                                                                     ctx=Load()),
                                                           args=[Name(id='item',
                                                                      ctx=Load()),
                                                                 Name(id='str',
                                                                      ctx=Load())],
                                                           keywords=[],
                                                           starargs=None,
                                                           kwargs=None),
                                                 body=[Print(dest=None,
                                                             values=[Str(s='attr is a string, as it should be')],
                                                             nl=True)],
                                                 orelse=[]),
                                              Print(dest=None,
                                                    values=[BinOp(left=BinOp(left=BinOp(left=Str(s='Intercepted attempt to set '),
                                                                                        op=Add(),
                                                                                        right=Name(id='item',
                                                                                                   ctx=Load())),
                                                                             op=Add(),
                                                                             right=Str(s=' to ')),
                                                                  op=Add(),
                                                                  right=Call(func=Name(id='str',
                                                                                       ctx=Load()),
                                                                             args=[Name(id='value',
                                                                                        ctx=Load())],
                                                                             keywords=[],
                                                                             starargs=None,
                                                                             kwargs=None))],
                                                    nl=True)],
                                        decorator_list=[])],
                      decorator_list=[]),
             Assign(targets=[Name(id='a',
                                  ctx=Store())],
                    value=Call(func=Name(id='A',
                                         ctx=Load()),
                               args=[],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Assign(targets=[Subscript(value=Name(id='a',
                                                  ctx=Load()),
                                       slice=Index(value=Str(s='x')),
                                       ctx=Store())],
                    value=Num(n=0)),
             Print(dest=None,
                   values=[BinOp(left=Str(s='a["x"] = '),
                                 op=Add(),
                                 right=Call(func=Name(id='str',
                                                      ctx=Load()),
                                            args=[Subscript(value=Name(id='a',
                                                                       ctx=Load()),
                                                            slice=Index(value=Str(s='x')),
                                                            ctx=Load())],
                                            keywords=[],
                                            starargs=None,
                                            kwargs=None))],
                   nl=True),
             ClassDef(name='B',
                      bases=[],
                      body=[FunctionDef(name='__getitem__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param()),
                                                             Name(id='item',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Print(dest=None,
                                                    values=[BinOp(left=Str(s='Getting '),
                                                                  op=Add(),
                                                                  right=Name(id='item',
                                                                             ctx=Load()))],
                                                    nl=True),
                                              Expr(value=Call(func=Name(id='sleep',
                                                                        ctx=Load()),
                                                              args=[Num(n=0.01)],
                                                              keywords=[],
                                                              starargs=None,
                                                              kwargs=None)),
                                              Return(value=Num(n=42))],
                                        decorator_list=[]),
                            FunctionDef(name='__setitem__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param()),
                                                             Name(id='item',
                                                                  ctx=Param()),
                                                             Name(id='value',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Print(dest=None,
                                                    values=[BinOp(left=BinOp(left=BinOp(left=Str(s='Intercepted attempt to set '),
                                                                                        op=Add(),
                                                                                        right=Name(id='item',
                                                                                                   ctx=Load())),
                                                                             op=Add(),
                                                                             right=Str(s=' to ')),
                                                                  op=Add(),
                                                                  right=Call(func=Name(id='str',
                                                                                       ctx=Load()),
                                                                             args=[Name(id='value',
                                                                                        ctx=Load())],
                                                                             keywords=[],
                                                                             starargs=None,
                                                                             kwargs=None))],
                                                    nl=True),
                                              Expr(value=Call(func=Name(id='sleep',
                                                                        ctx=Load()),
                                                              args=[Num(n=0.01)],
                                                              keywords=[],
                                                              starargs=None,
                                                              kwargs=None))],
                                        decorator_list=[])],
                      decorator_list=[]),
             Assign(targets=[Name(id='b',
                                  ctx=Store())],
                    value=Call(func=Name(id='B',
                                         ctx=Load()),
                               args=[],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Assign(targets=[Subscript(value=Name(id='b',
                                                  ctx=Load()),
                                       slice=Index(value=Str(s='x')),
                                       ctx=Store())],
                    value=Num(n=0)),
             Print(dest=None,
                   values=[BinOp(left=Str(s='b["x"] = '),
                                 op=Add(),
                                 right=Call(func=Name(id='str',
                                                      ctx=Load()),
                                            args=[Subscript(value=Name(id='b',
                                                                       ctx=Load()),
                                                            slice=Index(value=Str(s='x')),
                                                            ctx=Load())],
                                            keywords=[],
                                            starargs=None,
                                            kwargs=None))],
                   nl=True),
             AugAssign(target=Subscript(value=Name(id='b',
                                                   ctx=Load()),
                                        slice=Index(value=Str(s='x')),
                                        ctx=Store()),
                       op=Add(),
                       value=Num(n=1)),
             Print(dest=None,
                   values=[BinOp(left=Str(s='b["x"] = '),
                                 op=Add(),
                                 right=Call(func=Name(id='str',
                                                      ctx=Load()),
                                            args=[Subscript(value=Name(id='b',
                                                                       ctx=Load()),
                                                            slice=Index(value=Str(s='x')),
                                                            ctx=Load())],
                                            keywords=[],
                                            starargs=None,
                                            kwargs=None))],
                   nl=True)])
