Module(body=[Assign(targets=[Name(id='l',
                                  ctx=Store())],
                    value=Call(func=Name(id='range',
                                         ctx=Load()),
                               args=[Num(n=10)],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Name(id='l',
                                ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='slice',
                                          ctx=Load()),
                                args=[Num(n=6)],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Subscript(value=Name(id='l',
                                                ctx=Load()),
                                     slice=Index(value=Call(func=Name(id='slice',
                                                                      ctx=Load()),
                                                            args=[Num(n=6)],
                                                            keywords=[],
                                                            starargs=None,
                                                            kwargs=None)),
                                     ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='slice',
                                          ctx=Load()),
                                args=[Num(n=1),
                                      Num(n=6)],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Subscript(value=Name(id='l',
                                                ctx=Load()),
                                     slice=Index(value=Call(func=Name(id='slice',
                                                                      ctx=Load()),
                                                            args=[Num(n=1),
                                                                  Num(n=6)],
                                                            keywords=[],
                                                            starargs=None,
                                                            kwargs=None)),
                                     ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='slice',
                                          ctx=Load()),
                                args=[Num(n=1),
                                      Num(n=6),
                                      Num(n=2)],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Subscript(value=Name(id='l',
                                                ctx=Load()),
                                     slice=Index(value=Call(func=Name(id='slice',
                                                                      ctx=Load()),
                                                            args=[Num(n=1),
                                                                  Num(n=6),
                                                                  Num(n=2)],
                                                            keywords=[],
                                                            starargs=None,
                                                            kwargs=None)),
                                     ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='slice',
                                          ctx=Load()),
                                args=[Num(n=-6)],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Subscript(value=Name(id='l',
                                                ctx=Load()),
                                     slice=Index(value=Call(func=Name(id='slice',
                                                                      ctx=Load()),
                                                            args=[Num(n=-6)],
                                                            keywords=[],
                                                            starargs=None,
                                                            kwargs=None)),
                                     ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='slice',
                                          ctx=Load()),
                                args=[Num(n=1),
                                      Num(n=-6)],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Subscript(value=Name(id='l',
                                                ctx=Load()),
                                     slice=Index(value=Call(func=Name(id='slice',
                                                                      ctx=Load()),
                                                            args=[Num(n=1),
                                                                  Num(n=-6)],
                                                            keywords=[],
                                                            starargs=None,
                                                            kwargs=None)),
                                     ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='slice',
                                          ctx=Load()),
                                args=[Num(n=-1),
                                      Num(n=-6)],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Subscript(value=Name(id='l',
                                                ctx=Load()),
                                     slice=Index(value=Call(func=Name(id='slice',
                                                                      ctx=Load()),
                                                            args=[Num(n=-1),
                                                                  Num(n=-6)],
                                                            keywords=[],
                                                            starargs=None,
                                                            kwargs=None)),
                                     ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='slice',
                                          ctx=Load()),
                                args=[Num(n=-1),
                                      Num(n=6)],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Subscript(value=Name(id='l',
                                                ctx=Load()),
                                     slice=Index(value=Call(func=Name(id='slice',
                                                                      ctx=Load()),
                                                            args=[Num(n=-1),
                                                                  Num(n=6)],
                                                            keywords=[],
                                                            starargs=None,
                                                            kwargs=None)),
                                     ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='slice',
                                          ctx=Load()),
                                args=[Num(n=-1),
                                      Num(n=-6),
                                      Num(n=3)],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Subscript(value=Name(id='l',
                                                ctx=Load()),
                                     slice=Index(value=Call(func=Name(id='slice',
                                                                      ctx=Load()),
                                                            args=[Num(n=-1),
                                                                  Num(n=-6),
                                                                  Num(n=3)],
                                                            keywords=[],
                                                            starargs=None,
                                                            kwargs=None)),
                                     ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='slice',
                                          ctx=Load()),
                                args=[Num(n=-1),
                                      Num(n=-6),
                                      Num(n=-3)],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Subscript(value=Name(id='l',
                                                ctx=Load()),
                                     slice=Index(value=Call(func=Name(id='slice',
                                                                      ctx=Load()),
                                                            args=[Num(n=-1),
                                                                  Num(n=-6),
                                                                  Num(n=-3)],
                                                            keywords=[],
                                                            starargs=None,
                                                            kwargs=None)),
                                     ctx=Load())],
                   nl=True)])
