Module(body=[Assign(targets=[Name(id='t',
                                  ctx=Store())],
                    value=ListComp(elt=ListComp(elt=BinOp(left=BinOp(left=Name(id='y',
                                                                               ctx=Load()),
                                                                     op=Mult(),
                                                                     right=Num(n=10)),
                                                          op=Add(),
                                                          right=Name(id='x',
                                                                     ctx=Load())),
                                                generators=[comprehension(target=Name(id='x',
                                                                                      ctx=Store()),
                                                                          iter=Call(func=Name(id='range',
                                                                                              ctx=Load()),
                                                                                    args=[Num(n=0),
                                                                                          Num(n=10)],
                                                                                    keywords=[],
                                                                                    starargs=None,
                                                                                    kwargs=None),
                                                                          ifs=[])]),
                                   generators=[comprehension(target=Name(id='y',
                                                                         ctx=Store()),
                                                             iter=Call(func=Name(id='range',
                                                                                 ctx=Load()),
                                                                       args=[Num(n=0),
                                                                             Num(n=10)],
                                                                       keywords=[],
                                                                       starargs=None,
                                                                       kwargs=None),
                                                             ifs=[])])),
             Print(dest=None,
                   values=[Subscript(value=Subscript(value=Name(id='t',
                                                                ctx=Load()),
                                                     slice=Index(value=Num(n=2)),
                                                     ctx=Load()),
                                     slice=Index(value=Num(n=3)),
                                     ctx=Load())],
                   nl=True)])
