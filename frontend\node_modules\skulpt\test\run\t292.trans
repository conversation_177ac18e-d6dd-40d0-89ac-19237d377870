Module(body=[FunctionDef(name='mygen',
                         args=arguments(args=[Name(id='upto',
                                                   ctx=Param())],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[]),
                         body=[For(target=Name(id='i',
                                               ctx=Store()),
                                   iter=Call(func=Name(id='range',
                                                       ctx=Load()),
                                             args=[Num(n=0),
                                                   Name(id='upto',
                                                        ctx=Load())],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   body=[Print(dest=None,
                                               values=[Str(s='i'),
                                                       Name(id='i',
                                                            ctx=Load())],
                                               nl=True),
                                         Assign(targets=[Name(id='got',
                                                              ctx=Store())],
                                                value=Yield(value=Name(id='i',
                                                                       ctx=Load()))),
                                         Print(dest=None,
                                               values=[Str(s='got'),
                                                       Name(id='got',
                                                            ctx=Load())],
                                               nl=True)],
                                   orelse=[])],
                         decorator_list=[]),
             Assign(targets=[Name(id='handle',
                                  ctx=Store())],
                    value=Call(func=Name(id='mygen',
                                         ctx=Load()),
                               args=[Num(n=3)],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Assign(targets=[Name(id='first',
                                  ctx=Store())],
                    value=Name(id='True',
                               ctx=Load())),
             For(target=Name(id='num',
                             ctx=Store()),
                 iter=Name(id='handle',
                           ctx=Load()),
                 body=[Print(dest=None,
                             values=[Str(s='num'),
                                     Name(id='num',
                                          ctx=Load())],
                             nl=True),
                       If(test=Name(id='first',
                                    ctx=Load()),
                          body=[Print(dest=None,
                                      values=[Str(s='signalling')],
                                      nl=True),
                                Assign(targets=[Name(id='foo',
                                                     ctx=Store())],
                                       value=Call(func=Attribute(value=Name(id='handle',
                                                                            ctx=Load()),
                                                                 attr='send',
                                                                 ctx=Load()),
                                                  args=[Str(s='sig')],
                                                  keywords=[],
                                                  starargs=None,
                                                  kwargs=None)),
                                Print(dest=None,
                                      values=[Str(s='foo'),
                                              Name(id='foo',
                                                   ctx=Load())],
                                      nl=True),
                                Assign(targets=[Name(id='first',
                                                     ctx=Store())],
                                       value=Name(id='False',
                                                  ctx=Load()))],
                          orelse=[])],
                 orelse=[])])
