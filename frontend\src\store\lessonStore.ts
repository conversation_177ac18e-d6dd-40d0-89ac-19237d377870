import { create } from 'zustand';

export interface Lesson {
  id: string;
  title: string;
  description: string;
  content: string;
  order: number;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  estimatedTime: number; // in minutes
  prerequisites: string[];
  exercises: Exercise[];
  isCompleted: boolean;
  isUnlocked: boolean;
}

export interface Exercise {
  id: string;
  title: string;
  description: string;
  starterCode: string;
  solution: string;
  hints: string[];
  testCases: TestCase[];
}

export interface TestCase {
  input: string;
  expectedOutput: string;
  description: string;
}

export interface Progress {
  lessonId: string;
  completed: boolean;
  score: number;
  timeSpent: number;
  completedAt?: Date;
}

interface LessonState {
  lessons: Lesson[];
  currentLesson: Lesson | null;
  progress: Progress[];
  isLoading: boolean;
  error: string | null;
  
  // Actions
  fetchLessons: () => Promise<void>;
  setCurrentLesson: (lessonId: string) => void;
  completeLesson: (lessonId: string, score: number, timeSpent: number) => Promise<void>;
  updateProgress: (lessonId: string, updates: Partial<Progress>) => void;
  resetProgress: () => void;
}

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000';

// Mock lessons data for development
const mockLessons: Lesson[] = [
  {
    id: '1',
    title: 'Welcome to Python!',
    description: 'Learn what Python is and write your first program',
    content: `# Welcome to Python Programming!

Python is a friendly programming language that's perfect for beginners. It's like giving instructions to a computer in a way that's easy to understand.

## What is Python?
Python is a programming language that helps us tell computers what to do. It's named after a comedy group called "Monty Python" - so it's meant to be fun!

## Your First Python Program
Let's start with the most famous program in programming - saying "Hello, World!"`,
    order: 1,
    difficulty: 'beginner',
    estimatedTime: 10,
    prerequisites: [],
    exercises: [
      {
        id: '1-1',
        title: 'Say Hello',
        description: 'Make Python say hello to the world!',
        starterCode: '# Type your code here\n',
        solution: 'print("Hello, World!")',
        hints: [
          'Use the print() function',
          'Put your message inside quotes',
          'Don\'t forget the parentheses!'
        ],
        testCases: [
          {
            input: '',
            expectedOutput: 'Hello, World!',
            description: 'Should print "Hello, World!"'
          }
        ]
      }
    ],
    isCompleted: false,
    isUnlocked: true,
  },
  {
    id: '2',
    title: 'Variables - Your Data Containers',
    description: 'Learn how to store and use information in variables',
    content: `# Variables - Your Data Containers

Variables are like boxes where we can store information. We can put different types of things in these boxes and use them later!

## What are Variables?
Think of variables as labeled boxes. You can put something in the box, and later you can look at what's inside by reading the label.

## Creating Variables
In Python, creating a variable is super easy! You just give it a name and assign a value.`,
    order: 2,
    difficulty: 'beginner',
    estimatedTime: 15,
    prerequisites: ['1'],
    exercises: [
      {
        id: '2-1',
        title: 'Create Your First Variable',
        description: 'Create a variable called "name" and store your name in it',
        starterCode: '# Create a variable called name\n# Then print it\n',
        solution: 'name = "Your Name"\nprint(name)',
        hints: [
          'Use the = sign to assign a value',
          'Put text in quotes',
          'Use print() to show the variable'
        ],
        testCases: [
          {
            input: '',
            expectedOutput: 'Your Name',
            description: 'Should print the name stored in the variable'
          }
        ]
      }
    ],
    isCompleted: false,
    isUnlocked: false,
  }
];

export const useLessonStore = create<LessonState>((set, get) => ({
  lessons: mockLessons,
  currentLesson: null,
  progress: [],
  isLoading: false,
  error: null,

  fetchLessons: async () => {
    set({ isLoading: true, error: null });
    try {
      // For now, use mock data
      // In production, this would fetch from the API
      set({ lessons: mockLessons, isLoading: false });
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Failed to fetch lessons',
        isLoading: false,
      });
    }
  },

  setCurrentLesson: (lessonId: string) => {
    const lessons = get().lessons;
    const lesson = lessons.find(l => l.id === lessonId);
    if (lesson) {
      set({ currentLesson: lesson });
    }
  },

  completeLesson: async (lessonId: string, score: number, timeSpent: number) => {
    const { progress, lessons } = get();
    
    // Update progress
    const existingProgress = progress.find(p => p.lessonId === lessonId);
    const newProgress: Progress = {
      lessonId,
      completed: true,
      score,
      timeSpent,
      completedAt: new Date(),
    };

    let updatedProgress;
    if (existingProgress) {
      updatedProgress = progress.map(p => 
        p.lessonId === lessonId ? newProgress : p
      );
    } else {
      updatedProgress = [...progress, newProgress];
    }

    // Update lesson completion status and unlock next lesson
    const updatedLessons = lessons.map(lesson => {
      if (lesson.id === lessonId) {
        return { ...lesson, isCompleted: true };
      }
      
      // Unlock next lesson if prerequisites are met
      const prereqsMet = lesson.prerequisites.every(prereqId => 
        updatedProgress.some(p => p.lessonId === prereqId && p.completed)
      );
      
      if (prereqsMet) {
        return { ...lesson, isUnlocked: true };
      }
      
      return lesson;
    });

    set({ 
      progress: updatedProgress, 
      lessons: updatedLessons 
    });
  },

  updateProgress: (lessonId: string, updates: Partial<Progress>) => {
    const progress = get().progress;
    const existingProgress = progress.find(p => p.lessonId === lessonId);
    
    if (existingProgress) {
      const updatedProgress = progress.map(p => 
        p.lessonId === lessonId ? { ...p, ...updates } : p
      );
      set({ progress: updatedProgress });
    } else {
      const newProgress: Progress = {
        lessonId,
        completed: false,
        score: 0,
        timeSpent: 0,
        ...updates,
      };
      set({ progress: [...progress, newProgress] });
    }
  },

  resetProgress: () => {
    set({ progress: [] });
  },
}));
