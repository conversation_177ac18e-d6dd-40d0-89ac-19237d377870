Module(body=[Print(dest=None,
                   values=[Call(func=Name(id='isinstance',
                                          ctx=Load()),
                                args=[Num(n=3),
                                      Name(id='int',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='isinstance',
                                          ctx=Load()),
                                args=[Num(n=3.1),
                                      Name(id='float',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='isinstance',
                                          ctx=Load()),
                                args=[Num(n=3),
                                      Call(func=Name(id='type',
                                                     ctx=Load()),
                                           args=[Num(n=3)],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='isinstance',
                                          ctx=Load()),
                                args=[Num(n=3.1),
                                      Call(func=Name(id='type',
                                                     ctx=Load()),
                                           args=[Num(n=3.1)],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='isinstance',
                                          ctx=Load()),
                                args=[Str(s='foo'),
                                      Name(id='str',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='isinstance',
                                          ctx=Load()),
                                args=[Str(s='foo'),
                                      Call(func=Name(id='type',
                                                     ctx=Load()),
                                           args=[Str(s='foo')],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='isinstance',
                                          ctx=Load()),
                                args=[List(elts=[],
                                           ctx=Load()),
                                      Name(id='list',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='isinstance',
                                          ctx=Load()),
                                args=[List(elts=[],
                                           ctx=Load()),
                                      Call(func=Name(id='type',
                                                     ctx=Load()),
                                           args=[List(elts=[Num(n=1),
                                                            Num(n=2),
                                                            Num(n=3)],
                                                      ctx=Load())],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='isinstance',
                                          ctx=Load()),
                                args=[Name(id='None',
                                           ctx=Load()),
                                      Call(func=Name(id='type',
                                                     ctx=Load()),
                                           args=[Name(id='None',
                                                      ctx=Load())],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='isinstance',
                                          ctx=Load()),
                                args=[Name(id='True',
                                           ctx=Load()),
                                      Name(id='bool',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='isinstance',
                                          ctx=Load()),
                                args=[Name(id='True',
                                           ctx=Load()),
                                      Call(func=Name(id='type',
                                                     ctx=Load()),
                                           args=[Name(id='True',
                                                      ctx=Load())],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True)])
