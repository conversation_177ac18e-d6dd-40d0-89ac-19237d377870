Module(body=[Assign(targets=[Name(id='l',
                                  ctx=Store())],
                    value=List(elts=[Num(n=1),
                                     Num(n=2),
                                     Num(n=3),
                                     Num(n=4)],
                               ctx=Load())),
             Assign(targets=[Name(id='t',
                                  ctx=Store())],
                    value=Tuple(elts=[Num(n=1),
                                      Num(n=2),
                                      Num(n=3),
                                      Num(n=4)],
                                ctx=Load())),
             Assign(targets=[Name(id='d',
                                  ctx=Store())],
                    value=Dict(keys=[Num(n=1),
                                     Num(n=3)],
                               values=[Num(n=2),
                                       Num(n=4)])),
             Assign(targets=[Name(id='s',
                                  ctx=Store())],
                    value=Str(s='1234')),
             Print(dest=None,
                   values=[Call(func=Name(id='zip',
                                          ctx=Load()),
                                args=[],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='zip',
                                          ctx=Load()),
                                args=[Name(id='l',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Call(func=Name(id='zip',
                                          ctx=Load()),
                                args=[Name(id='t',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Call(func=Name(id='zip',
                                          ctx=Load()),
                                args=[Name(id='d',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Call(func=Name(id='zip',
                                          ctx=Load()),
                                args=[Name(id='s',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='zip',
                                          ctx=Load()),
                                args=[Name(id='l',
                                           ctx=Load()),
                                      Name(id='t',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Call(func=Name(id='zip',
                                          ctx=Load()),
                                args=[Name(id='l',
                                           ctx=Load()),
                                      Name(id='d',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Call(func=Name(id='zip',
                                          ctx=Load()),
                                args=[Name(id='l',
                                           ctx=Load()),
                                      Name(id='s',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='zip',
                                          ctx=Load()),
                                args=[Name(id='t',
                                           ctx=Load()),
                                      Name(id='d',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Call(func=Name(id='zip',
                                          ctx=Load()),
                                args=[Name(id='t',
                                           ctx=Load()),
                                      Name(id='s',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='zip',
                                          ctx=Load()),
                                args=[Name(id='d',
                                           ctx=Load()),
                                      Name(id='s',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='zip',
                                          ctx=Load()),
                                args=[Name(id='l',
                                           ctx=Load()),
                                      Name(id='t',
                                           ctx=Load()),
                                      Name(id='s',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='zip',
                                          ctx=Load()),
                                args=[Name(id='l',
                                           ctx=Load()),
                                      Name(id='t',
                                           ctx=Load()),
                                      Name(id='s',
                                           ctx=Load()),
                                      Name(id='d',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Assign(targets=[Name(id='z',
                                  ctx=Store())],
                    value=Call(func=Name(id='zip',
                                         ctx=Load()),
                               args=[Name(id='l',
                                          ctx=Load()),
                                     Name(id='t',
                                          ctx=Load()),
                                     Name(id='s',
                                          ctx=Load())],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Call(func=Name(id='zip',
                                          ctx=Load()),
                                args=[],
                                keywords=[],
                                starargs=Name(id='z',
                                              ctx=Load()),
                                kwargs=None)],
                   nl=True),
             Assign(targets=[Name(id='z',
                                  ctx=Store())],
                    value=Call(func=Name(id='zip',
                                         ctx=Load()),
                               args=[Name(id='l',
                                          ctx=Load()),
                                     Name(id='t',
                                          ctx=Load()),
                                     Name(id='s',
                                          ctx=Load()),
                                     Name(id='d',
                                          ctx=Load())],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Call(func=Name(id='zip',
                                          ctx=Load()),
                                args=[],
                                keywords=[],
                                starargs=Name(id='z',
                                              ctx=Load()),
                                kwargs=None)],
                   nl=True)])
