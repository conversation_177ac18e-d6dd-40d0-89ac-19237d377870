Module(body=[Print(dest=None,
                   values=[Subscript(value=Subscript(value=List(elts=[Num(n=1),
                                                                      Num(n=2),
                                                                      Str(s='OK'),
                                                                      Num(n=4)],
                                                                ctx=Load()),
                                                     slice=Slice(lower=Num(n=-3),
                                                                 upper=Num(n=3),
                                                                 step=None),
                                                     ctx=Load()),
                                     slice=Index(value=Num(n=1)),
                                     ctx=Load())],
                   nl=True)])
