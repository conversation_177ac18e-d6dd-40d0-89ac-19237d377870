Module(body=[Import(names=[alias(name='math',
                                 asname=None)]),
             FunctionDef(name='isCloseTo',
                         args=arguments(args=[Name(id='expected',
                                                   ctx=Param()),
                                              Name(id='actual',
                                                   ctx=Param()),
                                              Name(id='precision',
                                                   ctx=Param())],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[]),
                         body=[Return(value=Compare(left=Call(func=Attribute(value=Name(id='math',
                                                                                        ctx=Load()),
                                                                             attr='fabs',
                                                                             ctx=Load()),
                                                              args=[BinOp(left=Name(id='expected',
                                                                                    ctx=Load()),
                                                                          op=Sub(),
                                                                          right=Name(id='actual',
                                                                                     ctx=Load()))],
                                                              keywords=[],
                                                              starargs=None,
                                                              kwargs=None),
                                                    ops=[Lt()],
                                                    comparators=[BinOp(left=Call(func=Attribute(value=Name(id='math',
                                                                                                           ctx=Load()),
                                                                                                attr='pow',
                                                                                                ctx=Load()),
                                                                                 args=[Num(n=10),
                                                                                       UnaryOp(op=USub(),
                                                                                               operand=Name(id='precision',
                                                                                                            ctx=Load()))],
                                                                                 keywords=[],
                                                                                 starargs=None,
                                                                                 kwargs=None),
                                                                       op=Div(),
                                                                       right=Num(n=2))]))],
                         decorator_list=[]),
             Print(dest=None,
                   values=[Str(s='\nmath.acos(x)')],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='acos',
                                               ctx=Load()),
                                args=[Num(n=1.0)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='acos',
                                               ctx=Load()),
                                args=[Num(n=0.5)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='acos',
                                               ctx=Load()),
                                args=[Num(n=0.0)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='acos',
                                               ctx=Load()),
                                args=[Num(n=-0.5)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='acos',
                                               ctx=Load()),
                                args=[Num(n=-1.0)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='acos',
                                               ctx=Load()),
                                args=[Num(n=-1)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\nmath.asin(x)')],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='asin',
                                               ctx=Load()),
                                args=[Num(n=1.0)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='asin',
                                               ctx=Load()),
                                args=[Num(n=0.5)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='asin',
                                               ctx=Load()),
                                args=[Num(n=0.0)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='asin',
                                               ctx=Load()),
                                args=[Num(n=-0.5)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='asin',
                                               ctx=Load()),
                                args=[Num(n=-1.0)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='asin',
                                               ctx=Load()),
                                args=[Num(n=-1)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\nmath.atan(x)')],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='atan',
                                               ctx=Load()),
                                args=[Num(n=1.0)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='atan',
                                               ctx=Load()),
                                args=[Num(n=0.5)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='atan',
                                               ctx=Load()),
                                args=[Num(n=0.0)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='atan',
                                               ctx=Load()),
                                args=[Num(n=-0.5)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='atan',
                                               ctx=Load()),
                                args=[Num(n=-1.0)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='atan',
                                               ctx=Load()),
                                args=[Num(n=-1)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\nmath.atan2(y,x)')],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='atan2',
                                               ctx=Load()),
                                args=[Num(n=1),
                                      Num(n=1)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='atan2',
                                               ctx=Load()),
                                args=[Num(n=1),
                                      Num(n=-1)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='atan2',
                                               ctx=Load()),
                                args=[Num(n=-1),
                                      Num(n=1)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='atan2',
                                               ctx=Load()),
                                args=[Num(n=-1),
                                      Num(n=-1)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='atan2',
                                               ctx=Load()),
                                args=[Num(n=-5.1),
                                      Num(n=6.3)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\nmath.cos(x)')],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='cos',
                                               ctx=Load()),
                                args=[Num(n=0.0)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='isCloseTo',
                                          ctx=Load()),
                                args=[Num(n=0),
                                      Call(func=Attribute(value=Name(id='math',
                                                                     ctx=Load()),
                                                          attr='cos',
                                                          ctx=Load()),
                                           args=[BinOp(left=Attribute(value=Name(id='math',
                                                                                 ctx=Load()),
                                                                      attr='pi',
                                                                      ctx=Load()),
                                                       op=Div(),
                                                       right=Num(n=2.0))],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None),
                                      Num(n=15)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='cos',
                                               ctx=Load()),
                                args=[Attribute(value=Name(id='math',
                                                           ctx=Load()),
                                                attr='pi',
                                                ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='cos',
                                               ctx=Load()),
                                args=[Num(n=1)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\nmath.sin(x)')],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='sin',
                                               ctx=Load()),
                                args=[Num(n=0.0)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='sin',
                                               ctx=Load()),
                                args=[BinOp(left=Attribute(value=Name(id='math',
                                                                      ctx=Load()),
                                                           attr='pi',
                                                           ctx=Load()),
                                            op=Div(),
                                            right=Num(n=2.0))],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='isCloseTo',
                                          ctx=Load()),
                                args=[Num(n=0),
                                      Call(func=Attribute(value=Name(id='math',
                                                                     ctx=Load()),
                                                          attr='sin',
                                                          ctx=Load()),
                                           args=[Attribute(value=Name(id='math',
                                                                      ctx=Load()),
                                                           attr='pi',
                                                           ctx=Load())],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None),
                                      Num(n=15)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='sin',
                                               ctx=Load()),
                                args=[Num(n=1)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\nmath.tan(x)')],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='tan',
                                               ctx=Load()),
                                args=[Num(n=0.0)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='isCloseTo',
                                          ctx=Load()),
                                args=[Num(n=0),
                                      Call(func=Attribute(value=Name(id='math',
                                                                     ctx=Load()),
                                                          attr='tan',
                                                          ctx=Load()),
                                           args=[Attribute(value=Name(id='math',
                                                                      ctx=Load()),
                                                           attr='pi',
                                                           ctx=Load())],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None),
                                      Num(n=15)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='tan',
                                               ctx=Load()),
                                args=[Num(n=1)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\nmath.degrees(x)')],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='degrees',
                                               ctx=Load()),
                                args=[Num(n=0.0)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='degrees',
                                               ctx=Load()),
                                args=[Num(n=0.261799387799)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='degrees',
                                               ctx=Load()),
                                args=[Num(n=0.785398163397)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='degrees',
                                               ctx=Load()),
                                args=[Num(n=1.57079632679)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='degrees',
                                               ctx=Load()),
                                args=[Num(n=3.14159265359)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='degrees',
                                               ctx=Load()),
                                args=[Num(n=4.71238898038)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='degrees',
                                               ctx=Load()),
                                args=[Num(n=6.28318530718)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='degrees',
                                               ctx=Load()),
                                args=[Num(n=0)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\nmath.radians(x)')],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='radians',
                                               ctx=Load()),
                                args=[Num(n=0)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='radians',
                                               ctx=Load()),
                                args=[Num(n=15)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='radians',
                                               ctx=Load()),
                                args=[Num(n=45)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='radians',
                                               ctx=Load()),
                                args=[Num(n=90)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='radians',
                                               ctx=Load()),
                                args=[Num(n=180)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='radians',
                                               ctx=Load()),
                                args=[Num(n=270)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='radians',
                                               ctx=Load()),
                                args=[Num(n=360)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True)])
