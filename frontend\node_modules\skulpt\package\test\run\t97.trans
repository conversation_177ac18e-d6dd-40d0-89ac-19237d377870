Module(body=[Assign(targets=[Name(id='x',
                                  ctx=Store())],
                    value=List(elts=[],
                               ctx=Load())),
             Expr(value=Call(func=Attribute(value=Name(id='x',
                                                       ctx=Load()),
                                            attr='append',
                                            ctx=Load()),
                             args=[Name(id='x',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Subscript(value=Dict(keys=[Name(id='x',
                                                           ctx=Load())],
                                                values=[Str(s='OK')]),
                                     slice=Index(value=Name(id='x',
                                                            ctx=Load())),
                                     ctx=Load())],
                   nl=True)])
