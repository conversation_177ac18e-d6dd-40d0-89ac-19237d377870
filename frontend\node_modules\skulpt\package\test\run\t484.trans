Module(body=[ClassDef(name='calculator',
                      bases=[],
                      body=[FunctionDef(name='__init__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Pass()],
                                        decorator_list=[]),
                            FunctionDef(name='div',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param()),
                                                             Name(id='x',
                                                                  ctx=Param()),
                                                             Name(id='y',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[TryExcept(body=[Return(value=BinOp(left=Name(id='x',
                                                                                           ctx=Load()),
                                                                                 op=Div(),
                                                                                 right=Name(id='y',
                                                                                            ctx=Load())))],
                                                        handlers=[ExceptHandler(type=Name(id='ZeroDivisionError',
                                                                                          ctx=Load()),
                                                                                name=None,
                                                                                body=[Return(value=Str(s="ZeroDivisionError: can't divide by zero"))]),
                                                                  Except<PERSON><PERSON><PERSON>(type=Name(id='NameError',
                                                                                          ctx=Load()),
                                                                                name=Name(id='e',
                                                                                          ctx=Store()),
                                                                                body=[Return(value=Name(id='e',
                                                                                                        ctx=Load()))]),
                                                                  ExceptHandler(type=Name(id='TypeError',
                                                                                          ctx=Load()),
                                                                                name=Name(id='e',
                                                                                          ctx=Store()),
                                                                                body=[Return(value=Name(id='e',
                                                                                                        ctx=Load()))]),
                                                                  ExceptHandler(type=Name(id='TypeError',
                                                                                          ctx=Load()),
                                                                                name=None,
                                                                                body=[Print(dest=None,
                                                                                            values=[Str(s="DID NOT CATCH 'TypeError as e'")],
                                                                                            nl=True),
                                                                                      Return(value=Str(s='TypeError'))]),
                                                                  ExceptHandler(type=None,
                                                                                name=None,
                                                                                body=[Return(value=Str(s='OTHER ERROR'))])],
                                                        orelse=[])],
                                        decorator_list=[])],
                      decorator_list=[]),
             Assign(targets=[Name(id='c',
                                  ctx=Store())],
                    value=Call(func=Name(id='calculator',
                                         ctx=Load()),
                               args=[],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='c',
                                                          ctx=Load()),
                                               attr='div',
                                               ctx=Load()),
                                args=[Num(n=10),
                                      Num(n=1)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='c',
                                                          ctx=Load()),
                                               attr='div',
                                               ctx=Load()),
                                args=[Num(n=10),
                                      Num(n=0)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='c',
                                                          ctx=Load()),
                                               attr='div',
                                               ctx=Load()),
                                args=[Str(s='12'),
                                      Str(s='6')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             TryExcept(body=[Print(dest=None,
                                   values=[BinOp(left=Call(func=Attribute(value=Name(id='c',
                                                                                     ctx=Load()),
                                                                          attr='div',
                                                                          ctx=Load()),
                                                           args=[Str(s='10'),
                                                                 Str(s='1')],
                                                           keywords=[],
                                                           starargs=None,
                                                           kwargs=None),
                                                 op=Div(),
                                                 right=Num(n=2))],
                                   nl=True)],
                       handlers=[ExceptHandler(type=None,
                                               name=None,
                                               body=[Print(dest=None,
                                                           values=[Str(s='ERROR')],
                                                           nl=True)])],
                       orelse=[]),
             TryExcept(body=[Print(dest=None,
                                   values=[Call(func=Attribute(value=Name(id='c',
                                                                          ctx=Load()),
                                                               attr='div',
                                                               ctx=Load()),
                                                args=[Name(id='x',
                                                           ctx=Load()),
                                                      Num(n=12)],
                                                keywords=[],
                                                starargs=None,
                                                kwargs=None)],
                                   nl=True)],
                       handlers=[ExceptHandler(type=Name(id='NameError',
                                                         ctx=Load()),
                                               name=Name(id='e',
                                                         ctx=Store()),
                                               body=[Print(dest=None,
                                                           values=[Name(id='e',
                                                                        ctx=Load())],
                                                           nl=True)])],
                       orelse=[])])
