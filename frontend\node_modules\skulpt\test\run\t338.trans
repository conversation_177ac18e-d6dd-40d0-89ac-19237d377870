Module(body=[Assign(targets=[Name(id='l',
                                  ctx=Store())],
                    value=List(elts=[Num(n=9),
                                     Num(n=8),
                                     Num(n=1),
                                     Num(n=2),
                                     Num(n=3),
                                     Num(n=45),
                                     Num(n=5)],
                               ctx=Load())),
             Expr(value=Call(func=Attribute(value=Name(id='l',
                                                       ctx=Load()),
                                            attr='sort',
                                            ctx=Load()),
                             args=[],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Name(id='l',
                                ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='---------------- DEFAULT')],
                   nl=True),
             Expr(value=Call(func=Attribute(value=Name(id='l',
                                                       ctx=Load()),
                                            attr='sort',
                                            ctx=Load()),
                             args=[],
                             keywords=[keyword(arg='reverse',
                                               value=Name(id='True',
                                                          ctx=Load()))],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Name(id='l',
                                ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='---------------- REVERSE')],
                   nl=True)])
