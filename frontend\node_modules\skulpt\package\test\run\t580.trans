Module(body=[Assign(targets=[Name(id='lst',
                                  ctx=Store())],
                    value=List(elts=[Num(n=2),
                                     Num(n=2),
                                     Num(n=2),
                                     Num(n=2)],
                               ctx=Load())),
             Assign(targets=[Name(id='i',
                                  ctx=Store())],
                    value=Num(n=0)),
             While(test=Compare(left=Subscript(value=Name(id='lst',
                                                          ctx=Load()),
                                               slice=Index(value=Name(id='i',
                                                                      ctx=Load())),
                                               ctx=Load()),
                                ops=[NotEq()],
                                comparators=[Num(n=0)]),
                   body=[AugAssign(target=Name(id='i',
                                               ctx=Store()),
                                   op=Add(),
                                   value=Num(n=2))],
                   orelse=[])])
