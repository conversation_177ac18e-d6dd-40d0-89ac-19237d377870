Module(body=[Assign(targets=[Name(id='l',
                                  ctx=Store())],
                    value=List(elts=[Num(n=1),
                                     Num(n=2),
                                     Num(n=3),
                                     Num(n=4),
                                     Num(n=1),
                                     Num(n=1)],
                               ctx=Load())),
             Print(dest=None,
                   values=[Name(id='l',
                                ctx=Load())],
                   nl=True),
             Assign(targets=[Name(id='s',
                                  ctx=Store())],
                    value=Call(func=Name(id='set',
                                         ctx=Load()),
                               args=[Name(id='l',
                                          ctx=Load())],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Name(id='s',
                                ctx=Load())],
                   nl=True),
             Assign(targets=[Name(id='t',
                                  ctx=Store())],
                    value=Call(func=Name(id='set',
                                         ctx=Load()),
                               args=[Name(id='s',
                                          ctx=Load())],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Call(func=Name(id='len',
                                          ctx=Load()),
                                args=[Name(id='t',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Name(id='t',
                                ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='len',
                                          ctx=Load()),
                                args=[Name(id='s',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Name(id='s',
                                ctx=Load())],
                   nl=True),
             Expr(value=Call(func=Attribute(value=Name(id='t',
                                                       ctx=Load()),
                                            attr='add',
                                            ctx=Load()),
                             args=[Num(n=100)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Call(func=Name(id='len',
                                          ctx=Load()),
                                args=[Name(id='t',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Name(id='t',
                                ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='len',
                                          ctx=Load()),
                                args=[Name(id='s',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Name(id='s',
                                ctx=Load())],
                   nl=True),
             Expr(value=Call(func=Attribute(value=Name(id='t',
                                                       ctx=Load()),
                                            attr='discard',
                                            ctx=Load()),
                             args=[Num(n=2)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Call(func=Name(id='len',
                                          ctx=Load()),
                                args=[Name(id='t',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Name(id='t',
                                ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='len',
                                          ctx=Load()),
                                args=[Name(id='s',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Name(id='s',
                                ctx=Load())],
                   nl=True)])
