Module(body=[Assign(targets=[Name(id='ctr',
                                  ctx=Store())],
                    value=Num(n=0)),
             FunctionDef(name='f',
                         args=arguments(args=[],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[]),
                         body=[Global(names=['ctr']),
                               AugAssign(target=Name(id='ctr',
                                                     ctx=Store()),
                                         op=Add(),
                                         value=Num(n=1)),
                               Return(value=Name(id='ctr',
                                                 ctx=Load()))],
                         decorator_list=[]),
             Assign(targets=[Name(id='lst',
                                  ctx=Store())],
                    value=Call(func=Name(id='range',
                                         ctx=Load()),
                               args=[Num(n=4)],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             AugAssign(target=Subscript(value=Name(id='lst',
                                                   ctx=Load()),
                                        slice=Index(value=Call(func=Name(id='f',
                                                                         ctx=Load()),
                                                               args=[],
                                                               keywords=[],
                                                               starargs=None,
                                                               kwargs=None)),
                                        ctx=Store()),
                       op=Add(),
                       value=Num(n=3)),
             Print(dest=None,
                   values=[Name(id='lst',
                                ctx=Load())],
                   nl=True)])
