Module(body=[Assign(targets=[Name(id='a',
                                  ctx=Store())],
                    value=List(elts=[Num(n=1),
                                     Num(n=2),
                                     Num(n=3)],
                               ctx=Load())),
             AugAssign(target=Subscript(value=Name(id='a',
                                                   ctx=Load()),
                                        slice=Index(value=Num(n=1)),
                                        ctx=Store()),
                       op=Add(),
                       value=Num(n=4)),
             Print(dest=None,
                   values=[Name(id='a',
                                ctx=Load())],
                   nl=True)])
