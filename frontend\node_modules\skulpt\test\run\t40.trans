Module(body=[Assign(targets=[Name(id='a',
                                  ctx=Store())],
                    value=Num(n=4)),
             FunctionDef(name='test',
                         args=arguments(args=[Name(id='z',
                                                   ctx=Param())],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[]),
                         body=[For(target=Name(id='i',
                                               ctx=Store()),
                                   iter=Call(func=Name(id='range',
                                                       ctx=Load()),
                                             args=[Num(n=0),
                                                   Name(id='a',
                                                        ctx=Load())],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   body=[AugAssign(target=Name(id='z',
                                                               ctx=Store()),
                                                   op=Add(),
                                                   value=Name(id='i',
                                                              ctx=Load()))],
                                   orelse=[]),
                               Return(value=Name(id='z',
                                                 ctx=Load()))],
                         decorator_list=[]),
             Print(dest=None,
                   values=[Call(func=Name(id='test',
                                          ctx=Load()),
                                args=[Num(n=1)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True)])
