Module(body=[Import(names=[alias(name='re',
                                 asname=None)]),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='split',
                                               ctx=Load()),
                                args=[Str(s='\\W+'),
                                      Str(s='Words, words, words.')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='split',
                                               ctx=Load()),
                                args=[Str(s='(\\W+)'),
                                      Str(s='Words, words, words.')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='split',
                                               ctx=Load()),
                                args=[Str(s='\\W+'),
                                      Str(s='Words, words, words.'),
                                      Num(n=1)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='split',
                                               ctx=Load()),
                                args=[Str(s='[a-f]+'),
                                      Str(s='0a3B9'),
                                      Num(n=0),
                                      Attribute(value=Name(id='re',
                                                           ctx=Load()),
                                                attr='IGNORECASE',
                                                ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='split',
                                               ctx=Load()),
                                args=[Str(s='(\\W+)'),
                                      Str(s='...words, words...')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='split',
                                               ctx=Load()),
                                args=[Str(s='x*'),
                                      Str(s='foo')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='\\w+'),
                                      Str(s='Words, words, words.')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='(abc)(def)'),
                                      Str(s='abcdef')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='(abc)(def)'),
                                      Str(s='abcdefabcdefjaabcdef3sabc')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='(abc)'),
                                      Str(s='abcdef')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='(abc)|(def)'),
                                      Str(s='abcdefabcdefjaabcdef3sabc')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True)])
