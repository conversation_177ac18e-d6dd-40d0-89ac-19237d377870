Module(body=[Import(names=[alias(name='string',
                                 asname=None)]),
             Print(dest=None,
                   values=[Attribute(value=Name(id='string',
                                                ctx=Load()),
                                     attr='ascii_lowercase',
                                     ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Attribute(value=Name(id='string',
                                                ctx=Load()),
                                     attr='ascii_uppercase',
                                     ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Attribute(value=Name(id='string',
                                                ctx=Load()),
                                     attr='ascii_letters',
                                     ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Attribute(value=Name(id='string',
                                                ctx=Load()),
                                     attr='lowercase',
                                     ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Attribute(value=Name(id='string',
                                                ctx=Load()),
                                     attr='uppercase',
                                     ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Attribute(value=Name(id='string',
                                                ctx=Load()),
                                     attr='letters',
                                     ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Attribute(value=Name(id='string',
                                                ctx=Load()),
                                     attr='digits',
                                     ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Attribute(value=Name(id='string',
                                                ctx=Load()),
                                     attr='hexdigits',
                                     ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Attribute(value=Name(id='string',
                                                ctx=Load()),
                                     attr='octdigits',
                                     ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Attribute(value=Name(id='string',
                                                ctx=Load()),
                                     attr='punctuation',
                                     ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Attribute(value=Name(id='string',
                                                ctx=Load()),
                                     attr='whitespace',
                                     ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Attribute(value=Name(id='string',
                                                ctx=Load()),
                                     attr='printable',
                                     ctx=Load())],
                   nl=True),
             Assign(targets=[Name(id='s',
                                  ctx=Store())],
                    value=Str(s="I frequently eat pizza; however, I don't particularly like it")),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='string',
                                                          ctx=Load()),
                                               attr='split',
                                               ctx=Load()),
                                args=[Name(id='s',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='string',
                                                          ctx=Load()),
                                               attr='split',
                                               ctx=Load()),
                                args=[Name(id='s',
                                           ctx=Load()),
                                      Str(s=';')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Assign(targets=[Name(id='s',
                                  ctx=Store())],
                    value=Str(s='capitalize')),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='string',
                                                          ctx=Load()),
                                               attr='capitalize',
                                               ctx=Load()),
                                args=[Name(id='s',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Assign(targets=[Name(id='s',
                                  ctx=Store())],
                    value=Str(s='Capitalize')),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='string',
                                                          ctx=Load()),
                                               attr='capitalize',
                                               ctx=Load()),
                                args=[Name(id='s',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Assign(targets=[Name(id='l',
                                  ctx=Store())],
                    value=List(elts=[Str(s='this'),
                                     Str(s='will'),
                                     Str(s='become'),
                                     Str(s='a'),
                                     Str(s='sentence')],
                               ctx=Load())),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='string',
                                                          ctx=Load()),
                                               attr='join',
                                               ctx=Load()),
                                args=[Name(id='l',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='string',
                                                          ctx=Load()),
                                               attr='join',
                                               ctx=Load()),
                                args=[Name(id='l',
                                           ctx=Load()),
                                      Str(s='_')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Assign(targets=[Name(id='s',
                                  ctx=Store())],
                    value=Str(s="i frequently eat pizza; however, i don't particularly like it")),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='string',
                                                          ctx=Load()),
                                               attr='capwords',
                                               ctx=Load()),
                                args=[Name(id='s',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='string',
                                                          ctx=Load()),
                                               attr='capwords',
                                               ctx=Load()),
                                args=[Name(id='s',
                                           ctx=Load()),
                                      Str(s='; ')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True)])
