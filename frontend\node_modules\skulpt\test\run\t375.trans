Module(body=[Assign(targets=[Name(id='l',
                                  ctx=Store())],
                    value=List(elts=[],
                               ctx=Load())),
             Assign(targets=[Name(id='a',
                                  ctx=Store())],
                    value=Call(func=Attribute(value=Name(id='l',
                                                         ctx=Load()),
                                              attr='pop',
                                              ctx=Load()),
                               args=[],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Name(id='a',
                                ctx=Load())],
                   nl=True)])
