Module(body=[Assign(targets=[Name(id='s',
                                  ctx=Store())],
                    value=SetComp(elt=BinOp(left=Name(id='i',
                                                      ctx=Load()),
                                            op=Mult(),
                                            right=Name(id='i',
                                                       ctx=Load())),
                                  generators=[comprehension(target=Name(id='i',
                                                                        ctx=Store()),
                                                            iter=Call(func=Name(id='range',
                                                                                ctx=Load()),
                                                                      args=[Num(n=100)],
                                                                      keywords=[],
                                                                      starargs=None,
                                                                      kwargs=None),
                                                            ifs=[Compare(left=BinOp(left=Name(id='i',
                                                                                              ctx=Load()),
                                                                                    op=BitAnd(),
                                                                                    right=Num(n=1)),
                                                                         ops=[Eq()],
                                                                         comparators=[Num(n=1)])])])),
             Print(dest=None,
                   values=[Name(id='s',
                                ctx=Load())],
                   nl=True),
             Assign(targets=[Name(id='s2',
                                  ctx=Store())],
                    value=SetComp(elt=BinOp(left=BinOp(left=BinOp(left=Num(n=2),
                                                                  op=Mult(),
                                                                  right=Name(id='y',
                                                                             ctx=Load())),
                                                       op=Add(),
                                                       right=Name(id='x',
                                                                  ctx=Load())),
                                            op=Add(),
                                            right=Num(n=1)),
                                  generators=[comprehension(target=Name(id='x',
                                                                        ctx=Store()),
                                                            iter=Tuple(elts=[Num(n=0)],
                                                                       ctx=Load()),
                                                            ifs=[]),
                                              comprehension(target=Name(id='y',
                                                                        ctx=Store()),
                                                            iter=Tuple(elts=[Num(n=1)],
                                                                       ctx=Load()),
                                                            ifs=[])])),
             Print(dest=None,
                   values=[Name(id='s2',
                                ctx=Load())],
                   nl=True)])
