Module(body=[ClassDef(name='F',
                      bases=[],
                      body=[FunctionDef(name='__init__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Assign(targets=[Attribute(value=Name(id='self',
                                                                                   ctx=Load()),
                                                                        attr='a',
                                                                        ctx=Store())],
                                                     value=Num(n=1)),
                                              Assign(targets=[Attribute(value=Name(id='self',
                                                                                   ctx=Load()),
                                                                        attr='b',
                                                                        ctx=Store())],
                                                     value=Num(n=2)),
                                              Assign(targets=[Attribute(value=Name(id='self',
                                                                                   ctx=Load()),
                                                                        attr='d',
                                                                        ctx=Store())],
                                                     value=Num(n=4))],
                                        decorator_list=[])],
                      decorator_list=[]),
             Assign(targets=[Name(id='f',
                                  ctx=Store())],
                    value=Call(func=Name(id='F',
                                         ctx=Load()),
                               args=[],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Call(func=Name(id='getattr',
                                          ctx=Load()),
                                args=[Name(id='f',
                                           ctx=Load()),
                                      Str(s='a')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='getattr',
                                          ctx=Load()),
                                args=[Name(id='f',
                                           ctx=Load()),
                                      Str(s='b')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='getattr',
                                          ctx=Load()),
                                args=[Name(id='f',
                                           ctx=Load()),
                                      Str(s='c'),
                                      Num(n=3)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='getattr',
                                          ctx=Load()),
                                args=[Name(id='f',
                                           ctx=Load()),
                                      Str(s='d')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True)])
