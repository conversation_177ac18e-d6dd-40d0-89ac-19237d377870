Module(body=[FunctionDef(name='helper',
                         args=arguments(args=[Name(id='got',
                                                   ctx=Param()),
                                              Name(id='expect',
                                                   ctx=Param())],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[]),
                         body=[If(test=Compare(left=Name(id='got',
                                                         ctx=Load()),
                                               ops=[Eq()],
                                               comparators=[Name(id='expect',
                                                                 ctx=Load())]),
                                  body=[Print(dest=None,
                                              values=[Name(id='True',
                                                           ctx=Load())],
                                              nl=True)],
                                  orelse=[Print(dest=None,
                                                values=[Name(id='False',
                                                             ctx=Load()),
                                                        Name(id='expect',
                                                             ctx=Load()),
                                                        Name(id='got',
                                                             ctx=Load())],
                                                nl=True)])],
                         decorator_list=[]),
             Print(dest=None,
                   values=[Str(s='\nstr.ljust')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='12345'),
                                                       attr='ljust',
                                                       ctx=Load()),
                                        args=[Num(n=8)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='12345   ')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='12345'),
                                                       attr='ljust',
                                                       ctx=Load()),
                                        args=[Num(n=8),
                                              Str(s='.')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='12345...')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Str(s='\nstr.center')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='12345'),
                                                       attr='center',
                                                       ctx=Load()),
                                        args=[Num(n=7)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s=' 12345 ')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='12345'),
                                                       attr='center',
                                                       ctx=Load()),
                                        args=[Num(n=8),
                                              Str(s='.')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='.12345..')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Str(s='\nstr.rjust')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='12345'),
                                                       attr='rjust',
                                                       ctx=Load()),
                                        args=[Num(n=8)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='   12345')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='12345'),
                                                       attr='rjust',
                                                       ctx=Load()),
                                        args=[Num(n=8),
                                              Str(s='.')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='...12345')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             FunctionDef(name='helper',
                         args=arguments(args=[Name(id='str',
                                                   ctx=Param()),
                                              Name(id='fillchar',
                                                   ctx=Param())],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[]),
                         body=[Print(dest=None,
                                     values=[],
                                     nl=True),
                               Print(dest=None,
                                     values=[Call(func=Attribute(value=Name(id='str',
                                                                            ctx=Load()),
                                                                 attr='ljust',
                                                                 ctx=Load()),
                                                  args=[Num(n=10),
                                                        Name(id='fillchar',
                                                             ctx=Load())],
                                                  keywords=[],
                                                  starargs=None,
                                                  kwargs=None)],
                                     nl=True),
                               Print(dest=None,
                                     values=[Call(func=Attribute(value=Name(id='str',
                                                                            ctx=Load()),
                                                                 attr='center',
                                                                 ctx=Load()),
                                                  args=[Num(n=10),
                                                        Name(id='fillchar',
                                                             ctx=Load())],
                                                  keywords=[],
                                                  starargs=None,
                                                  kwargs=None)],
                                     nl=True),
                               Print(dest=None,
                                     values=[Call(func=Attribute(value=Name(id='str',
                                                                            ctx=Load()),
                                                                 attr='rjust',
                                                                 ctx=Load()),
                                                  args=[Num(n=10),
                                                        Name(id='fillchar',
                                                             ctx=Load())],
                                                  keywords=[],
                                                  starargs=None,
                                                  kwargs=None)],
                                     nl=True)],
                         decorator_list=[]),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Str(s='a'),
                                   Str(s='-')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Str(s='?'),
                                   Str(s='!')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Str(s='-'),
                                   Str(s='.')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Str(s='hello'),
                                   Str(s='~')],
                             keywords=[],
                             starargs=None,
                             kwargs=None))])
