Module(body=[FunctionDef(name='isPrototypeOf',
                         args=arguments(args=[Name(id='x',
                                                   ctx=Param())],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[]),
                         body=[Print(dest=None,
                                     values=[Name(id='x',
                                                  ctx=Load())],
                                     nl=True)],
                         decorator_list=[]),
             FunctionDef(name='toSource',
                         args=arguments(args=[Name(id='x',
                                                   ctx=Param())],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[]),
                         body=[Print(dest=None,
                                     values=[Name(id='x',
                                                  ctx=Load())],
                                     nl=True)],
                         decorator_list=[]),
             FunctionDef(name='hasOwnProperty',
                         args=arguments(args=[Name(id='x',
                                                   ctx=Param())],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[]),
                         body=[Print(dest=None,
                                     values=[Name(id='x',
                                                  ctx=Load())],
                                     nl=True)],
                         decorator_list=[]),
             Expr(value=Call(func=Name(id='isPrototypeOf',
                                       ctx=Load()),
                             args=[Num(n=1)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='toSource',
                                       ctx=Load()),
                             args=[Num(n=2)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='hasOwnProperty',
                                       ctx=Load()),
                             args=[Num(n=3)],
                             keywords=[],
                             starargs=None,
                             kwargs=None))])
