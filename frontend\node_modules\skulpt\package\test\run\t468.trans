Module(body=[ClassDef(name='Matrix',
                      bases=[Name(id='object',
                                  ctx=Load())],
                      body=[Expr(value=Str(s='\n    Represents a matrix\n    ')),
                            FunctionDef(name='__init__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param()),
                                                             Name(id='matrix',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[Name(id='None',
                                                                      ctx=Load())]),
                                        body=[Expr(value=Str(s='\n        ')),
                                              Assign(targets=[Attribute(value=Name(id='self',
                                                                                   ctx=Load()),
                                                                        attr='mat',
                                                                        ctx=Store())],
                                                     value=Name(id='matrix',
                                                                ctx=Load()))],
                                        decorator_list=[]),
                            FunctionDef(name='__getitem__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param()),
                                                             Name(id='index',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Expr(value=Str(s='\n        ')),
                                              Return(value=Subscript(value=Subscript(value=Attribute(value=Name(id='self',
                                                                                                                ctx=Load()),
                                                                                                     attr='mat',
                                                                                                     ctx=Load()),
                                                                                     slice=Index(value=Subscript(value=Name(id='index',
                                                                                                                            ctx=Load()),
                                                                                                                 slice=Index(value=Num(n=0)),
                                                                                                                 ctx=Load())),
                                                                                     ctx=Load()),
                                                                     slice=Index(value=Subscript(value=Name(id='index',
                                                                                                            ctx=Load()),
                                                                                                 slice=Index(value=Num(n=1)),
                                                                                                 ctx=Load())),
                                                                     ctx=Load()))],
                                        decorator_list=[]),
                            FunctionDef(name='__setitem__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param()),
                                                             Name(id='index',
                                                                  ctx=Param()),
                                                             Name(id='item',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Expr(value=Str(s='\n        ')),
                                              Assign(targets=[Subscript(value=Subscript(value=Attribute(value=Name(id='self',
                                                                                                                   ctx=Load()),
                                                                                                        attr='mat',
                                                                                                        ctx=Load()),
                                                                                        slice=Index(value=Subscript(value=Name(id='index',
                                                                                                                               ctx=Load()),
                                                                                                                    slice=Index(value=Num(n=0)),
                                                                                                                    ctx=Load())),
                                                                                        ctx=Load()),
                                                                        slice=Index(value=Subscript(value=Name(id='index',
                                                                                                               ctx=Load()),
                                                                                                    slice=Index(value=Num(n=1)),
                                                                                                    ctx=Load())),
                                                                        ctx=Store())],
                                                     value=Name(id='item',
                                                                ctx=Load()))],
                                        decorator_list=[])],
                      decorator_list=[]),
             Assign(targets=[Name(id='trial',
                                  ctx=Store())],
                    value=Call(func=Name(id='Matrix',
                                         ctx=Load()),
                               args=[List(elts=[List(elts=[Num(n=543)],
                                                     ctx=Load())],
                                          ctx=Load())],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Assign(targets=[Subscript(value=Name(id='trial',
                                                  ctx=Load()),
                                       slice=Index(value=Tuple(elts=[Num(n=0),
                                                                     Num(n=0)],
                                                               ctx=Load())),
                                       ctx=Store())],
                    value=Num(n=100)),
             Print(dest=None,
                   values=[Subscript(value=Name(id='trial',
                                                ctx=Load()),
                                     slice=Index(value=Tuple(elts=[Num(n=0),
                                                                   Num(n=0)],
                                                             ctx=Load())),
                                     ctx=Load())],
                   nl=True)])
