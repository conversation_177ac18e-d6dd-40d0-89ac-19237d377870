Module(body=[Import(names=[alias(name='random',
                                 asname=None)]),
             Print(dest=None,
                   values=[Str(s='\nrandom.seed([x])')],
                   nl=True),
             Expr(value=Call(func=Attribute(value=Name(id='random',
                                                       ctx=Load()),
                                            attr='seed',
                                            ctx=Load()),
                             args=[Num(n=1234)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='random',
                                               ctx=Load()),
                                args=[],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Expr(value=Call(func=Attribute(value=Name(id='random',
                                                       ctx=Load()),
                                            attr='seed',
                                            ctx=Load()),
                             args=[Num(n=5678)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='random',
                                               ctx=Load()),
                                args=[],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Expr(value=Call(func=Attribute(value=Name(id='random',
                                                       ctx=Load()),
                                            attr='seed',
                                            ctx=Load()),
                             args=[Str(s='hello')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='random',
                                               ctx=Load()),
                                args=[],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Expr(value=Call(func=Attribute(value=Name(id='random',
                                                       ctx=Load()),
                                            attr='seed',
                                            ctx=Load()),
                             args=[Tuple(elts=[Num(n=1),
                                               Num(n=2),
                                               Num(n=3),
                                               Num(n=4)],
                                         ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='random',
                                               ctx=Load()),
                                args=[],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Expr(value=Call(func=Attribute(value=Name(id='random',
                                                       ctx=Load()),
                                            attr='seed',
                                            ctx=Load()),
                             args=[Str(s='world')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='random',
                                               ctx=Load()),
                                args=[],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Expr(value=Call(func=Attribute(value=Name(id='random',
                                                       ctx=Load()),
                                            attr='seed',
                                            ctx=Load()),
                             args=[Tuple(elts=[Num(n=5),
                                               Num(n=6),
                                               Num(n=7),
                                               Num(n=8)],
                                         ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='random',
                                               ctx=Load()),
                                args=[],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Expr(value=Call(func=Attribute(value=Name(id='random',
                                                       ctx=Load()),
                                            attr='seed',
                                            ctx=Load()),
                             args=[Str(s='')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='random',
                                               ctx=Load()),
                                args=[],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Expr(value=Call(func=Attribute(value=Name(id='random',
                                                       ctx=Load()),
                                            attr='seed',
                                            ctx=Load()),
                             args=[Tuple(elts=[],
                                         ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='random',
                                               ctx=Load()),
                                args=[],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\nrandom.randrange([start],stop[,step])')],
                   nl=True),
             Expr(value=Call(func=Attribute(value=Name(id='random',
                                                       ctx=Load()),
                                            attr='seed',
                                            ctx=Load()),
                             args=[Num(n=1)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='randrange',
                                               ctx=Load()),
                                args=[Num(n=100)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='randrange',
                                               ctx=Load()),
                                args=[Num(n=90),
                                      Num(n=100)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='randrange',
                                               ctx=Load()),
                                args=[Num(n=10),
                                      Num(n=101),
                                      Num(n=10)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\nrandom.randint(a,b)')],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='randint',
                                               ctx=Load()),
                                args=[Num(n=1),
                                      Num(n=100)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='randint',
                                               ctx=Load()),
                                args=[Num(n=-10),
                                      Num(n=0)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='randint',
                                               ctx=Load()),
                                args=[Num(n=0),
                                      Num(n=0x7fffffff)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\nrandom.choice(seq)')],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='choice',
                                               ctx=Load()),
                                args=[List(elts=[Num(n=1),
                                                 Num(n=2),
                                                 Num(n=3),
                                                 Num(n=4),
                                                 Num(n=5)],
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='choice',
                                               ctx=Load()),
                                args=[Str(s='hello world')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='choice',
                                               ctx=Load()),
                                args=[Tuple(elts=[Num(n=1),
                                                  Num(n=2),
                                                  Num(n=3),
                                                  Num(n=4),
                                                  Num(n=5)],
                                            ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\nrandom.shuffle(x[,random])')],
                   nl=True),
             Assign(targets=[Name(id='l',
                                  ctx=Store())],
                    value=List(elts=[Num(n=1),
                                     Num(n=2),
                                     Num(n=3),
                                     Num(n=4),
                                     Num(n=5)],
                               ctx=Load())),
             Expr(value=Call(func=Attribute(value=Name(id='random',
                                                       ctx=Load()),
                                            attr='shuffle',
                                            ctx=Load()),
                             args=[Name(id='l',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Name(id='l',
                                ctx=Load())],
                   nl=True),
             Expr(value=Call(func=Attribute(value=Name(id='random',
                                                       ctx=Load()),
                                            attr='shuffle',
                                            ctx=Load()),
                             args=[Name(id='l',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Name(id='l',
                                ctx=Load())],
                   nl=True),
             Expr(value=Call(func=Attribute(value=Name(id='random',
                                                       ctx=Load()),
                                            attr='shuffle',
                                            ctx=Load()),
                             args=[Name(id='l',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Name(id='l',
                                ctx=Load())],
                   nl=True),
             Expr(value=Call(func=Attribute(value=Name(id='random',
                                                       ctx=Load()),
                                            attr='shuffle',
                                            ctx=Load()),
                             args=[Name(id='l',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Name(id='l',
                                ctx=Load())],
                   nl=True),
             Expr(value=Call(func=Attribute(value=Name(id='random',
                                                       ctx=Load()),
                                            attr='shuffle',
                                            ctx=Load()),
                             args=[Name(id='l',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Name(id='l',
                                ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\nrandom.random()')],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='random',
                                               ctx=Load()),
                                args=[],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='random',
                                               ctx=Load()),
                                args=[],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='random',
                                               ctx=Load()),
                                args=[],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='random',
                                               ctx=Load()),
                                args=[],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='random',
                                               ctx=Load()),
                                args=[],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True)])
