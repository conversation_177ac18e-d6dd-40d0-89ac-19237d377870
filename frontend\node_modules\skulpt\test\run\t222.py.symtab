Sym_type: module
Sym_name: top
Sym_lineno: 0
Sym_nested: False
Sym_haschildren: True
-- Identifiers --
name: foo
  is_referenced: True
  is_imported: False
  is_parameter: False
  is_global: False
  is_declared_global: False
  is_local: True
  is_free: False
  is_assigned: True
  is_namespace: True
  namespaces: [
    Sym_type: function
    Sym_name: foo
    Sym_lineno: 1
    Sym_nested: False
    Sym_haschildren: False
    Func_params: ['x']
    Func_locals: ['x']
    Func_globals: ['len']
    Func_frees: []
    -- Identifiers --
    name: len
      is_referenced: True
      is_imported: False
      is_parameter: False
      is_global: True
      is_declared_global: False
      is_local: False
      is_free: False
      is_assigned: False
      is_namespace: False
      namespaces: [
      ]
    name: x
      is_referenced: True
      is_imported: False
      is_parameter: True
      is_global: False
      is_declared_global: False
      is_local: True
      is_free: False
      is_assigned: False
      is_namespace: False
      namespaces: [
      ]
  ]
name: g
  is_referenced: True
  is_imported: False
  is_parameter: False
  is_global: False
  is_declared_global: False
  is_local: True
  is_free: False
  is_assigned: True
  is_namespace: False
  namespaces: [
  ]
name: len
  is_referenced: False
  is_imported: False
  is_parameter: False
  is_global: False
  is_declared_global: False
  is_local: True
  is_free: False
  is_assigned: True
  is_namespace: False
  namespaces: [
  ]
name: range
  is_referenced: True
  is_imported: False
  is_parameter: False
  is_global: True
  is_declared_global: False
  is_local: False
  is_free: False
  is_assigned: False
  is_namespace: False
  namespaces: [
  ]
