from datetime import datetime
from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash, check_password_hash
import json

db = SQLAlchemy()

class User(db.Model):
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    points = db.Column(db.Integer, default=0)
    level = db.Column(db.Integer, default=1)
    current_streak = db.Column(db.Integer, default=0)
    lessons_completed = db.Column(db.Integer, default=0)
    is_guest = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    progress = db.relationship('Progress', backref='user', lazy=True, cascade='all, delete-orphan')
    badges = db.relationship('UserBadge', backref='user', lazy=True, cascade='all, delete-orphan')
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)
    
    def to_dict(self):
        return {
            'id': str(self.id),
            'username': self.username,
            'email': self.email,
            'points': self.points,
            'level': self.level,
            'current_streak': self.current_streak,
            'lessons_completed': self.lessons_completed,
            'is_guest': self.is_guest,
            'badges': [badge.badge.name for badge in self.badges if badge.earned],
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

class Lesson(db.Model):
    __tablename__ = 'lessons'
    
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text, nullable=False)
    content = db.Column(db.Text, nullable=False)
    order = db.Column(db.Integer, nullable=False, unique=True)
    difficulty = db.Column(db.String(20), default='beginner')
    estimated_time = db.Column(db.Integer, default=10)  # in minutes
    prerequisites = db.Column(db.Text)  # JSON string of lesson IDs
    exercises = db.Column(db.Text)  # JSON string of exercises
    is_published = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    progress = db.relationship('Progress', backref='lesson', lazy=True)
    
    def get_prerequisites(self):
        if self.prerequisites:
            return json.loads(self.prerequisites)
        return []
    
    def set_prerequisites(self, prereq_list):
        self.prerequisites = json.dumps(prereq_list)
    
    def get_exercises(self):
        if self.exercises:
            return json.loads(self.exercises)
        return []
    
    def set_exercises(self, exercises_list):
        self.exercises = json.dumps(exercises_list)
    
    def to_dict(self, user_id=None):
        # Check if user has completed prerequisites
        is_unlocked = True
        if user_id and self.get_prerequisites():
            completed_prereqs = Progress.query.filter_by(
                user_id=user_id,
                completed=True
            ).filter(Progress.lesson_id.in_(self.get_prerequisites())).count()
            is_unlocked = completed_prereqs == len(self.get_prerequisites())
        elif self.order == 1:
            is_unlocked = True
        else:
            is_unlocked = user_id is None  # For guest users, show all as unlocked
        
        # Check if user has completed this lesson
        is_completed = False
        if user_id:
            progress = Progress.query.filter_by(
                user_id=user_id,
                lesson_id=self.id,
                completed=True
            ).first()
            is_completed = progress is not None
        
        return {
            'id': str(self.id),
            'title': self.title,
            'description': self.description,
            'content': self.content,
            'order': self.order,
            'difficulty': self.difficulty,
            'estimated_time': self.estimated_time,
            'prerequisites': self.get_prerequisites(),
            'exercises': self.get_exercises(),
            'is_unlocked': is_unlocked,
            'is_completed': is_completed
        }

class Progress(db.Model):
    __tablename__ = 'progress'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    lesson_id = db.Column(db.Integer, db.ForeignKey('lessons.id'), nullable=False)
    completed = db.Column(db.Boolean, default=False)
    score = db.Column(db.Integer, default=0)
    time_spent = db.Column(db.Integer, default=0)  # in minutes
    completed_at = db.Column(db.DateTime)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Unique constraint to prevent duplicate progress entries
    __table_args__ = (db.UniqueConstraint('user_id', 'lesson_id', name='unique_user_lesson'),)
    
    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'lesson_id': str(self.lesson_id),
            'completed': self.completed,
            'score': self.score,
            'time_spent': self.time_spent,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

class Badge(db.Model):
    __tablename__ = 'badges'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), unique=True, nullable=False)
    description = db.Column(db.Text, nullable=False)
    icon = db.Column(db.String(10), nullable=False)
    criteria = db.Column(db.Text)  # JSON string describing criteria
    points_required = db.Column(db.Integer, default=0)
    lessons_required = db.Column(db.Integer, default=0)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relationships
    user_badges = db.relationship('UserBadge', backref='badge', lazy=True)
    
    def get_criteria(self):
        if self.criteria:
            return json.loads(self.criteria)
        return {}
    
    def set_criteria(self, criteria_dict):
        self.criteria = json.dumps(criteria_dict)
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'icon': self.icon,
            'criteria': self.get_criteria(),
            'points_required': self.points_required,
            'lessons_required': self.lessons_required
        }

class UserBadge(db.Model):
    __tablename__ = 'user_badges'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    badge_id = db.Column(db.Integer, db.ForeignKey('badges.id'), nullable=False)
    earned = db.Column(db.Boolean, default=False)
    earned_at = db.Column(db.DateTime)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Unique constraint to prevent duplicate badge entries
    __table_args__ = (db.UniqueConstraint('user_id', 'badge_id', name='unique_user_badge'),)
    
    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'badge_id': self.badge_id,
            'badge_name': self.badge.name,
            'badge_icon': self.badge.icon,
            'earned': self.earned,
            'earned_at': self.earned_at.isoformat() if self.earned_at else None
        }
