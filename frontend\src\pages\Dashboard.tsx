import React, { useEffect } from 'react';
import { <PERSON> } from 'react-router-dom';
import { useAuthStore } from '../store/authStore';
import { useLessonStore } from '../store/lessonStore';
import { 
  BookOpen, 
  Trophy, 
  Star, 
  Clock, 
  Target,
  Play,
  Lock,
  CheckCircle,
  TrendingUp,
  Award
} from 'lucide-react';

const Dashboard: React.FC = () => {
  const { user } = useAuthStore();
  const { lessons, progress, fetchLessons } = useLessonStore();

  useEffect(() => {
    fetchLessons();
  }, [fetchLessons]);

  const completedLessons = progress.filter(p => p.completed).length;
  const totalLessons = lessons.length;
  const progressPercentage = totalLessons > 0 ? (completedLessons / totalLessons) * 100 : 0;
  
  const nextLesson = lessons.find(lesson => lesson.isUnlocked && !lesson.isCompleted);
  const recentLessons = lessons.filter(lesson => lesson.isCompleted).slice(-3);

  const badges = [
    { name: 'First Steps', icon: '👶', description: 'Completed your first lesson', earned: completedLessons >= 1 },
    { name: 'Getting Started', icon: '🚀', description: 'Completed 3 lessons', earned: completedLessons >= 3 },
    { name: 'Python Explorer', icon: '🔍', description: 'Completed 5 lessons', earned: completedLessons >= 5 },
    { name: 'Code Warrior', icon: '⚔️', description: 'Completed 10 lessons', earned: completedLessons >= 10 },
    { name: 'Python Master', icon: '🏆', description: 'Completed all lessons', earned: completedLessons >= totalLessons },
  ];

  const earnedBadges = badges.filter(badge => badge.earned);
  const nextBadge = badges.find(badge => !badge.earned);

  return (
    <div className="min-h-screen py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Welcome Header */}
        <div className="mb-8">
          <h1 className="text-kid-3xl font-bold text-gray-900 mb-2">
            Welcome back, {user?.username}! 🎉
          </h1>
          <p className="text-kid-lg text-gray-600">
            Ready to continue your Python adventure?
          </p>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="card text-center">
            <div className="w-12 h-12 bg-gradient-to-br from-primary-400 to-primary-600 rounded-kid-lg mx-auto mb-3 flex items-center justify-center">
              <Star className="text-white" size={24} />
            </div>
            <div className="text-2xl font-bold text-gray-900 mb-1">{user?.points || 0}</div>
            <div className="text-gray-600">Total Points</div>
          </div>

          <div className="card text-center">
            <div className="w-12 h-12 bg-gradient-to-br from-secondary-400 to-secondary-600 rounded-kid-lg mx-auto mb-3 flex items-center justify-center">
              <BookOpen className="text-white" size={24} />
            </div>
            <div className="text-2xl font-bold text-gray-900 mb-1">{completedLessons}</div>
            <div className="text-gray-600">Lessons Completed</div>
          </div>

          <div className="card text-center">
            <div className="w-12 h-12 bg-gradient-to-br from-success-400 to-success-600 rounded-kid-lg mx-auto mb-3 flex items-center justify-center">
              <Trophy className="text-white" size={24} />
            </div>
            <div className="text-2xl font-bold text-gray-900 mb-1">{earnedBadges.length}</div>
            <div className="text-gray-600">Badges Earned</div>
          </div>

          <div className="card text-center">
            <div className="w-12 h-12 bg-gradient-to-br from-warning-400 to-warning-600 rounded-kid-lg mx-auto mb-3 flex items-center justify-center">
              <TrendingUp className="text-white" size={24} />
            </div>
            <div className="text-2xl font-bold text-gray-900 mb-1">{user?.currentStreak || 0}</div>
            <div className="text-gray-600">Day Streak</div>
          </div>
        </div>

        {/* Progress Section */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
          {/* Overall Progress */}
          <div className="lg:col-span-2 card">
            <h2 className="text-kid-xl font-bold text-gray-900 mb-4 flex items-center">
              <Target className="mr-2 text-primary-500" size={24} />
              Your Progress
            </h2>
            
            <div className="mb-4">
              <div className="flex justify-between items-center mb-2">
                <span className="text-gray-700 font-medium">Overall Completion</span>
                <span className="text-gray-700 font-bold">{Math.round(progressPercentage)}%</span>
              </div>
              <div className="progress-bar">
                <div 
                  className="progress-fill" 
                  style={{ width: `${progressPercentage}%` }}
                />
              </div>
            </div>

            <div className="text-gray-600 mb-6">
              {completedLessons} of {totalLessons} lessons completed
            </div>

            {nextLesson && (
              <div className="bg-gradient-to-r from-primary-50 to-secondary-50 p-4 rounded-kid-lg border border-primary-200">
                <h3 className="font-bold text-gray-900 mb-2">Continue Learning</h3>
                <p className="text-gray-700 mb-3">{nextLesson.title}</p>
                <Link 
                  to={`/lessons/${nextLesson.id}`}
                  className="btn-primary inline-flex items-center"
                >
                  <Play className="mr-2" size={16} />
                  Start Lesson
                </Link>
              </div>
            )}
          </div>

          {/* Next Badge */}
          <div className="card">
            <h2 className="text-kid-xl font-bold text-gray-900 mb-4 flex items-center">
              <Award className="mr-2 text-warning-500" size={24} />
              Next Badge
            </h2>
            
            {nextBadge ? (
              <div className="text-center">
                <div className="text-4xl mb-3">{nextBadge.icon}</div>
                <h3 className="font-bold text-gray-900 mb-2">{nextBadge.name}</h3>
                <p className="text-gray-600 text-kid-sm mb-4">{nextBadge.description}</p>
                <div className="bg-gray-200 rounded-full h-2 mb-2">
                  <div 
                    className="bg-gradient-to-r from-warning-400 to-warning-500 h-2 rounded-full transition-all duration-500"
                    style={{ 
                      width: `${Math.min(100, (completedLessons / (nextBadge.name === 'First Steps' ? 1 : 
                                                                   nextBadge.name === 'Getting Started' ? 3 :
                                                                   nextBadge.name === 'Python Explorer' ? 5 :
                                                                   nextBadge.name === 'Code Warrior' ? 10 : totalLessons)) * 100)}%` 
                    }}
                  />
                </div>
                <p className="text-kid-xs text-gray-500">
                  {completedLessons} / {nextBadge.name === 'First Steps' ? 1 : 
                                       nextBadge.name === 'Getting Started' ? 3 :
                                       nextBadge.name === 'Python Explorer' ? 5 :
                                       nextBadge.name === 'Code Warrior' ? 10 : totalLessons} lessons
                </p>
              </div>
            ) : (
              <div className="text-center">
                <div className="text-4xl mb-3">🎉</div>
                <h3 className="font-bold text-gray-900 mb-2">All Badges Earned!</h3>
                <p className="text-gray-600 text-kid-sm">Congratulations on completing all challenges!</p>
              </div>
            )}
          </div>
        </div>

        {/* Recent Activity & Badges */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Recent Lessons */}
          <div className="card">
            <h2 className="text-kid-xl font-bold text-gray-900 mb-4 flex items-center">
              <Clock className="mr-2 text-secondary-500" size={24} />
              Recent Activity
            </h2>
            
            {recentLessons.length > 0 ? (
              <div className="space-y-3">
                {recentLessons.map((lesson) => (
                  <div key={lesson.id} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-kid">
                    <CheckCircle className="text-success-500" size={20} />
                    <div className="flex-1">
                      <h3 className="font-medium text-gray-900">{lesson.title}</h3>
                      <p className="text-kid-sm text-gray-600">Completed</p>
                    </div>
                    <Link 
                      to={`/lessons/${lesson.id}`}
                      className="text-primary-600 hover:text-primary-700 text-kid-sm font-medium"
                    >
                      Review
                    </Link>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <BookOpen size={48} className="mx-auto mb-3 opacity-50" />
                <p>No lessons completed yet</p>
                <p className="text-kid-sm">Start your first lesson to see activity here!</p>
              </div>
            )}
          </div>

          {/* Earned Badges */}
          <div className="card">
            <h2 className="text-kid-xl font-bold text-gray-900 mb-4 flex items-center">
              <Trophy className="mr-2 text-warning-500" size={24} />
              Your Badges
            </h2>
            
            <div className="grid grid-cols-3 gap-4">
              {badges.map((badge, index) => (
                <div 
                  key={index}
                  className={`text-center p-3 rounded-kid-lg border-2 transition-all duration-200 ${
                    badge.earned 
                      ? 'border-warning-200 bg-warning-50' 
                      : 'border-gray-200 bg-gray-50 opacity-50'
                  }`}
                >
                  <div className="text-2xl mb-2">{badge.icon}</div>
                  <h3 className="font-medium text-gray-900 text-kid-xs mb-1">{badge.name}</h3>
                  {badge.earned ? (
                    <div className="text-success-600 text-kid-xs">Earned!</div>
                  ) : (
                    <Lock className="mx-auto text-gray-400" size={12} />
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="mt-8 text-center">
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link to="/lessons" className="btn-primary text-kid-lg px-8 py-4">
              <BookOpen className="mr-2" size={20} />
              Browse All Lessons
            </Link>
            <Link to="/profile" className="btn-secondary text-kid-lg px-8 py-4">
              <User className="mr-2" size={20} />
              View Profile
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
