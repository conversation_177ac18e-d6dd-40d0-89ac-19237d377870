Module(body=[Assign(targets=[Name(id='l',
                                  ctx=Store())],
                    value=List(elts=[Num(n=1),
                                     Num(n=2),
                                     Num(n=3),
                                     Num(n=4)],
                               ctx=Load())),
             Assign(targets=[Name(id='t',
                                  ctx=Store())],
                    value=Tuple(elts=[Num(n=1),
                                      Num(n=2),
                                      Num(n=3),
                                      Num(n=4)],
                                ctx=Load())),
             Assign(targets=[Name(id='d',
                                  ctx=Store())],
                    value=Dict(keys=[Num(n=1),
                                     Num(n=3)],
                               values=[Num(n=2),
                                       Num(n=4)])),
             Print(dest=None,
                   values=[Str(s='\nlists')],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='sum',
                                          ctx=Load()),
                                args=[List(elts=[],
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='sum',
                                          ctx=Load()),
                                args=[List(elts=[],
                                           ctx=Load()),
                                      Num(n=5)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='sum',
                                          ctx=Load()),
                                args=[List(elts=[Num(n=1),
                                                 Num(n=2),
                                                 Num(n=3),
                                                 Num(n=4)],
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='sum',
                                          ctx=Load()),
                                args=[Name(id='l',
                                           ctx=Load()),
                                      Num(n=5)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\ntuples')],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='sum',
                                          ctx=Load()),
                                args=[Tuple(elts=[],
                                            ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='sum',
                                          ctx=Load()),
                                args=[Tuple(elts=[],
                                            ctx=Load()),
                                      Num(n=5)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='sum',
                                          ctx=Load()),
                                args=[Tuple(elts=[Num(n=1),
                                                  Num(n=2),
                                                  Num(n=3),
                                                  Num(n=4)],
                                            ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='sum',
                                          ctx=Load()),
                                args=[Name(id='t',
                                           ctx=Load()),
                                      Num(n=5)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\ndictionaries')],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='sum',
                                          ctx=Load()),
                                args=[Dict(keys=[],
                                           values=[])],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='sum',
                                          ctx=Load()),
                                args=[Dict(keys=[],
                                           values=[]),
                                      Num(n=5)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='sum',
                                          ctx=Load()),
                                args=[Dict(keys=[Num(n=1),
                                                 Num(n=3)],
                                           values=[Num(n=2),
                                                   Num(n=4)])],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='sum',
                                          ctx=Load()),
                                args=[Call(func=Attribute(value=Name(id='d',
                                                                     ctx=Load()),
                                                          attr='keys',
                                                          ctx=Load()),
                                           args=[],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='sum',
                                          ctx=Load()),
                                args=[Call(func=Attribute(value=Name(id='d',
                                                                     ctx=Load()),
                                                          attr='values',
                                                          ctx=Load()),
                                           args=[],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='sum',
                                          ctx=Load()),
                                args=[Name(id='d',
                                           ctx=Load()),
                                      Num(n=5)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True)])
