Module(body=[Assign(targets=[Name(id='q',
                                  ctx=Store())],
                    value=Num(n=5.0)),
             Assign(targets=[Name(id='w',
                                  ctx=Store())],
                    value=UnaryOp(op=USub(),
                                  operand=Name(id='q',
                                               ctx=Load()))),
             Assign(targets=[Name(id='x',
                                  ctx=Store())],
                    value=Name(id='True',
                               ctx=Load())),
             Assign(targets=[Name(id='y',
                                  ctx=Store())],
                    value=UnaryOp(op=USub(),
                                  operand=Name(id='x',
                                               ctx=Load()))),
             Print(dest=None,
                   values=[Name(id='q',
                                ctx=Load()),
                           Call(func=Name(id='type',
                                          ctx=Load()),
                                args=[Name(id='q',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Name(id='w',
                                ctx=Load()),
                           Call(func=Name(id='type',
                                          ctx=Load()),
                                args=[Name(id='w',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Name(id='x',
                                ctx=Load()),
                           Call(func=Name(id='type',
                                          ctx=Load()),
                                args=[Name(id='x',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Name(id='y',
                                ctx=Load()),
                           Call(func=Name(id='type',
                                          ctx=Load()),
                                args=[Name(id='y',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True)])
