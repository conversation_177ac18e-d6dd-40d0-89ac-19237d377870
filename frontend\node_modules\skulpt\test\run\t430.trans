Module(body=[Print(dest=None,
                   values=[Str(s='\nINTEGERS')],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=BinOp(left=Num(n=5),
                                              op=BitOr(),
                                              right=Num(n=10)),
                                   ops=[Eq()],
                                   comparators=[Num(n=15)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=BinOp(left=Num(n=6),
                                              op=BitXor(),
                                              right=Num(n=5)),
                                   ops=[Eq()],
                                   comparators=[Num(n=3)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=BinOp(left=Num(n=15),
                                              op=BitAnd(),
                                              right=Num(n=1)),
                                   ops=[Eq()],
                                   comparators=[Num(n=1)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=BinOp(left=Num(n=6),
                                              op=LShift(),
                                              right=Num(n=2)),
                                   ops=[Eq()],
                                   comparators=[Num(n=24)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=BinOp(left=Num(n=6),
                                              op=RShift(),
                                              right=Num(n=2)),
                                   ops=[Eq()],
                                   comparators=[Num(n=1)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=UnaryOp(op=Invert(),
                                                operand=Num(n=3)),
                                   ops=[Eq()],
                                   comparators=[Num(n=-4)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=BinOp(left=Num(n=325),
                                              op=BitOr(),
                                              right=Num(n=512)),
                                   ops=[Eq()],
                                   comparators=[Num(n=837)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=BinOp(left=Num(n=640),
                                              op=BitXor(),
                                              right=Num(n=540)),
                                   ops=[Eq()],
                                   comparators=[Num(n=156)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=BinOp(left=Num(n=4064),
                                              op=BitAnd(),
                                              right=Num(n=3840)),
                                   ops=[Eq()],
                                   comparators=[Num(n=3840)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=BinOp(left=Num(n=1523),
                                              op=LShift(),
                                              right=Num(n=2)),
                                   ops=[Eq()],
                                   comparators=[Num(n=6092)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=BinOp(left=Num(n=1523),
                                              op=RShift(),
                                              right=Num(n=2)),
                                   ops=[Eq()],
                                   comparators=[Num(n=380)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=UnaryOp(op=Invert(),
                                                operand=Num(n=668)),
                                   ops=[Eq()],
                                   comparators=[Num(n=-669)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=BinOp(left=Num(n=4080),
                                              op=BitOr(),
                                              right=Num(n=0)),
                                   ops=[Eq()],
                                   comparators=[Num(n=4080)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=BinOp(left=Num(n=4336),
                                              op=BitXor(),
                                              right=Num(n=496)),
                                   ops=[Eq()],
                                   comparators=[Num(n=4352)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=BinOp(left=Num(n=4080),
                                              op=BitAnd(),
                                              right=Num(n=61455)),
                                   ops=[Eq()],
                                   comparators=[Num(n=0)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=BinOp(left=Num(n=23041),
                                              op=LShift(),
                                              right=Num(n=2)),
                                   ops=[Eq()],
                                   comparators=[Num(n=92164)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=BinOp(left=Num(n=23041),
                                              op=RShift(),
                                              right=Num(n=2)),
                                   ops=[Eq()],
                                   comparators=[Num(n=5760)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=UnaryOp(op=Invert(),
                                                operand=Num(n=18992)),
                                   ops=[Eq()],
                                   comparators=[Num(n=-18993)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=BinOp(left=Num(n=124),
                                              op=BitOr(),
                                              right=Num(n=37)),
                                   ops=[Eq()],
                                   comparators=[Num(n=125)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=BinOp(left=Num(n=3847),
                                              op=BitXor(),
                                              right=Num(n=4958)),
                                   ops=[Eq()],
                                   comparators=[Num(n=7257)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=BinOp(left=Num(n=745),
                                              op=BitAnd(),
                                              right=Num(n=348)),
                                   ops=[Eq()],
                                   comparators=[Num(n=72)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=BinOp(left=Num(n=1834),
                                              op=LShift(),
                                              right=Num(n=2)),
                                   ops=[Eq()],
                                   comparators=[Num(n=7336)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=BinOp(left=Num(n=1834),
                                              op=RShift(),
                                              right=Num(n=2)),
                                   ops=[Eq()],
                                   comparators=[Num(n=458)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=UnaryOp(op=Invert(),
                                                operand=Num(n=2398)),
                                   ops=[Eq()],
                                   comparators=[Num(n=-2399)])],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\nLONG INTEGERS')],
                   nl=True),
             Expr(value=Str(s="\nprint 0b0101L | 0b1010L == 0b1111L\nprint 0b0110L ^ 0b0101L == 0b0011L\nprint 0b1111L & 0b0001L == 0b0001L\nprint 0b0110L << 2L == 0b11000L\nprint 0b0110L >> 2L == 0b0001L\n#print ~0b0011L == -0b0100L #skulpt doesn't accept the ~ operator with longs\n")),
             Print(dest=None,
                   values=[Compare(left=BinOp(left=Num(n=325),
                                              op=BitOr(),
                                              right=Num(n=512)),
                                   ops=[Eq()],
                                   comparators=[Num(n=837)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=BinOp(left=Num(n=640),
                                              op=BitXor(),
                                              right=Num(n=540)),
                                   ops=[Eq()],
                                   comparators=[Num(n=156)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=BinOp(left=Num(n=4064),
                                              op=BitAnd(),
                                              right=Num(n=3840)),
                                   ops=[Eq()],
                                   comparators=[Num(n=3840)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=BinOp(left=Num(n=1523),
                                              op=LShift(),
                                              right=Num(n=2)),
                                   ops=[Eq()],
                                   comparators=[Num(n=6092)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=BinOp(left=Num(n=1523),
                                              op=RShift(),
                                              right=Num(n=2)),
                                   ops=[Eq()],
                                   comparators=[Num(n=380)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=BinOp(left=Num(n=4080),
                                              op=BitOr(),
                                              right=Num(n=0)),
                                   ops=[Eq()],
                                   comparators=[Num(n=4080)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=BinOp(left=Num(n=4336),
                                              op=BitXor(),
                                              right=Num(n=496)),
                                   ops=[Eq()],
                                   comparators=[Num(n=4352)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=BinOp(left=Num(n=4080),
                                              op=BitAnd(),
                                              right=Num(n=61455)),
                                   ops=[Eq()],
                                   comparators=[Num(n=0)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=BinOp(left=Num(n=23041),
                                              op=LShift(),
                                              right=Num(n=2)),
                                   ops=[Eq()],
                                   comparators=[Num(n=92164)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=BinOp(left=Num(n=23041),
                                              op=RShift(),
                                              right=Num(n=2)),
                                   ops=[Eq()],
                                   comparators=[Num(n=5760)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=BinOp(left=Num(n=124),
                                              op=BitOr(),
                                              right=Num(n=37)),
                                   ops=[Eq()],
                                   comparators=[Num(n=125)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=BinOp(left=Num(n=3847),
                                              op=BitXor(),
                                              right=Num(n=4958)),
                                   ops=[Eq()],
                                   comparators=[Num(n=7257)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=BinOp(left=Num(n=745),
                                              op=BitAnd(),
                                              right=Num(n=348)),
                                   ops=[Eq()],
                                   comparators=[Num(n=72)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=BinOp(left=Num(n=1834),
                                              op=LShift(),
                                              right=Num(n=2)),
                                   ops=[Eq()],
                                   comparators=[Num(n=7336)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=BinOp(left=Num(n=1834),
                                              op=RShift(),
                                              right=Num(n=2)),
                                   ops=[Eq()],
                                   comparators=[Num(n=458)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=BinOp(left=Call(func=Name(id='int',
                                                                  ctx=Load()),
                                                        args=[BinOp(left=Str(s='123456789'),
                                                                    op=Mult(),
                                                                    right=Num(n=10))],
                                                        keywords=[],
                                                        starargs=None,
                                                        kwargs=None),
                                              op=BitAnd(),
                                              right=Call(func=Name(id='int',
                                                                   ctx=Load()),
                                                         args=[BinOp(left=Str(s='987654321'),
                                                                     op=Mult(),
                                                                     right=Num(n=10))],
                                                         keywords=[],
                                                         starargs=None,
                                                         kwargs=None)),
                                   ops=[Eq()],
                                   comparators=[Num(n=95579309557357885362290225874030292317027763371981185445626785720401260273886076820525585)])],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='type',
                                          ctx=Load()),
                                args=[BinOp(left=Call(func=Name(id='int',
                                                                ctx=Load()),
                                                      args=[BinOp(left=Str(s='123456789'),
                                                                  op=Mult(),
                                                                  right=Num(n=10))],
                                                      keywords=[],
                                                      starargs=None,
                                                      kwargs=None),
                                            op=BitAnd(),
                                            right=Call(func=Name(id='int',
                                                                 ctx=Load()),
                                                       args=[BinOp(left=Str(s='987654321'),
                                                                   op=Mult(),
                                                                   right=Num(n=10))],
                                                       keywords=[],
                                                       starargs=None,
                                                       kwargs=None))],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True)])
