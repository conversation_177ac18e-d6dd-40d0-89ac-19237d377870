Module(body=[Assign(targets=[Name(id='seq',
                                  ctx=Store())],
                    value=Call(func=Name(id='range',
                                         ctx=Load()),
                               args=[Num(n=8)],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Assign(targets=[Name(id='newseq',
                                  ctx=Store())],
                    value=Call(func=Name(id='map',
                                         ctx=Load()),
                               args=[Lambda(args=arguments(args=[Name(id='x',
                                                                      ctx=Param())],
                                                           vararg=None,
                                                           kwarg=None,
                                                           defaults=[]),
                                            body=BinOp(left=Name(id='x',
                                                                 ctx=Load()),
                                                       op=Mult(),
                                                       right=Name(id='x',
                                                                  ctx=Load()))),
                                     Name(id='seq',
                                          ctx=Load())],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Name(id='newseq',
                                ctx=Load())],
                   nl=True),
             Assign(targets=[Name(id='rseq',
                                  ctx=Store())],
                    value=Call(func=Name(id='range',
                                         ctx=Load()),
                               args=[Num(n=16)],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Assign(targets=[Name(id='rnewseq',
                                  ctx=Store())],
                    value=Call(func=Name(id='reduce',
                                         ctx=Load()),
                               args=[Lambda(args=arguments(args=[Name(id='x',
                                                                      ctx=Param()),
                                                                 Name(id='y',
                                                                      ctx=Param())],
                                                           vararg=None,
                                                           kwarg=None,
                                                           defaults=[]),
                                            body=BinOp(left=Name(id='x',
                                                                 ctx=Load()),
                                                       op=Add(),
                                                       right=Name(id='y',
                                                                  ctx=Load()))),
                                     Name(id='rseq',
                                          ctx=Load())],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Name(id='rnewseq',
                                ctx=Load())],
                   nl=True),
             Assign(targets=[Name(id='rnewseq2',
                                  ctx=Store())],
                    value=Call(func=Name(id='reduce',
                                         ctx=Load()),
                               args=[Lambda(args=arguments(args=[Name(id='x',
                                                                      ctx=Param()),
                                                                 Name(id='y',
                                                                      ctx=Param())],
                                                           vararg=None,
                                                           kwarg=None,
                                                           defaults=[]),
                                            body=BinOp(left=Name(id='x',
                                                                 ctx=Load()),
                                                       op=Add(),
                                                       right=Name(id='y',
                                                                  ctx=Load()))),
                                     List(elts=[],
                                          ctx=Load()),
                                     Num(n=8)],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Name(id='rnewseq2',
                                ctx=Load())],
                   nl=True),
             Assign(targets=[Name(id='fseq',
                                  ctx=Store())],
                    value=Call(func=Name(id='range',
                                         ctx=Load()),
                               args=[Num(n=16)],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Assign(targets=[Name(id='fnewseq',
                                  ctx=Store())],
                    value=Call(func=Name(id='filter',
                                         ctx=Load()),
                               args=[Lambda(args=arguments(args=[Name(id='x',
                                                                      ctx=Param())],
                                                           vararg=None,
                                                           kwarg=None,
                                                           defaults=[]),
                                            body=Compare(left=BinOp(left=Name(id='x',
                                                                              ctx=Load()),
                                                                    op=Mod(),
                                                                    right=Num(n=2)),
                                                         ops=[Eq()],
                                                         comparators=[Num(n=0)])),
                                     Name(id='fseq',
                                          ctx=Load())],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Name(id='fnewseq',
                                ctx=Load())],
                   nl=True),
             FunctionDef(name='f',
                         args=arguments(args=[Name(id='x',
                                                   ctx=Param())],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[]),
                         body=[Return(value=Call(func=Name(id='ord',
                                                           ctx=Load()),
                                                 args=[Name(id='x',
                                                            ctx=Load())],
                                                 keywords=[],
                                                 starargs=None,
                                                 kwargs=None))],
                         decorator_list=[]),
             Print(dest=None,
                   values=[Call(func=Name(id='map',
                                          ctx=Load()),
                                args=[Name(id='f',
                                           ctx=Load()),
                                      Str(s='abcdef')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Assign(targets=[Name(id='string',
                                  ctx=Store())],
                    value=Call(func=Name(id='filter',
                                         ctx=Load()),
                               args=[Lambda(args=arguments(args=[Name(id='c',
                                                                      ctx=Param())],
                                                           vararg=None,
                                                           kwarg=None,
                                                           defaults=[]),
                                            body=Compare(left=Name(id='c',
                                                                   ctx=Load()),
                                                         ops=[NotEq()],
                                                         comparators=[Str(s='a')])),
                                     Str(s='abc')],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Call(func=Name(id='type',
                                          ctx=Load()),
                                args=[Name(id='string',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Name(id='string',
                                ctx=Load())],
                   nl=True),
             Assign(targets=[Name(id='tup',
                                  ctx=Store())],
                    value=Call(func=Name(id='filter',
                                         ctx=Load()),
                               args=[Lambda(args=arguments(args=[Name(id='t',
                                                                      ctx=Param())],
                                                           vararg=None,
                                                           kwarg=None,
                                                           defaults=[]),
                                            body=Compare(left=BinOp(left=Name(id='t',
                                                                              ctx=Load()),
                                                                    op=Mod(),
                                                                    right=Num(n=2)),
                                                         ops=[Eq()],
                                                         comparators=[Num(n=0)])),
                                     Tuple(elts=[Num(n=1),
                                                 Num(n=2),
                                                 Num(n=3),
                                                 Num(n=4),
                                                 Num(n=5),
                                                 Num(n=6),
                                                 Num(n=7),
                                                 Num(n=8),
                                                 Num(n=9),
                                                 Num(n=10)],
                                           ctx=Load())],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Call(func=Name(id='type',
                                          ctx=Load()),
                                args=[Name(id='tup',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Name(id='tup',
                                ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='filter',
                                          ctx=Load()),
                                args=[Name(id='None',
                                           ctx=Load()),
                                      List(elts=[Num(n=0),
                                                 Num(n=1),
                                                 Str(s=''),
                                                 Str(s='hello'),
                                                 Name(id='False',
                                                      ctx=Load()),
                                                 Name(id='True',
                                                      ctx=Load())],
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Assign(targets=[Name(id='b',
                                  ctx=Store())],
                    value=Call(func=Name(id='range',
                                         ctx=Load()),
                               args=[Num(n=8)],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Assign(targets=[Name(id='c',
                                  ctx=Store())],
                    value=Call(func=Name(id='range',
                                         ctx=Load()),
                               args=[Num(n=10)],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             FunctionDef(name='mapy',
                         args=arguments(args=[Name(id='x',
                                                   ctx=Param()),
                                              Name(id='y',
                                                   ctx=Param())],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[]),
                         body=[If(test=Compare(left=Name(id='x',
                                                         ctx=Load()),
                                               ops=[Eq()],
                                               comparators=[Name(id='None',
                                                                 ctx=Load())]),
                                  body=[Assign(targets=[Name(id='x',
                                                             ctx=Store())],
                                               value=Num(n=0))],
                                  orelse=[]),
                               If(test=Compare(left=Name(id='y',
                                                         ctx=Load()),
                                               ops=[Eq()],
                                               comparators=[Name(id='None',
                                                                 ctx=Load())]),
                                  body=[Assign(targets=[Name(id='y',
                                                             ctx=Store())],
                                               value=Num(n=0))],
                                  orelse=[]),
                               Return(value=BinOp(left=Name(id='x',
                                                            ctx=Load()),
                                                  op=Add(),
                                                  right=Name(id='y',
                                                             ctx=Load())))],
                         decorator_list=[]),
             Print(dest=None,
                   values=[Call(func=Name(id='map',
                                          ctx=Load()),
                                args=[Name(id='mapy',
                                           ctx=Load()),
                                      Name(id='b',
                                           ctx=Load()),
                                      Name(id='c',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='map',
                                          ctx=Load()),
                                args=[Name(id='None',
                                           ctx=Load()),
                                      List(elts=[Num(n=0),
                                                 Num(n=1),
                                                 Dict(keys=[],
                                                      values=[]),
                                                 Str(s=''),
                                                 Str(s='hello'),
                                                 Name(id='False',
                                                      ctx=Load()),
                                                 Name(id='True',
                                                      ctx=Load())],
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True)])
