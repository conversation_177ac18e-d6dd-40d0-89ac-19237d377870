Module(body=[TryExcept(body=[Raise(type=Name(id='TypeError',
                                             ctx=Load()),
                                   inst=Str(s='abc'),
                                   tback=None)],
                       handlers=[ExceptHandler(type=Name(id='TypeError',
                                                         ctx=Load()),
                                               name=Name(id='e',
                                                         ctx=Store()),
                                               body=[Print(dest=None,
                                                           values=[Str(s='caught'),
                                                                   Name(id='e',
                                                                        ctx=Load())],
                                                           nl=True)])],
                       orelse=[]),
             TryExcept(body=[TryExcept(body=[Raise(type=Call(func=Name(id='TypeError',
                                                                       ctx=Load()),
                                                             args=[Str(s='abc')],
                                                             keywords=[],
                                                             starargs=None,
                                                             kwargs=None),
                                                   inst=None,
                                                   tback=None)],
                                       handlers=[ExceptHandler(type=Name(id='TypeError',
                                                                         ctx=Load()),
                                                               name=Name(id='e',
                                                                         ctx=Store()),
                                                               body=[Print(dest=None,
                                                                           values=[Str(s='caught'),
                                                                                   Name(id='e',
                                                                                        ctx=Load())],
                                                                           nl=True),
                                                                     Raise(type=None,
                                                                           inst=None,
                                                                           tback=None)])],
                                       orelse=[])],
                       handlers=[ExceptHandler(type=Name(id='TypeError',
                                                         ctx=Load()),
                                               name=Name(id='e',
                                                         ctx=Store()),
                                               body=[Print(dest=None,
                                                           values=[Str(s='caught re-raise: '),
                                                                   Name(id='e',
                                                                        ctx=Load())],
                                                           nl=True)])],
                       orelse=[]),
             TryExcept(body=[Raise(type=Name(id='TypeError',
                                             ctx=Load()),
                                   inst=None,
                                   tback=None)],
                       handlers=[ExceptHandler(type=Name(id='TypeError',
                                                         ctx=Load()),
                                               name=Name(id='e',
                                                         ctx=Store()),
                                               body=[Print(dest=None,
                                                           values=[Str(s='caught'),
                                                                   Name(id='e',
                                                                        ctx=Load())],
                                                           nl=True)])],
                       orelse=[]),
             TryExcept(body=[Assign(targets=[Name(id='x',
                                                  ctx=Store())],
                                    value=Call(func=Name(id='TypeError',
                                                         ctx=Load()),
                                               args=[Str(s='abc')],
                                               keywords=[],
                                               starargs=None,
                                               kwargs=None)),
                             Raise(type=Name(id='x',
                                             ctx=Load()),
                                   inst=None,
                                   tback=None)],
                       handlers=[ExceptHandler(type=Name(id='TypeError',
                                                         ctx=Load()),
                                               name=Name(id='e',
                                                         ctx=Store()),
                                               body=[Print(dest=None,
                                                           values=[Str(s='caught'),
                                                                   Name(id='e',
                                                                        ctx=Load())],
                                                           nl=True)])],
                       orelse=[])])
