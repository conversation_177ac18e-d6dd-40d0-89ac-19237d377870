Module(body=[Assign(targets=[Name(id='x',
                                  ctx=Store())],
                    value=Num(n=1)),
             AugAssign(target=Name(id='x',
                                   ctx=Store()),
                       op=Add(),
                       value=Num(n=4)),
             Print(dest=None,
                   values=[Compare(left=Name(id='x',
                                             ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Num(n=5)])],
                   nl=True),
             AugAssign(target=Name(id='x',
                                   ctx=Store()),
                       op=Sub(),
                       value=Num(n=2)),
             Print(dest=None,
                   values=[Compare(left=Name(id='x',
                                             ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Num(n=3)])],
                   nl=True),
             AugAssign(target=Name(id='x',
                                   ctx=Store()),
                       op=Mult(),
                       value=Num(n=4)),
             Print(dest=None,
                   values=[Compare(left=Name(id='x',
                                             ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Num(n=12)])],
                   nl=True),
             AugAssign(target=Name(id='x',
                                   ctx=Store()),
                       op=Div(),
                       value=Num(n=6)),
             Print(dest=None,
                   values=[Compare(left=Name(id='x',
                                             ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Num(n=2)])],
                   nl=True),
             AugAssign(target=Name(id='x',
                                   ctx=Store()),
                       op=Mod(),
                       value=Num(n=5)),
             Print(dest=None,
                   values=[Compare(left=Name(id='x',
                                             ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Num(n=2)])],
                   nl=True),
             Assign(targets=[Name(id='x',
                                  ctx=Store())],
                    value=Num(n=1)),
             AugAssign(target=Name(id='x',
                                   ctx=Store()),
                       op=Add(),
                       value=Num(n=4)),
             Print(dest=None,
                   values=[Compare(left=Name(id='x',
                                             ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Num(n=5)])],
                   nl=True),
             AugAssign(target=Name(id='x',
                                   ctx=Store()),
                       op=Sub(),
                       value=Num(n=2)),
             Print(dest=None,
                   values=[Compare(left=Name(id='x',
                                             ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Num(n=3)])],
                   nl=True),
             AugAssign(target=Name(id='x',
                                   ctx=Store()),
                       op=Mult(),
                       value=Num(n=4)),
             Print(dest=None,
                   values=[Compare(left=Name(id='x',
                                             ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Num(n=12)])],
                   nl=True),
             AugAssign(target=Name(id='x',
                                   ctx=Store()),
                       op=Div(),
                       value=Num(n=6)),
             Print(dest=None,
                   values=[Compare(left=Name(id='x',
                                             ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Num(n=2)])],
                   nl=True),
             AugAssign(target=Name(id='x',
                                   ctx=Store()),
                       op=Mod(),
                       value=Num(n=5)),
             Print(dest=None,
                   values=[Compare(left=Name(id='x',
                                             ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Num(n=2)])],
                   nl=True),
             Assign(targets=[Name(id='x',
                                  ctx=Store())],
                    value=Num(n=1.5)),
             AugAssign(target=Name(id='x',
                                   ctx=Store()),
                       op=Add(),
                       value=Num(n=4.2)),
             Print(dest=None,
                   values=[Compare(left=Name(id='x',
                                             ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Num(n=5.7)])],
                   nl=True),
             AugAssign(target=Name(id='x',
                                   ctx=Store()),
                       op=Sub(),
                       value=Num(n=3.7)),
             Print(dest=None,
                   values=[Compare(left=Name(id='x',
                                             ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Num(n=2.0)])],
                   nl=True),
             AugAssign(target=Name(id='x',
                                   ctx=Store()),
                       op=Mult(),
                       value=Num(n=4.25)),
             Print(dest=None,
                   values=[Compare(left=Name(id='x',
                                             ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Num(n=8.5)])],
                   nl=True),
             AugAssign(target=Name(id='x',
                                   ctx=Store()),
                       op=Div(),
                       value=Num(n=4)),
             Print(dest=None,
                   values=[Compare(left=Name(id='x',
                                             ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Num(n=2.125)])],
                   nl=True),
             AugAssign(target=Name(id='x',
                                   ctx=Store()),
                       op=Mod(),
                       value=Num(n=2)),
             Print(dest=None,
                   values=[Compare(left=Name(id='x',
                                             ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Num(n=0.125)])],
                   nl=True)])
