Module(body=[Print(dest=None,
                   values=[Call(func=Name(id='any',
                                          ctx=Load()),
                                args=[List(elts=[Num(n=1),
                                                 Num(n=2),
                                                 Num(n=3)],
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='any',
                                          ctx=Load()),
                                args=[List(elts=[Num(n=1),
                                                 Num(n=2),
                                                 Num(n=0)],
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='any',
                                          ctx=Load()),
                                args=[Str(s='abc')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='any',
                                          ctx=Load()),
                                args=[List(elts=[Name(id='False',
                                                      ctx=Load()),
                                                 Name(id='False',
                                                      ctx=Load()),
                                                 Name(id='False',
                                                      ctx=Load())],
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='any',
                                          ctx=Load()),
                                args=[List(elts=[Name(id='True',
                                                      ctx=Load()),
                                                 Name(id='True',
                                                      ctx=Load()),
                                                 Name(id='True',
                                                      ctx=Load())],
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='any',
                                          ctx=Load()),
                                args=[Call(func=Name(id='range',
                                                     ctx=Load()),
                                           args=[Num(n=10)],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='any',
                                          ctx=Load()),
                                args=[Call(func=Name(id='range',
                                                     ctx=Load()),
                                           args=[Num(n=1),
                                                 Num(n=11)],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='any',
                                          ctx=Load()),
                                args=[Tuple(elts=[Str(s='a'),
                                                  Str(s='b'),
                                                  Str(s='c')],
                                            ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='any',
                                          ctx=Load()),
                                args=[Tuple(elts=[Str(s='a'),
                                                  Str(s=''),
                                                  Str(s='c')],
                                            ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True)])
