Module(body=[FunctionDef(name='helper',
                         args=arguments(args=[Name(id='x',
                                                   ctx=Param()),
                                              Name(id='y',
                                                   ctx=Param()),
                                              Name(id='expect',
                                                   ctx=Param())],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[]),
                         body=[Assign(targets=[Name(id='l',
                                                    ctx=Store())],
                                      value=BinOp(left=List(elts=[Num(n=0)],
                                                            ctx=Load()),
                                                  op=Mult(),
                                                  right=Num(n=6))),
                               If(test=Compare(left=Name(id='expect',
                                                         ctx=Load()),
                                               ops=[Lt()],
                                               comparators=[Num(n=0)]),
                                  body=[Assign(targets=[Subscript(value=Name(id='l',
                                                                             ctx=Load()),
                                                                  slice=Index(value=Num(n=0)),
                                                                  ctx=Store())],
                                               value=Compare(left=Compare(left=Name(id='x',
                                                                                    ctx=Load()),
                                                                          ops=[Lt()],
                                                                          comparators=[Name(id='y',
                                                                                            ctx=Load())]),
                                                             ops=[Eq()],
                                                             comparators=[Name(id='True',
                                                                               ctx=Load())])),
                                        Assign(targets=[Subscript(value=Name(id='l',
                                                                             ctx=Load()),
                                                                  slice=Index(value=Num(n=1)),
                                                                  ctx=Store())],
                                               value=Compare(left=Compare(left=Name(id='x',
                                                                                    ctx=Load()),
                                                                          ops=[LtE()],
                                                                          comparators=[Name(id='y',
                                                                                            ctx=Load())]),
                                                             ops=[Eq()],
                                                             comparators=[Name(id='True',
                                                                               ctx=Load())])),
                                        Assign(targets=[Subscript(value=Name(id='l',
                                                                             ctx=Load()),
                                                                  slice=Index(value=Num(n=2)),
                                                                  ctx=Store())],
                                               value=Compare(left=Compare(left=Name(id='x',
                                                                                    ctx=Load()),
                                                                          ops=[Gt()],
                                                                          comparators=[Name(id='y',
                                                                                            ctx=Load())]),
                                                             ops=[Eq()],
                                                             comparators=[Name(id='False',
                                                                               ctx=Load())])),
                                        Assign(targets=[Subscript(value=Name(id='l',
                                                                             ctx=Load()),
                                                                  slice=Index(value=Num(n=3)),
                                                                  ctx=Store())],
                                               value=Compare(left=Compare(left=Name(id='x',
                                                                                    ctx=Load()),
                                                                          ops=[GtE()],
                                                                          comparators=[Name(id='y',
                                                                                            ctx=Load())]),
                                                             ops=[Eq()],
                                                             comparators=[Name(id='False',
                                                                               ctx=Load())])),
                                        Assign(targets=[Subscript(value=Name(id='l',
                                                                             ctx=Load()),
                                                                  slice=Index(value=Num(n=4)),
                                                                  ctx=Store())],
                                               value=Compare(left=Compare(left=Name(id='x',
                                                                                    ctx=Load()),
                                                                          ops=[Eq()],
                                                                          comparators=[Name(id='y',
                                                                                            ctx=Load())]),
                                                             ops=[Eq()],
                                                             comparators=[Name(id='False',
                                                                               ctx=Load())])),
                                        Assign(targets=[Subscript(value=Name(id='l',
                                                                             ctx=Load()),
                                                                  slice=Index(value=Num(n=5)),
                                                                  ctx=Store())],
                                               value=Compare(left=Compare(left=Name(id='x',
                                                                                    ctx=Load()),
                                                                          ops=[NotEq()],
                                                                          comparators=[Name(id='y',
                                                                                            ctx=Load())]),
                                                             ops=[Eq()],
                                                             comparators=[Name(id='True',
                                                                               ctx=Load())])),
                                        If(test=BoolOp(op=Or(),
                                                       values=[Call(func=Name(id='isinstance',
                                                                              ctx=Load()),
                                                                    args=[Name(id='x',
                                                                               ctx=Load()),
                                                                          Tuple(elts=[Name(id='int',
                                                                                           ctx=Load()),
                                                                                      Name(id='float',
                                                                                           ctx=Load()),
                                                                                      Name(id='long',
                                                                                           ctx=Load()),
                                                                                      Name(id='str',
                                                                                           ctx=Load())],
                                                                                ctx=Load())],
                                                                    keywords=[],
                                                                    starargs=None,
                                                                    kwargs=None),
                                                               Call(func=Name(id='isinstance',
                                                                              ctx=Load()),
                                                                    args=[Name(id='y',
                                                                               ctx=Load()),
                                                                          Tuple(elts=[Name(id='int',
                                                                                           ctx=Load()),
                                                                                      Name(id='float',
                                                                                           ctx=Load()),
                                                                                      Name(id='long',
                                                                                           ctx=Load()),
                                                                                      Name(id='str',
                                                                                           ctx=Load())],
                                                                                ctx=Load())],
                                                                    keywords=[],
                                                                    starargs=None,
                                                                    kwargs=None)]),
                                           body=[Expr(value=Call(func=Attribute(value=Name(id='l',
                                                                                           ctx=Load()),
                                                                                attr='append',
                                                                                ctx=Load()),
                                                                 args=[Compare(left=Compare(left=Name(id='x',
                                                                                                      ctx=Load()),
                                                                                            ops=[Is()],
                                                                                            comparators=[Name(id='y',
                                                                                                              ctx=Load())]),
                                                                               ops=[Eq()],
                                                                               comparators=[Name(id='False',
                                                                                                 ctx=Load())])],
                                                                 keywords=[],
                                                                 starargs=None,
                                                                 kwargs=None)),
                                                 Expr(value=Call(func=Attribute(value=Name(id='l',
                                                                                           ctx=Load()),
                                                                                attr='append',
                                                                                ctx=Load()),
                                                                 args=[Compare(left=Compare(left=Name(id='x',
                                                                                                      ctx=Load()),
                                                                                            ops=[IsNot()],
                                                                                            comparators=[Name(id='y',
                                                                                                              ctx=Load())]),
                                                                               ops=[Eq()],
                                                                               comparators=[Name(id='True',
                                                                                                 ctx=Load())])],
                                                                 keywords=[],
                                                                 starargs=None,
                                                                 kwargs=None))],
                                           orelse=[])],
                                  orelse=[If(test=Compare(left=Name(id='expect',
                                                                    ctx=Load()),
                                                          ops=[Eq()],
                                                          comparators=[Num(n=0)]),
                                             body=[Assign(targets=[Subscript(value=Name(id='l',
                                                                                        ctx=Load()),
                                                                             slice=Index(value=Num(n=0)),
                                                                             ctx=Store())],
                                                          value=Compare(left=Compare(left=Name(id='x',
                                                                                               ctx=Load()),
                                                                                     ops=[Lt()],
                                                                                     comparators=[Name(id='y',
                                                                                                       ctx=Load())]),
                                                                        ops=[Eq()],
                                                                        comparators=[Name(id='False',
                                                                                          ctx=Load())])),
                                                   Assign(targets=[Subscript(value=Name(id='l',
                                                                                        ctx=Load()),
                                                                             slice=Index(value=Num(n=1)),
                                                                             ctx=Store())],
                                                          value=Compare(left=Compare(left=Name(id='x',
                                                                                               ctx=Load()),
                                                                                     ops=[LtE()],
                                                                                     comparators=[Name(id='y',
                                                                                                       ctx=Load())]),
                                                                        ops=[Eq()],
                                                                        comparators=[Name(id='True',
                                                                                          ctx=Load())])),
                                                   Assign(targets=[Subscript(value=Name(id='l',
                                                                                        ctx=Load()),
                                                                             slice=Index(value=Num(n=2)),
                                                                             ctx=Store())],
                                                          value=Compare(left=Compare(left=Name(id='x',
                                                                                               ctx=Load()),
                                                                                     ops=[Gt()],
                                                                                     comparators=[Name(id='y',
                                                                                                       ctx=Load())]),
                                                                        ops=[Eq()],
                                                                        comparators=[Name(id='False',
                                                                                          ctx=Load())])),
                                                   Assign(targets=[Subscript(value=Name(id='l',
                                                                                        ctx=Load()),
                                                                             slice=Index(value=Num(n=3)),
                                                                             ctx=Store())],
                                                          value=Compare(left=Compare(left=Name(id='x',
                                                                                               ctx=Load()),
                                                                                     ops=[GtE()],
                                                                                     comparators=[Name(id='y',
                                                                                                       ctx=Load())]),
                                                                        ops=[Eq()],
                                                                        comparators=[Name(id='True',
                                                                                          ctx=Load())])),
                                                   Assign(targets=[Subscript(value=Name(id='l',
                                                                                        ctx=Load()),
                                                                             slice=Index(value=Num(n=4)),
                                                                             ctx=Store())],
                                                          value=Compare(left=Compare(left=Name(id='x',
                                                                                               ctx=Load()),
                                                                                     ops=[Eq()],
                                                                                     comparators=[Name(id='y',
                                                                                                       ctx=Load())]),
                                                                        ops=[Eq()],
                                                                        comparators=[Name(id='True',
                                                                                          ctx=Load())])),
                                                   Assign(targets=[Subscript(value=Name(id='l',
                                                                                        ctx=Load()),
                                                                             slice=Index(value=Num(n=5)),
                                                                             ctx=Store())],
                                                          value=Compare(left=Compare(left=Name(id='x',
                                                                                               ctx=Load()),
                                                                                     ops=[NotEq()],
                                                                                     comparators=[Name(id='y',
                                                                                                       ctx=Load())]),
                                                                        ops=[Eq()],
                                                                        comparators=[Name(id='False',
                                                                                          ctx=Load())])),
                                                   If(test=BoolOp(op=Or(),
                                                                  values=[Call(func=Name(id='isinstance',
                                                                                         ctx=Load()),
                                                                               args=[Name(id='x',
                                                                                          ctx=Load()),
                                                                                     Tuple(elts=[Name(id='int',
                                                                                                      ctx=Load()),
                                                                                                 Name(id='float',
                                                                                                      ctx=Load()),
                                                                                                 Name(id='long',
                                                                                                      ctx=Load()),
                                                                                                 Name(id='str',
                                                                                                      ctx=Load())],
                                                                                           ctx=Load())],
                                                                               keywords=[],
                                                                               starargs=None,
                                                                               kwargs=None),
                                                                          Call(func=Name(id='isinstance',
                                                                                         ctx=Load()),
                                                                               args=[Name(id='y',
                                                                                          ctx=Load()),
                                                                                     Tuple(elts=[Name(id='int',
                                                                                                      ctx=Load()),
                                                                                                 Name(id='float',
                                                                                                      ctx=Load()),
                                                                                                 Name(id='long',
                                                                                                      ctx=Load()),
                                                                                                 Name(id='str',
                                                                                                      ctx=Load())],
                                                                                           ctx=Load())],
                                                                               keywords=[],
                                                                               starargs=None,
                                                                               kwargs=None)]),
                                                      body=[Expr(value=Call(func=Attribute(value=Name(id='l',
                                                                                                      ctx=Load()),
                                                                                           attr='append',
                                                                                           ctx=Load()),
                                                                            args=[Compare(left=Compare(left=Name(id='x',
                                                                                                                 ctx=Load()),
                                                                                                       ops=[Is()],
                                                                                                       comparators=[Name(id='y',
                                                                                                                         ctx=Load())]),
                                                                                          ops=[Eq()],
                                                                                          comparators=[Name(id='True',
                                                                                                            ctx=Load())])],
                                                                            keywords=[],
                                                                            starargs=None,
                                                                            kwargs=None)),
                                                            Expr(value=Call(func=Attribute(value=Name(id='l',
                                                                                                      ctx=Load()),
                                                                                           attr='append',
                                                                                           ctx=Load()),
                                                                            args=[Compare(left=Compare(left=Name(id='x',
                                                                                                                 ctx=Load()),
                                                                                                       ops=[IsNot()],
                                                                                                       comparators=[Name(id='y',
                                                                                                                         ctx=Load())]),
                                                                                          ops=[Eq()],
                                                                                          comparators=[Name(id='False',
                                                                                                            ctx=Load())])],
                                                                            keywords=[],
                                                                            starargs=None,
                                                                            kwargs=None))],
                                                      orelse=[])],
                                             orelse=[If(test=Compare(left=Name(id='expect',
                                                                               ctx=Load()),
                                                                     ops=[Gt()],
                                                                     comparators=[Num(n=0)]),
                                                        body=[Assign(targets=[Subscript(value=Name(id='l',
                                                                                                   ctx=Load()),
                                                                                        slice=Index(value=Num(n=0)),
                                                                                        ctx=Store())],
                                                                     value=Compare(left=Compare(left=Name(id='x',
                                                                                                          ctx=Load()),
                                                                                                ops=[Lt()],
                                                                                                comparators=[Name(id='y',
                                                                                                                  ctx=Load())]),
                                                                                   ops=[Eq()],
                                                                                   comparators=[Name(id='False',
                                                                                                     ctx=Load())])),
                                                              Assign(targets=[Subscript(value=Name(id='l',
                                                                                                   ctx=Load()),
                                                                                        slice=Index(value=Num(n=1)),
                                                                                        ctx=Store())],
                                                                     value=Compare(left=Compare(left=Name(id='x',
                                                                                                          ctx=Load()),
                                                                                                ops=[LtE()],
                                                                                                comparators=[Name(id='y',
                                                                                                                  ctx=Load())]),
                                                                                   ops=[Eq()],
                                                                                   comparators=[Name(id='False',
                                                                                                     ctx=Load())])),
                                                              Assign(targets=[Subscript(value=Name(id='l',
                                                                                                   ctx=Load()),
                                                                                        slice=Index(value=Num(n=2)),
                                                                                        ctx=Store())],
                                                                     value=Compare(left=Compare(left=Name(id='x',
                                                                                                          ctx=Load()),
                                                                                                ops=[Gt()],
                                                                                                comparators=[Name(id='y',
                                                                                                                  ctx=Load())]),
                                                                                   ops=[Eq()],
                                                                                   comparators=[Name(id='True',
                                                                                                     ctx=Load())])),
                                                              Assign(targets=[Subscript(value=Name(id='l',
                                                                                                   ctx=Load()),
                                                                                        slice=Index(value=Num(n=3)),
                                                                                        ctx=Store())],
                                                                     value=Compare(left=Compare(left=Name(id='x',
                                                                                                          ctx=Load()),
                                                                                                ops=[GtE()],
                                                                                                comparators=[Name(id='y',
                                                                                                                  ctx=Load())]),
                                                                                   ops=[Eq()],
                                                                                   comparators=[Name(id='True',
                                                                                                     ctx=Load())])),
                                                              Assign(targets=[Subscript(value=Name(id='l',
                                                                                                   ctx=Load()),
                                                                                        slice=Index(value=Num(n=4)),
                                                                                        ctx=Store())],
                                                                     value=Compare(left=Compare(left=Name(id='x',
                                                                                                          ctx=Load()),
                                                                                                ops=[Eq()],
                                                                                                comparators=[Name(id='y',
                                                                                                                  ctx=Load())]),
                                                                                   ops=[Eq()],
                                                                                   comparators=[Name(id='False',
                                                                                                     ctx=Load())])),
                                                              Assign(targets=[Subscript(value=Name(id='l',
                                                                                                   ctx=Load()),
                                                                                        slice=Index(value=Num(n=5)),
                                                                                        ctx=Store())],
                                                                     value=Compare(left=Compare(left=Name(id='x',
                                                                                                          ctx=Load()),
                                                                                                ops=[NotEq()],
                                                                                                comparators=[Name(id='y',
                                                                                                                  ctx=Load())]),
                                                                                   ops=[Eq()],
                                                                                   comparators=[Name(id='True',
                                                                                                     ctx=Load())])),
                                                              If(test=BoolOp(op=Or(),
                                                                             values=[Call(func=Name(id='isinstance',
                                                                                                    ctx=Load()),
                                                                                          args=[Name(id='x',
                                                                                                     ctx=Load()),
                                                                                                Tuple(elts=[Name(id='int',
                                                                                                                 ctx=Load()),
                                                                                                            Name(id='float',
                                                                                                                 ctx=Load()),
                                                                                                            Name(id='long',
                                                                                                                 ctx=Load()),
                                                                                                            Name(id='str',
                                                                                                                 ctx=Load())],
                                                                                                      ctx=Load())],
                                                                                          keywords=[],
                                                                                          starargs=None,
                                                                                          kwargs=None),
                                                                                     Call(func=Name(id='isinstance',
                                                                                                    ctx=Load()),
                                                                                          args=[Name(id='y',
                                                                                                     ctx=Load()),
                                                                                                Tuple(elts=[Name(id='int',
                                                                                                                 ctx=Load()),
                                                                                                            Name(id='float',
                                                                                                                 ctx=Load()),
                                                                                                            Name(id='long',
                                                                                                                 ctx=Load()),
                                                                                                            Name(id='str',
                                                                                                                 ctx=Load())],
                                                                                                      ctx=Load())],
                                                                                          keywords=[],
                                                                                          starargs=None,
                                                                                          kwargs=None)]),
                                                                 body=[Expr(value=Call(func=Attribute(value=Name(id='l',
                                                                                                                 ctx=Load()),
                                                                                                      attr='append',
                                                                                                      ctx=Load()),
                                                                                       args=[Compare(left=Compare(left=Name(id='x',
                                                                                                                            ctx=Load()),
                                                                                                                  ops=[Is()],
                                                                                                                  comparators=[Name(id='y',
                                                                                                                                    ctx=Load())]),
                                                                                                     ops=[Eq()],
                                                                                                     comparators=[Name(id='False',
                                                                                                                       ctx=Load())])],
                                                                                       keywords=[],
                                                                                       starargs=None,
                                                                                       kwargs=None)),
                                                                       Expr(value=Call(func=Attribute(value=Name(id='l',
                                                                                                                 ctx=Load()),
                                                                                                      attr='append',
                                                                                                      ctx=Load()),
                                                                                       args=[Compare(left=Compare(left=Name(id='x',
                                                                                                                            ctx=Load()),
                                                                                                                  ops=[IsNot()],
                                                                                                                  comparators=[Name(id='y',
                                                                                                                                    ctx=Load())]),
                                                                                                     ops=[Eq()],
                                                                                                     comparators=[Name(id='True',
                                                                                                                       ctx=Load())])],
                                                                                       keywords=[],
                                                                                       starargs=None,
                                                                                       kwargs=None))],
                                                                 orelse=[])],
                                                        orelse=[])])]),
                               If(test=BoolOp(op=And(),
                                              values=[UnaryOp(op=Not(),
                                                              operand=Call(func=Name(id='isinstance',
                                                                                     ctx=Load()),
                                                                           args=[Name(id='x',
                                                                                      ctx=Load()),
                                                                                 Tuple(elts=[Name(id='int',
                                                                                                  ctx=Load()),
                                                                                             Name(id='float',
                                                                                                  ctx=Load()),
                                                                                             Name(id='long',
                                                                                                  ctx=Load()),
                                                                                             Name(id='str',
                                                                                                  ctx=Load())],
                                                                                       ctx=Load())],
                                                                           keywords=[],
                                                                           starargs=None,
                                                                           kwargs=None)),
                                                      UnaryOp(op=Not(),
                                                              operand=Call(func=Name(id='isinstance',
                                                                                     ctx=Load()),
                                                                           args=[Name(id='y',
                                                                                      ctx=Load()),
                                                                                 Tuple(elts=[Name(id='int',
                                                                                                  ctx=Load()),
                                                                                             Name(id='float',
                                                                                                  ctx=Load()),
                                                                                             Name(id='long',
                                                                                                  ctx=Load()),
                                                                                             Name(id='str',
                                                                                                  ctx=Load())],
                                                                                       ctx=Load())],
                                                                           keywords=[],
                                                                           starargs=None,
                                                                           kwargs=None))]),
                                  body=[Expr(value=Call(func=Attribute(value=Name(id='l',
                                                                                  ctx=Load()),
                                                                       attr='append',
                                                                       ctx=Load()),
                                                        args=[Compare(left=Compare(left=Name(id='x',
                                                                                             ctx=Load()),
                                                                                   ops=[Is()],
                                                                                   comparators=[Name(id='y',
                                                                                                     ctx=Load())]),
                                                                      ops=[Eq()],
                                                                      comparators=[Name(id='False',
                                                                                        ctx=Load())])],
                                                        keywords=[],
                                                        starargs=None,
                                                        kwargs=None)),
                                        Expr(value=Call(func=Attribute(value=Name(id='l',
                                                                                  ctx=Load()),
                                                                       attr='append',
                                                                       ctx=Load()),
                                                        args=[Compare(left=Compare(left=Name(id='x',
                                                                                             ctx=Load()),
                                                                                   ops=[IsNot()],
                                                                                   comparators=[Name(id='y',
                                                                                                     ctx=Load())]),
                                                                      ops=[Eq()],
                                                                      comparators=[Name(id='True',
                                                                                        ctx=Load())])],
                                                        keywords=[],
                                                        starargs=None,
                                                        kwargs=None))],
                                  orelse=[]),
                               If(test=Call(func=Name(id='all',
                                                      ctx=Load()),
                                            args=[Name(id='l',
                                                       ctx=Load())],
                                            keywords=[],
                                            starargs=None,
                                            kwargs=None),
                                  body=[Print(dest=None,
                                              values=[Name(id='True',
                                                           ctx=Load())],
                                              nl=True)],
                                  orelse=[Print(dest=None,
                                                values=[Name(id='False',
                                                             ctx=Load()),
                                                        Name(id='x',
                                                             ctx=Load()),
                                                        Name(id='y',
                                                             ctx=Load()),
                                                        Name(id='l',
                                                             ctx=Load())],
                                                nl=True)])],
                         decorator_list=[]),
             Print(dest=None,
                   values=[Str(s='\nINTEGERS')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Num(n=1),
                                   Num(n=2),
                                   Num(n=-1)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Num(n=1),
                                   Num(n=1),
                                   Num(n=0)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Num(n=2),
                                   Num(n=1),
                                   Num(n=1)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Num(n=-2),
                                   Num(n=-1),
                                   Num(n=-1)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Num(n=-2),
                                   Num(n=-2),
                                   Num(n=0)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Num(n=-1),
                                   Num(n=-2),
                                   Num(n=1)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Num(n=-1),
                                   Num(n=1),
                                   Num(n=-1)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Num(n=1),
                                   Num(n=-1),
                                   Num(n=1)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Str(s='\nLONG INTEGERS')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Num(n=1),
                                   Num(n=2),
                                   Num(n=-1)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Num(n=2),
                                   Num(n=1),
                                   Num(n=1)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Num(n=-1),
                                   Num(n=1),
                                   Num(n=-1)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Num(n=1),
                                   Num(n=-1),
                                   Num(n=1)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Str(s='\nFLOATING POINT')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Num(n=1.0),
                                   Num(n=2.0),
                                   Num(n=-1)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Num(n=1.0),
                                   Num(n=1.0),
                                   Num(n=0)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Num(n=2.0),
                                   Num(n=1.0),
                                   Num(n=1)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Num(n=-2.0),
                                   Num(n=-1.0),
                                   Num(n=-1)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Num(n=-2.0),
                                   Num(n=-2.0),
                                   Num(n=0)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Num(n=-1.0),
                                   Num(n=-2.0),
                                   Num(n=1)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Num(n=-1.0),
                                   Num(n=1.0),
                                   Num(n=-1)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Num(n=1.0),
                                   Num(n=-1.0),
                                   Num(n=1)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Str(s='\nLISTS')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[List(elts=[],
                                        ctx=Load()),
                                   List(elts=[Num(n=1)],
                                        ctx=Load()),
                                   Num(n=-1)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[List(elts=[Num(n=1),
                                              Num(n=2)],
                                        ctx=Load()),
                                   List(elts=[Num(n=1),
                                              Num(n=2)],
                                        ctx=Load()),
                                   Num(n=0)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[List(elts=[Num(n=1),
                                              Num(n=2),
                                              Num(n=3)],
                                        ctx=Load()),
                                   List(elts=[Num(n=1),
                                              Num(n=2)],
                                        ctx=Load()),
                                   Num(n=1)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[List(elts=[Num(n=1),
                                              Num(n=2)],
                                        ctx=Load()),
                                   List(elts=[Num(n=2),
                                              Num(n=1)],
                                        ctx=Load()),
                                   Num(n=-1)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[List(elts=[Num(n=1),
                                              Num(n=2),
                                              Num(n=3)],
                                        ctx=Load()),
                                   List(elts=[Num(n=1),
                                              Num(n=2),
                                              Num(n=1),
                                              Num(n=5)],
                                        ctx=Load()),
                                   Num(n=1)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Str(s='\nTUPLES')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Name(id='tuple',
                                                  ctx=Load()),
                                        args=[],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Tuple(elts=[Num(n=1)],
                                         ctx=Load()),
                                   Num(n=-1)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Tuple(elts=[Num(n=1),
                                               Num(n=2)],
                                         ctx=Load()),
                                   Tuple(elts=[Num(n=1),
                                               Num(n=2)],
                                         ctx=Load()),
                                   Num(n=0)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Tuple(elts=[Num(n=1),
                                               Num(n=2),
                                               Num(n=3)],
                                         ctx=Load()),
                                   Tuple(elts=[Num(n=1),
                                               Num(n=2)],
                                         ctx=Load()),
                                   Num(n=1)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Tuple(elts=[Num(n=1),
                                               Num(n=2)],
                                         ctx=Load()),
                                   Tuple(elts=[Num(n=2),
                                               Num(n=1)],
                                         ctx=Load()),
                                   Num(n=-1)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Tuple(elts=[Num(n=1),
                                               Num(n=2),
                                               Num(n=3)],
                                         ctx=Load()),
                                   Tuple(elts=[Num(n=1),
                                               Num(n=2),
                                               Num(n=1),
                                               Num(n=5)],
                                         ctx=Load()),
                                   Num(n=1)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Str(s='\nSTRINGS')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Str(s=''),
                                   Str(s='a'),
                                   Num(n=-1)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Str(s='a'),
                                   Str(s='a'),
                                   Num(n=0)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Str(s='ab'),
                                   Str(s='a'),
                                   Num(n=1)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Str(s='ABCD'),
                                   Str(s='abcd'),
                                   Num(n=-1)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Str(s='ABCD'),
                                   Str(s='ABCD'),
                                   Num(n=0)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Str(s='aBCD'),
                                   Str(s='Abcd'),
                                   Num(n=1)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             ClassDef(name='A',
                      bases=[],
                      body=[FunctionDef(name='__init__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param()),
                                                             Name(id='x',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Assign(targets=[Attribute(value=Name(id='self',
                                                                                   ctx=Load()),
                                                                        attr='x',
                                                                        ctx=Store())],
                                                     value=Name(id='x',
                                                                ctx=Load()))],
                                        decorator_list=[]),
                            FunctionDef(name='__cmp__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param()),
                                                             Name(id='other',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Return(value=Attribute(value=Name(id='self',
                                                                                ctx=Load()),
                                                                     attr='x',
                                                                     ctx=Load()))],
                                        decorator_list=[])],
                      decorator_list=[]),
             Print(dest=None,
                   values=[Str(s='\nUSER-DEFINED OBJECTS')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Name(id='A',
                                                  ctx=Load()),
                                        args=[Num(n=-1)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Call(func=Name(id='A',
                                                  ctx=Load()),
                                        args=[Num(n=1)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Num(n=-1)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Name(id='A',
                                                  ctx=Load()),
                                        args=[Num(n=0)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Call(func=Name(id='A',
                                                  ctx=Load()),
                                        args=[Num(n=0)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Num(n=0)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Name(id='A',
                                                  ctx=Load()),
                                        args=[Num(n=1)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Call(func=Name(id='A',
                                                  ctx=Load()),
                                        args=[Num(n=-1)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Num(n=1)],
                             keywords=[],
                             starargs=None,
                             kwargs=None))])
