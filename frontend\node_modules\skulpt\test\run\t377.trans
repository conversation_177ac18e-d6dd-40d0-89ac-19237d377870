Module(body=[Assign(targets=[Name(id='a',
                                  ctx=Store())],
                    value=List(elts=[Num(n=1),
                                     Num(n=2),
                                     Num(n=3)],
                               ctx=Load())),
             Expr(value=Call(func=Attribute(value=Name(id='a',
                                                       ctx=Load()),
                                            attr='extend',
                                            ctx=Load()),
                             args=[Num(n=4)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Name(id='a',
                                ctx=Load())],
                   nl=True)])
