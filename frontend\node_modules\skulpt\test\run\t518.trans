Module(body=[Assign(targets=[Name(id='list',
                                  ctx=Store())],
                    value=List(elts=[Num(n=0),
                                     Num(n=1),
                                     Num(n=2),
                                     Num(n=3)],
                               ctx=Load())),
             TryExcept(body=[Print(dest=None,
                                   values=[Subscript(value=Name(id='list',
                                                                ctx=Load()),
                                                     slice=Slice(lower=Num(n=1),
                                                                 upper=None,
                                                                 step=Num(n=0)),
                                                     ctx=Load())],
                                   nl=True)],
                       handlers=[ExceptHandler(type=Name(id='ValueError',
                                                         ctx=Load()),
                                               name=Name(id='e',
                                                         ctx=Store()),
                                               body=[Print(dest=None,
                                                           values=[Name(id='e',
                                                                        ctx=Load())],
                                                           nl=True)])],
                       orelse=[]),
             TryExcept(body=[Print(dest=None,
                                   values=[Subscript(value=Name(id='list',
                                                                ctx=Load()),
                                                     slice=Slice(lower=Num(n=1),
                                                                 upper=Num(n=3),
                                                                 step=Num(n=0)),
                                                     ctx=Load())],
                                   nl=True)],
                       handlers=[ExceptHandler(type=Name(id='ValueError',
                                                         ctx=Load()),
                                               name=Name(id='e',
                                                         ctx=Store()),
                                               body=[Print(dest=None,
                                                           values=[Name(id='e',
                                                                        ctx=Load())],
                                                           nl=True)])],
                       orelse=[])])
