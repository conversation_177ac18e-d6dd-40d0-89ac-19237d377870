Module(body=[Print(dest=None,
                   values=[BinOp(left=Num(n=-3),
                                 op=Mod(),
                                 right=Num(n=2))],
                   nl=True),
             Print(dest=None,
                   values=[BinOp(left=Num(n=3),
                                 op=Mod(),
                                 right=Num(n=2))],
                   nl=True),
             Print(dest=None,
                   values=[BinOp(left=Num(n=-3),
                                 op=Mod(),
                                 right=Num(n=3))],
                   nl=True),
             Print(dest=None,
                   values=[BinOp(left=Num(n=3),
                                 op=Mod(),
                                 right=Num(n=3))],
                   nl=True),
             Print(dest=None,
                   values=[],
                   nl=True),
             Print(dest=None,
                   values=[BinOp(left=Num(n=-3),
                                 op=Mod(),
                                 right=Num(n=-2))],
                   nl=True),
             Print(dest=None,
                   values=[BinOp(left=Num(n=3),
                                 op=Mod(),
                                 right=Num(n=-2))],
                   nl=True),
             Print(dest=None,
                   values=[BinOp(left=Num(n=-3),
                                 op=Mod(),
                                 right=Num(n=-3))],
                   nl=True),
             Print(dest=None,
                   values=[BinOp(left=Num(n=3),
                                 op=Mod(),
                                 right=Num(n=-3))],
                   nl=True),
             Print(dest=None,
                   values=[],
                   nl=True),
             Print(dest=None,
                   values=[BinOp(left=Num(n=0),
                                 op=Mod(),
                                 right=Num(n=1))],
                   nl=True),
             Print(dest=None,
                   values=[BinOp(left=Num(n=0),
                                 op=Mod(),
                                 right=Num(n=-1))],
                   nl=True)])
