Module(body=[ClassDef(name='HasLen',
                      bases=[],
                      body=[FunctionDef(name='__init__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param()),
                                                             Name(id='l',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Assign(targets=[Attribute(value=Name(id='self',
                                                                                   ctx=Load()),
                                                                        attr='l',
                                                                        ctx=Store())],
                                                     value=Name(id='l',
                                                                ctx=Load()))],
                                        decorator_list=[]),
                            FunctionDef(name='__len__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Return(value=Attribute(value=Name(id='self',
                                                                                ctx=Load()),
                                                                     attr='l',
                                                                     ctx=Load()))],
                                        decorator_list=[])],
                      decorator_list=[]),
             ClassDef(name='SubLen',
                      bases=[Name(id='HasLen',
                                  ctx=Load())],
                      body=[FunctionDef(name='__init__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param()),
                                                             Name(id='l',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Expr(value=Call(func=Attribute(value=Name(id='HasLen',
                                                                                        ctx=Load()),
                                                                             attr='__init__',
                                                                             ctx=Load()),
                                                              args=[Name(id='self',
                                                                         ctx=Load()),
                                                                    Name(id='l',
                                                                         ctx=Load())],
                                                              keywords=[],
                                                              starargs=None,
                                                              kwargs=None))],
                                        decorator_list=[])],
                      decorator_list=[]),
             ClassDef(name='NoLen',
                      bases=[],
                      body=[FunctionDef(name='__init__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param()),
                                                             Name(id='l',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Assign(targets=[Attribute(value=Name(id='self',
                                                                                   ctx=Load()),
                                                                        attr='l',
                                                                        ctx=Store())],
                                                     value=Name(id='l',
                                                                ctx=Load()))],
                                        decorator_list=[])],
                      decorator_list=[]),
             Assign(targets=[Name(id='h',
                                  ctx=Store())],
                    value=Call(func=Name(id='HasLen',
                                         ctx=Load()),
                               args=[Num(n=42)],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Call(func=Name(id='len',
                                          ctx=Load()),
                                args=[Name(id='h',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Assign(targets=[Name(id='h2',
                                  ctx=Store())],
                    value=Call(func=Name(id='SubLen',
                                         ctx=Load()),
                               args=[Num(n=43)],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Call(func=Name(id='len',
                                          ctx=Load()),
                                args=[Name(id='h2',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Assign(targets=[Name(id='h3',
                                  ctx=Store())],
                    value=Call(func=Name(id='NoLen',
                                         ctx=Load()),
                               args=[Num(n=44)],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Call(func=Name(id='len',
                                          ctx=Load()),
                                args=[Name(id='h3',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True)])
