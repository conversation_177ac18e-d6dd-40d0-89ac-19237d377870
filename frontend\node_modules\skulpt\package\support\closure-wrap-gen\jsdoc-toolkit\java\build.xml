<project>
   <target name="clean">
        <delete dir="build"/>
    </target>

    <target name="compile">
        <mkdir dir="build/classes"/>
        <javac 
         srcdir="src" destdir="build/classes"
         classpath="./classes/js.jar"
        />
    </target>

    <target name="jar">
        <mkdir dir="build/jar"/>
        <jar destfile="build/jar/jsrun.jar" basedir="build/classes">
            <manifest>
               <attribute name="Main-Class" value="JsRun"/>
               <attribute name="Class-Path" value="./java/classes/js.jar"/>
            </manifest>
        </jar>
    </target>
    
    <target name="debugjar">
        <mkdir dir="build/jar"/>
        <jar destfile="build/jar/jsdebug.jar" basedir="build/classes">
            <manifest>
               <attribute name="Main-Class" value="JsDebugRun"/>
               <attribute name="Class-Path" value="./java/classes/js.jar"/>
            </manifest>
        </jar>
    </target>

    <target name="clean-build" depends="clean,compile,jar"/>
    <target name="clean-debug" depends="clean,compile,debugjar"/>
</project>