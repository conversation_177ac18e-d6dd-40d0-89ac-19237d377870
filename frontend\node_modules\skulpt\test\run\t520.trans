Module(body=[Assign(targets=[Name(id='x',
                                  ctx=Store())],
                    value=Call(func=Name(id='any',
                                         ctx=Load()),
                               args=[List(elts=[Num(n=1),
                                                Num(n=2)],
                                          ctx=Load())],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Name(id='x',
                                ctx=Load()),
                           Call(func=Name(id='type',
                                          ctx=Load()),
                                args=[Name(id='x',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Assign(targets=[Name(id='y',
                                  ctx=Store())],
                    value=Call(func=Name(id='all',
                                         ctx=Load()),
                               args=[List(elts=[Num(n=1),
                                                Num(n=2)],
                                          ctx=Load())],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Name(id='x',
                                ctx=Load()),
                           Call(func=Name(id='type',
                                          ctx=Load()),
                                args=[Name(id='x',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Assign(targets=[Name(id='z',
                                  ctx=Store())],
                    value=Call(func=Name(id='isinstance',
                                         ctx=Load()),
                               args=[Num(n=5),
                                     Name(id='int',
                                          ctx=Load())],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Name(id='z',
                                ctx=Load()),
                           Call(func=Name(id='type',
                                          ctx=Load()),
                                args=[Name(id='z',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='hash',
                                          ctx=Load()),
                                args=[Name(id='True',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Call(func=Name(id='type',
                                          ctx=Load()),
                                args=[Call(func=Name(id='hash',
                                                     ctx=Load()),
                                           args=[Name(id='True',
                                                      ctx=Load())],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='hash',
                                          ctx=Load()),
                                args=[Name(id='None',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Call(func=Name(id='type',
                                          ctx=Load()),
                                args=[Call(func=Name(id='hash',
                                                     ctx=Load()),
                                           args=[Name(id='None',
                                                      ctx=Load())],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='hash',
                                          ctx=Load()),
                                args=[Str(s='hello')],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Call(func=Name(id='type',
                                          ctx=Load()),
                                args=[Call(func=Name(id='hash',
                                                     ctx=Load()),
                                           args=[Str(s='hello')],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Assign(targets=[Name(id='a',
                                  ctx=Store())],
                    value=Call(func=Name(id='hasattr',
                                         ctx=Load()),
                               args=[Str(s='hello'),
                                     Str(s='not_a_method')],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Name(id='a',
                                ctx=Load()),
                           Call(func=Name(id='type',
                                          ctx=Load()),
                                args=[Name(id='a',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True)])
