Module(body=[Assign(targets=[Name(id='a',
                                  ctx=Store())],
                    value=Dict(keys=[],
                               values=[])),
             Assign(targets=[Name(id='b',
                                  ctx=Store())],
                    value=Subscript(value=Name(id='a',
                                               ctx=Load()),
                                    slice=Index(value=Tuple(elts=[Num(n=1),
                                                                  Num(n=2)],
                                                            ctx=Load())),
                                    ctx=Load())),
             Assign(targets=[Name(id='b',
                                  ctx=Store())],
                    value=Subscript(value=Name(id='a',
                                               ctx=Load()),
                                    slice=Index(value=Str(s='something')),
                                    ctx=Load())),
             Print(dest=None,
                   values=[Name(id='b',
                                ctx=Load())],
                   nl=True)])
