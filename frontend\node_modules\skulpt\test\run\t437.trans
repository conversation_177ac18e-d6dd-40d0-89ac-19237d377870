Mo<PERSON>le(body=[Import(names=[alias(name='re',
                                 asname=None)]),
             Assign(targets=[Name(id='m',
                                  ctx=Store())],
                    value=Call(func=Attribute(value=Name(id='re',
                                                         ctx=Load()),
                                              attr='match',
                                              ctx=Load()),
                               args=[Str(s='([0-9]+)([a-z]+)([A-Z]*)'),
                                     Str(s='345abu')],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Str(s='\ngroup')],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Attribute(value=Name(id='m',
                                                                       ctx=Load()),
                                                            attr='group',
                                                            ctx=Load()),
                                             args=[],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Str(s='345abu')])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Attribute(value=Name(id='m',
                                                                       ctx=Load()),
                                                            attr='group',
                                                            ctx=Load()),
                                             args=[Num(n=0)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Str(s='345abu')])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Attribute(value=Name(id='m',
                                                                       ctx=Load()),
                                                            attr='group',
                                                            ctx=Load()),
                                             args=[Num(n=1)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Str(s='345')])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Attribute(value=Name(id='m',
                                                                       ctx=Load()),
                                                            attr='group',
                                                            ctx=Load()),
                                             args=[Num(n=2)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Str(s='abu')])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Attribute(value=Name(id='m',
                                                                       ctx=Load()),
                                                            attr='group',
                                                            ctx=Load()),
                                             args=[Num(n=3)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Str(s='')])],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\ngroups')],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Attribute(value=Name(id='m',
                                                                       ctx=Load()),
                                                            attr='groups',
                                                            ctx=Load()),
                                             args=[],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Tuple(elts=[Str(s='345'),
                                                            Str(s='abu'),
                                                            Str(s='')],
                                                      ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Attribute(value=Name(id='m',
                                                                       ctx=Load()),
                                                            attr='groups',
                                                            ctx=Load()),
                                             args=[Str(s='default')],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Tuple(elts=[Str(s='345'),
                                                            Str(s='abu'),
                                                            Str(s='')],
                                                      ctx=Load())])],
                   nl=True)])
