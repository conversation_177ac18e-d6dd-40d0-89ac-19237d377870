Module(body=[Assign(targets=[Name(id='a',
                                  ctx=Store())],
                    value=List(elts=[Num(n=100),
                                     Num(n=101),
                                     Num(n=102),
                                     Num(n=103),
                                     <PERSON>um(n=104),
                                     <PERSON>um(n=105),
                                     <PERSON>um(n=106),
                                     Num(n=107)],
                               ctx=Load())),
             Delete(targets=[Subscript(value=Name(id='a',
                                                  ctx=Load()),
                                       slice=Slice(lower=None,
                                                   upper=None,
                                                   step=None),
                                       ctx=Del())]),
             Print(dest=None,
                   values=[Name(id='a',
                                ctx=Load())],
                   nl=True)])
