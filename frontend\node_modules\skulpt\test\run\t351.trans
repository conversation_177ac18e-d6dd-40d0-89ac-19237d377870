Module(body=[FunctionDef(name='f',
                         args=arguments(args=[Name(id='x',
                                                   ctx=Param())],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[]),
                         body=[Print(dest=None,
                                     values=[Name(id='x',
                                                  ctx=Load())],
                                     nl=True)],
                         decorator_list=[]),
             Expr(value=Call(func=Call(func=Name(id='f',
                                                 ctx=Load()),
                                       args=[Num(n=3)],
                                       keywords=[],
                                       starargs=None,
                                       kwargs=None),
                             args=[],
                             keywords=[],
                             starargs=None,
                             kwargs=None))])
