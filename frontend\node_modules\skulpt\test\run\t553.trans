Module(body=[ImportFrom(module='time',
                        names=[alias(name='sleep',
                                     asname=None)],
                        level=0),
             ClassDef(name='GeneratorClass',
                      bases=[],
                      body=[Assign(targets=[Name(id='test',
                                                 ctx=Store())],
                                   value=Str(s='hi')),
                            FunctionDef(name='__init__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Pass()],
                                        decorator_list=[]),
                            FunctionDef(name='generator',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Print(dest=None,
                                                    values=[Attribute(value=Name(id='self',
                                                                                 ctx=Load()),
                                                                      attr='test',
                                                                      ctx=Load())],
                                                    nl=True),
                                              For(target=Name(id='i',
                                                              ctx=Store()),
                                                  iter=Call(func=Name(id='range',
                                                                      ctx=Load()),
                                                            args=[Num(n=10)],
                                                            keywords=[],
                                                            starargs=None,
                                                            kwargs=None),
                                                  body=[Expr(value=Yield(value=Name(id='i',
                                                                                    ctx=Load())))],
                                                  orelse=[])],
                                        decorator_list=[]),
                            FunctionDef(name='sleeping_generator',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Print(dest=None,
                                                    values=[Attribute(value=Name(id='self',
                                                                                 ctx=Load()),
                                                                      attr='test',
                                                                      ctx=Load())],
                                                    nl=True),
                                              For(target=Name(id='i',
                                                              ctx=Store()),
                                                  iter=Call(func=Name(id='range',
                                                                      ctx=Load()),
                                                            args=[Num(n=10)],
                                                            keywords=[],
                                                            starargs=None,
                                                            kwargs=None),
                                                  body=[Expr(value=Call(func=Name(id='sleep',
                                                                                  ctx=Load()),
                                                                        args=[Num(n=0.01)],
                                                                        keywords=[],
                                                                        starargs=None,
                                                                        kwargs=None)),
                                                        Expr(value=Yield(value=Name(id='i',
                                                                                    ctx=Load())))],
                                                  orelse=[])],
                                        decorator_list=[])],
                      decorator_list=[]),
             Assign(targets=[Name(id='gen',
                                  ctx=Store())],
                    value=Call(func=Name(id='GeneratorClass',
                                         ctx=Load()),
                               args=[],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             For(target=Name(id='g',
                             ctx=Store()),
                 iter=Call(func=Attribute(value=Name(id='gen',
                                                     ctx=Load()),
                                          attr='generator',
                                          ctx=Load()),
                           args=[],
                           keywords=[],
                           starargs=None,
                           kwargs=None),
                 body=[Print(dest=None,
                             values=[Name(id='g',
                                          ctx=Load())],
                             nl=True)],
                 orelse=[]),
             For(target=Name(id='g',
                             ctx=Store()),
                 iter=Call(func=Attribute(value=Name(id='gen',
                                                     ctx=Load()),
                                          attr='sleeping_generator',
                                          ctx=Load()),
                           args=[],
                           keywords=[],
                           starargs=None,
                           kwargs=None),
                 body=[Print(dest=None,
                             values=[Name(id='g',
                                          ctx=Load())],
                             nl=True)],
                 orelse=[]),
             Print(dest=None,
                   values=[Call(func=Name(id='list',
                                          ctx=Load()),
                                args=[Call(func=Attribute(value=Name(id='gen',
                                                                     ctx=Load()),
                                                          attr='generator',
                                                          ctx=Load()),
                                           args=[],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='list',
                                          ctx=Load()),
                                args=[Call(func=Attribute(value=Name(id='gen',
                                                                     ctx=Load()),
                                                          attr='sleeping_generator',
                                                          ctx=Load()),
                                           args=[],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[ListComp(elt=BinOp(left=Name(id='x',
                                                        ctx=Load()),
                                              op=Mult(),
                                              right=Num(n=2)),
                                    generators=[comprehension(target=Name(id='x',
                                                                          ctx=Store()),
                                                              iter=Call(func=Attribute(value=Name(id='gen',
                                                                                                  ctx=Load()),
                                                                                       attr='generator',
                                                                                       ctx=Load()),
                                                                        args=[],
                                                                        keywords=[],
                                                                        starargs=None,
                                                                        kwargs=None),
                                                              ifs=[])])],
                   nl=True),
             Print(dest=None,
                   values=[ListComp(elt=BinOp(left=Name(id='x',
                                                        ctx=Load()),
                                              op=Mult(),
                                              right=Num(n=2)),
                                    generators=[comprehension(target=Name(id='x',
                                                                          ctx=Store()),
                                                              iter=Call(func=Attribute(value=Name(id='gen',
                                                                                                  ctx=Load()),
                                                                                       attr='sleeping_generator',
                                                                                       ctx=Load()),
                                                                        args=[],
                                                                        keywords=[],
                                                                        starargs=None,
                                                                        kwargs=None),
                                                              ifs=[])])],
                   nl=True)])
