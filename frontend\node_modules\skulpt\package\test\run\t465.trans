Module(body=[Import(names=[alias(name='re',
                                 asname=None)]),
             Print(dest=None,
                   values=[Str(s='\nSyntax: .')],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='.'),
                                      Str(s='')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='.'),
                                      Str(s='a')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='.a'),
                                      Str(s='a')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='a'),
                                      Str(s='a')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='a.'),
                                      Str(s='a\n')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='.a'),
                                      Str(s='ba')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\nSyntax: ^')],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='^'),
                                      Str(s='')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='a^'),
                                      Str(s='')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='^a'),
                                      Str(s='ba')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='^a'),
                                      Str(s='ab')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='^a'),
                                      Str(s='\na')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='a^'),
                                      Str(s='a')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\nSyntax: $')],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='$'),
                                      Str(s='')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='$a'),
                                      Str(s='a')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='a$'),
                                      Str(s='a')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='a$'),
                                      Str(s='ab')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='a$'),
                                      Str(s='a\nb')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='a$'),
                                      Str(s='a\n')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\nSyntax: *')],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='a*'),
                                      Str(s='')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='ab*'),
                                      Str(s='a')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='ab*'),
                                      Str(s='ab')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='ab*'),
                                      Str(s='abbbbb')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='ab*'),
                                      Str(s='ba')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='ab*'),
                                      Str(s='bbbb')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\nSyntax: +')],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='a+'),
                                      Str(s='')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='ab+'),
                                      Str(s='a')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='ab+'),
                                      Str(s='ab')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='ab+'),
                                      Str(s='abbbbb')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='ab+'),
                                      Str(s='ba')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='ab+'),
                                      Str(s='bbbb')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\nSyntax: ?')],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='a?'),
                                      Str(s='')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='ab?'),
                                      Str(s='a')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='ab?'),
                                      Str(s='ab')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='ab?'),
                                      Str(s='abbbbb')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='ab?'),
                                      Str(s='ba')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='ab?'),
                                      Str(s='bbbb')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\nSyntax: *?')],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='a*?'),
                                      Str(s='a')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='ab*?'),
                                      Str(s='abbbb')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='ab*?'),
                                      Str(s='a')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='ab*?'),
                                      Str(s='')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\nSyntax: +?')],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='a+?'),
                                      Str(s='a')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='ab+?'),
                                      Str(s='abbbb')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='ab+?'),
                                      Str(s='a')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='ab+?'),
                                      Str(s='')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\nSyntax: ??')],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='a??'),
                                      Str(s='a')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='ab??'),
                                      Str(s='abbbb')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='ab??'),
                                      Str(s='a')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='ab??'),
                                      Str(s='')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\nSyntax: {m}')],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='a{2}'),
                                      Str(s='a')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='a{2}'),
                                      Str(s='aa')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='a{2}'),
                                      Str(s='aaa')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\nSyntax: {m,n}')],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='a{1,2}b'),
                                      Str(s='b')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='a{1,2}b'),
                                      Str(s='ab')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='a{1,2}b'),
                                      Str(s='aab')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='a{1,2}b'),
                                      Str(s='aaab')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='a{,2}b'),
                                      Str(s='b')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='a{,2}b'),
                                      Str(s='ab')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='a{,2}b'),
                                      Str(s='aab')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='a{,2}b'),
                                      Str(s='aaab')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='a{2,}b'),
                                      Str(s='b')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='a{2,}b'),
                                      Str(s='ab')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='a{2,}b'),
                                      Str(s='aab')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='a{2,}b'),
                                      Str(s='aaab')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='a{3,5}'),
                                      Str(s='aaaaaaaaaa')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='a{,5}'),
                                      Str(s='aaaaaaaaaa')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='a{3,}'),
                                      Str(s='aaaaaaaaaa')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\nSyntax: {m,n}?')],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='a{1,2}?b'),
                                      Str(s='b')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='a{1,2}?b'),
                                      Str(s='ab')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='a{1,2}?b'),
                                      Str(s='aab')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='a{1,2}?b'),
                                      Str(s='aaab')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='a{,2}?b'),
                                      Str(s='b')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='a{,2}?b'),
                                      Str(s='ab')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='a{,2}?b'),
                                      Str(s='aab')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='a{,2}?b'),
                                      Str(s='aaab')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='a{2,}?b'),
                                      Str(s='b')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='a{2,}?b'),
                                      Str(s='ab')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='a{2,}?b'),
                                      Str(s='aab')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='a{2,}?b'),
                                      Str(s='aaab')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='a{3,5}?'),
                                      Str(s='aaaaaaaaaa')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='a{,5}?'),
                                      Str(s='aaaaaaaaaa')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='a{3,}?'),
                                      Str(s='aaaaaaaaaa')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\nSyntax: []')],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='[a,b,c]'),
                                      Str(s='abc')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='[a-z]'),
                                      Str(s='bc')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='[A-Z,0-9]'),
                                      Str(s='abcdefg')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='[^A-Z]'),
                                      Str(s='ABCDEFGaHIJKL')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='[a*bc]'),
                                      Str(s='*')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\nSyntax: |')],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='|'),
                                      Str(s='')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='|a'),
                                      Str(s='')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='a|b'),
                                      Str(s='ba')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='h|ello'),
                                      Str(s='hello')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\nSyntax: (...)')],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='(b*)'),
                                      Str(s='bbbba')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\nSyntax: (?...)')],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='(?:b*)'),
                                      Str(s='bbbba')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='a(?=b)'),
                                      Str(s='a')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='a(?=b)'),
                                      Str(s='ab')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='a(?!b)'),
                                      Str(s='a')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[Str(s='a(?!b)'),
                                      Str(s='ab')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True)])
