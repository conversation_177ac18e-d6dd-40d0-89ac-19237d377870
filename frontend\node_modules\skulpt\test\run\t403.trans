Module(body=[Print(dest=None,
                   values=[Str(s='EVALUATE TO TRUE:')],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='any',
                                          ctx=Load()),
                                args=[List(elts=[Name(id='True',
                                                      ctx=Load()),
                                                 Num(n=1),
                                                 Num(n=5.0),
                                                 Num(n=-33),
                                                 Str(s='hello'),
                                                 <PERSON><PERSON>(elts=[Num(n=3),
                                                             Num(n=4),
                                                             Num(n=5)],
                                                       ctx=Load()),
                                                 List(elts=[Num(n=-6),
                                                            Num(n=7),
                                                            Num(n=10.0)],
                                                      ctx=Load()),
                                                 Dict(keys=[Num(n=17),
                                                            Num(n=-11)],
                                                      values=[Str(s='true'),
                                                              Name(id='True',
                                                                   ctx=Load())]),
                                                 Name(id='False',
                                                      ctx=Load())],
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='any',
                                          ctx=Load()),
                                args=[Str(s='hello')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='any',
                                          ctx=Load()),
                                args=[Str(s='false')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='any',
                                          ctx=Load()),
                                args=[Tuple(elts=[Num(n=3),
                                                  Num(n=4),
                                                  Num(n=5)],
                                            ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='any',
                                          ctx=Load()),
                                args=[Tuple(elts=[Num(n=3),
                                                  Num(n=4)],
                                            ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='any',
                                          ctx=Load()),
                                args=[Tuple(elts=[Num(n=1),
                                                  Num(n=0)],
                                            ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='any',
                                          ctx=Load()),
                                args=[Dict(keys=[Num(n=1),
                                                 Num(n=-2),
                                                 Num(n=0),
                                                 Tuple(elts=[Num(n=2),
                                                             Num(n=3)],
                                                       ctx=Load())],
                                           values=[Num(n=10),
                                                   Str(s='hello'),
                                                   Name(id='False',
                                                        ctx=Load()),
                                                   Name(id='True',
                                                        ctx=Load())])],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='EVALUATE TO FALSE:')],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='any',
                                          ctx=Load()),
                                args=[List(elts=[Name(id='None',
                                                      ctx=Load()),
                                                 Name(id='False',
                                                      ctx=Load()),
                                                 Num(n=0),
                                                 Num(n=0),
                                                 Num(n=0.0),
                                                 Str(s=''),
                                                 Tuple(elts=[],
                                                       ctx=Load()),
                                                 List(elts=[],
                                                      ctx=Load()),
                                                 Dict(keys=[],
                                                      values=[])],
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='any',
                                          ctx=Load()),
                                args=[List(elts=[],
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='any',
                                          ctx=Load()),
                                args=[Str(s='')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='any',
                                          ctx=Load()),
                                args=[Str(s='')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='any',
                                          ctx=Load()),
                                args=[Tuple(elts=[Num(n=0),
                                                  Num(n=0.0),
                                                  Num(n=0)],
                                            ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='any',
                                          ctx=Load()),
                                args=[Tuple(elts=[],
                                            ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='any',
                                          ctx=Load()),
                                args=[Dict(keys=[Num(n=0),
                                                 Num(n=0.0)],
                                           values=[Name(id='False',
                                                        ctx=Load()),
                                                   Name(id='None',
                                                        ctx=Load())])],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='any',
                                          ctx=Load()),
                                args=[Dict(keys=[],
                                           values=[])],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True)])
