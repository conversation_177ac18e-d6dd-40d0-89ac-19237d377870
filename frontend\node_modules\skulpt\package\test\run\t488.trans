Module(body=[Assign(targets=[Name(id='a',
                                  ctx=Store())],
                    value=List(elts=[Num(n=2),
                                     Num(n=1),
                                     Num(n=-4),
                                     Num(n=3),
                                     Num(n=0),
                                     Num(n=6)],
                               ctx=Load())),
             Expr(value=Call(func=Attribute(value=Name(id='a',
                                                       ctx=Load()),
                                            attr='sort',
                                            ctx=Load()),
                             args=[],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Name(id='a',
                                ctx=Load())],
                   nl=True),
             Assign(targets=[Name(id='b',
                                  ctx=Store())],
                    value=Str(s='rksdubtheynjmpwqzlfiovxgac')),
             Print(dest=None,
                   values=[Call(func=Name(id='sorted',
                                          ctx=Load()),
                                args=[Name(id='b',
                                           ctx=Load()),
                                      Name(id='None',
                                           ctx=Load()),
                                      Lambda(args=arguments(args=[Name(id='x',
                                                                       ctx=Param())],
                                                            vararg=None,
                                                            kwarg=None,
                                                            defaults=[]),
                                             body=Call(func=Name(id='ord',
                                                                 ctx=Load()),
                                                       args=[Name(id='x',
                                                                  ctx=Load())],
                                                       keywords=[],
                                                       starargs=None,
                                                       kwargs=None))],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Assign(targets=[Name(id='c',
                                  ctx=Store())],
                    value=List(elts=[Num(n=2),
                                     Num(n=1),
                                     Num(n=-4),
                                     Num(n=3),
                                     Num(n=0),
                                     Num(n=6)],
                               ctx=Load())),
             Print(dest=None,
                   values=[Call(func=Name(id='sorted',
                                          ctx=Load()),
                                args=[Name(id='c',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='sorted',
                                          ctx=Load()),
                                args=[Name(id='c',
                                           ctx=Load()),
                                      Lambda(args=arguments(args=[Name(id='x',
                                                                       ctx=Param()),
                                                                  Name(id='y',
                                                                       ctx=Param())],
                                                            vararg=None,
                                                            kwarg=None,
                                                            defaults=[]),
                                             body=BinOp(left=Name(id='y',
                                                                  ctx=Load()),
                                                        op=Sub(),
                                                        right=Name(id='x',
                                                                   ctx=Load())))],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             ClassDef(name='Test',
                      bases=[],
                      body=[FunctionDef(name='__init__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param()),
                                                             Name(id='id',
                                                                  ctx=Param()),
                                                             Name(id='value',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Assign(targets=[Attribute(value=Name(id='self',
                                                                                   ctx=Load()),
                                                                        attr='id',
                                                                        ctx=Store())],
                                                     value=Name(id='id',
                                                                ctx=Load())),
                                              Assign(targets=[Attribute(value=Name(id='self',
                                                                                   ctx=Load()),
                                                                        attr='value',
                                                                        ctx=Store())],
                                                     value=Name(id='value',
                                                                ctx=Load()))],
                                        decorator_list=[]),
                            FunctionDef(name='__repr__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Return(value=BinOp(left=BinOp(left=BinOp(left=Str(s='id: '),
                                                                                       op=Add(),
                                                                                       right=Call(func=Name(id='str',
                                                                                                            ctx=Load()),
                                                                                                  args=[Attribute(value=Name(id='self',
                                                                                                                             ctx=Load()),
                                                                                                                  attr='id',
                                                                                                                  ctx=Load())],
                                                                                                  keywords=[],
                                                                                                  starargs=None,
                                                                                                  kwargs=None)),
                                                                            op=Add(),
                                                                            right=Str(s=' value: ')),
                                                                 op=Add(),
                                                                 right=Attribute(value=Name(id='self',
                                                                                            ctx=Load()),
                                                                                 attr='value',
                                                                                 ctx=Load())))],
                                        decorator_list=[])],
                      decorator_list=[]),
             Assign(targets=[Name(id='d',
                                  ctx=Store())],
                    value=List(elts=[Call(func=Name(id='Test',
                                                    ctx=Load()),
                                          args=[Num(n=4),
                                                Str(s='test')],
                                          keywords=[],
                                          starargs=None,
                                          kwargs=None),
                                     Call(func=Name(id='Test',
                                                    ctx=Load()),
                                          args=[Num(n=3),
                                                Str(s='test')],
                                          keywords=[],
                                          starargs=None,
                                          kwargs=None),
                                     Call(func=Name(id='Test',
                                                    ctx=Load()),
                                          args=[Num(n=6),
                                                Str(s='test')],
                                          keywords=[],
                                          starargs=None,
                                          kwargs=None),
                                     Call(func=Name(id='Test',
                                                    ctx=Load()),
                                          args=[Num(n=1),
                                                Str(s='test')],
                                          keywords=[],
                                          starargs=None,
                                          kwargs=None),
                                     Call(func=Name(id='Test',
                                                    ctx=Load()),
                                          args=[Num(n=2),
                                                Str(s='test')],
                                          keywords=[],
                                          starargs=None,
                                          kwargs=None),
                                     Call(func=Name(id='Test',
                                                    ctx=Load()),
                                          args=[Num(n=9),
                                                Str(s='test')],
                                          keywords=[],
                                          starargs=None,
                                          kwargs=None),
                                     Call(func=Name(id='Test',
                                                    ctx=Load()),
                                          args=[Num(n=0),
                                                Str(s='test')],
                                          keywords=[],
                                          starargs=None,
                                          kwargs=None)],
                               ctx=Load())),
             Print(dest=None,
                   values=[Call(func=Name(id='sorted',
                                          ctx=Load()),
                                args=[Name(id='d',
                                           ctx=Load()),
                                      Lambda(args=arguments(args=[Name(id='x',
                                                                       ctx=Param()),
                                                                  Name(id='y',
                                                                       ctx=Param())],
                                                            vararg=None,
                                                            kwarg=None,
                                                            defaults=[]),
                                             body=BinOp(left=Name(id='y',
                                                                  ctx=Load()),
                                                        op=Sub(),
                                                        right=Name(id='x',
                                                                   ctx=Load()))),
                                      Lambda(args=arguments(args=[Name(id='x',
                                                                       ctx=Param())],
                                                            vararg=None,
                                                            kwarg=None,
                                                            defaults=[]),
                                             body=Attribute(value=Name(id='x',
                                                                       ctx=Load()),
                                                            attr='id',
                                                            ctx=Load())),
                                      Name(id='True',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Name(id='c',
                                ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='sorted',
                                          ctx=Load()),
                                args=[Name(id='c',
                                           ctx=Load()),
                                      Name(id='None',
                                           ctx=Load()),
                                      Name(id='None',
                                           ctx=Load()),
                                      Name(id='True',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Expr(value=Call(func=Attribute(value=Name(id='c',
                                                       ctx=Load()),
                                            attr='sort',
                                            ctx=Load()),
                             args=[],
                             keywords=[keyword(arg='reverse',
                                               value=Name(id='True',
                                                          ctx=Load()))],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Name(id='c',
                                ctx=Load())],
                   nl=True),
             Expr(value=Call(func=Attribute(value=Name(id='c',
                                                       ctx=Load()),
                                            attr='sort',
                                            ctx=Load()),
                             args=[],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Name(id='c',
                                ctx=Load())],
                   nl=True),
             Expr(value=Call(func=Attribute(value=Name(id='c',
                                                       ctx=Load()),
                                            attr='sort',
                                            ctx=Load()),
                             args=[Lambda(args=arguments(args=[Name(id='x',
                                                                    ctx=Param()),
                                                               Name(id='y',
                                                                    ctx=Param())],
                                                         vararg=None,
                                                         kwarg=None,
                                                         defaults=[]),
                                          body=BinOp(left=Name(id='y',
                                                               ctx=Load()),
                                                     op=Sub(),
                                                     right=Name(id='x',
                                                                ctx=Load()))),
                                   Lambda(args=arguments(args=[Name(id='x',
                                                                    ctx=Param())],
                                                         vararg=None,
                                                         kwarg=None,
                                                         defaults=[]),
                                          body=Call(func=Name(id='pow',
                                                              ctx=Load()),
                                                    args=[Name(id='x',
                                                               ctx=Load()),
                                                          Num(n=2)],
                                                    keywords=[],
                                                    starargs=None,
                                                    kwargs=None)),
                                   Name(id='True',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Name(id='c',
                                ctx=Load())],
                   nl=True),
             Assign(targets=[Name(id='L',
                                  ctx=Store())],
                    value=List(elts=[Num(n=7),
                                     Num(n=3),
                                     Num(n=-2),
                                     Num(n=4)],
                               ctx=Load())),
             Assign(targets=[Name(id='d',
                                  ctx=Store())],
                    value=Dict(keys=[Str(s='a'),
                                     Str(s='b')],
                               values=[Num(n=5),
                                       Num(n=9)])),
             FunctionDef(name='g',
                         args=arguments(args=[Name(id='k',
                                                   ctx=Param())],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[]),
                         body=[Return(value=Subscript(value=Name(id='d',
                                                                 ctx=Load()),
                                                      slice=Index(value=Name(id='k',
                                                                             ctx=Load())),
                                                      ctx=Load()))],
                         decorator_list=[]),
             Print(dest=None,
                   values=[Call(func=Name(id='g',
                                          ctx=Load()),
                                args=[Str(s='a')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='sorted',
                                          ctx=Load()),
                                args=[Call(func=Attribute(value=Name(id='d',
                                                                     ctx=Load()),
                                                          attr='keys',
                                                          ctx=Load()),
                                           args=[],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None),
                                      Name(id='None',
                                           ctx=Load()),
                                      Name(id='g',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='sorted',
                                          ctx=Load()),
                                args=[Call(func=Attribute(value=Name(id='d',
                                                                     ctx=Load()),
                                                          attr='keys',
                                                          ctx=Load()),
                                           args=[],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None),
                                      Name(id='None',
                                           ctx=Load()),
                                      Name(id='None',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='sorted',
                                          ctx=Load()),
                                args=[Call(func=Attribute(value=Name(id='d',
                                                                     ctx=Load()),
                                                          attr='keys',
                                                          ctx=Load()),
                                           args=[],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None),
                                      Name(id='None',
                                           ctx=Load()),
                                      Lambda(args=arguments(args=[Name(id='x',
                                                                       ctx=Param())],
                                                            vararg=None,
                                                            kwarg=None,
                                                            defaults=[]),
                                             body=Subscript(value=Name(id='d',
                                                                       ctx=Load()),
                                                            slice=Index(value=Name(id='x',
                                                                                   ctx=Load())),
                                                            ctx=Load()))],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             FunctionDef(name='myabs',
                         args=arguments(args=[Name(id='x',
                                                   ctx=Param())],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[]),
                         body=[Return(value=Call(func=Name(id='abs',
                                                           ctx=Load()),
                                                 args=[Name(id='x',
                                                            ctx=Load())],
                                                 keywords=[],
                                                 starargs=None,
                                                 kwargs=None))],
                         decorator_list=[]),
             Print(dest=None,
                   values=[Call(func=Name(id='sorted',
                                          ctx=Load()),
                                args=[Name(id='L',
                                           ctx=Load()),
                                      Name(id='None',
                                           ctx=Load()),
                                      Name(id='myabs',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='sorted',
                                          ctx=Load()),
                                args=[Name(id='L',
                                           ctx=Load()),
                                      Name(id='None',
                                           ctx=Load()),
                                      Lambda(args=arguments(args=[Name(id='x',
                                                                       ctx=Param())],
                                                            vararg=None,
                                                            kwarg=None,
                                                            defaults=[]),
                                             body=Call(func=Name(id='myabs',
                                                                 ctx=Load()),
                                                       args=[Name(id='x',
                                                                  ctx=Load())],
                                                       keywords=[],
                                                       starargs=None,
                                                       kwargs=None))],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='sorted',
                                          ctx=Load()),
                                args=[Name(id='L',
                                           ctx=Load()),
                                      Name(id='None',
                                           ctx=Load()),
                                      Lambda(args=arguments(args=[Name(id='x',
                                                                       ctx=Param())],
                                                            vararg=None,
                                                            kwarg=None,
                                                            defaults=[]),
                                             body=Call(func=Name(id='abs',
                                                                 ctx=Load()),
                                                       args=[Name(id='x',
                                                                  ctx=Load())],
                                                       keywords=[],
                                                       starargs=None,
                                                       kwargs=None))],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='sorted',
                                          ctx=Load()),
                                args=[Name(id='L',
                                           ctx=Load()),
                                      Name(id='None',
                                           ctx=Load()),
                                      Name(id='abs',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='sorted',
                                          ctx=Load()),
                                args=[Name(id='L',
                                           ctx=Load())],
                                keywords=[keyword(arg='key',
                                                  value=Lambda(args=arguments(args=[Name(id='x',
                                                                                         ctx=Param())],
                                                                              vararg=None,
                                                                              kwarg=None,
                                                                              defaults=[]),
                                                               body=UnaryOp(op=USub(),
                                                                            operand=Name(id='x',
                                                                                         ctx=Load())))),
                                          keyword(arg='reverse',
                                                  value=Name(id='True',
                                                             ctx=Load()))],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='sorted',
                                          ctx=Load()),
                                args=[Name(id='L',
                                           ctx=Load())],
                                keywords=[keyword(arg='key',
                                                  value=Lambda(args=arguments(args=[Name(id='x',
                                                                                         ctx=Param())],
                                                                              vararg=None,
                                                                              kwarg=None,
                                                                              defaults=[]),
                                                               body=UnaryOp(op=USub(),
                                                                            operand=Name(id='x',
                                                                                         ctx=Load()))))],
                                starargs=None,
                                kwargs=None)],
                   nl=True)])
