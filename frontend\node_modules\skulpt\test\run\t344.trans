Module(body=[For(target=Name(id='ch',
                             ctx=Store()),
                 iter=Str(s='Hello world!'),
                 body=[Assign(targets=[Name(id='d',
                                            ctx=Store())],
                              value=Call(func=Name(id='ord',
                                                   ctx=Load()),
                                         args=[Name(id='ch',
                                                    ctx=Load())],
                                         keywords=[],
                                         starargs=None,
                                         kwargs=None)),
                       Assign(targets=[Name(id='h',
                                            ctx=Store())],
                              value=Call(func=Name(id='hex',
                                                   ctx=Load()),
                                         args=[Name(id='d',
                                                    ctx=Load())],
                                         keywords=[],
                                         starargs=None,
                                         kwargs=None)),
                       Assign(targets=[Name(id='o',
                                            ctx=Store())],
                              value=Call(func=Name(id='oct',
                                                   ctx=Load()),
                                         args=[Name(id='d',
                                                    ctx=Load())],
                                         keywords=[],
                                         starargs=None,
                                         kwargs=None)),
                       Assign(targets=[Name(id='b',
                                            ctx=Store())],
                              value=Call(func=Name(id='bin',
                                                   ctx=Load()),
                                         args=[Name(id='d',
                                                    ctx=Load())],
                                         keywords=[],
                                         starargs=None,
                                         kwargs=None)),
                       Print(dest=None,
                             values=[Name(id='ch',
                                          ctx=Load()),
                                     Name(id='d',
                                          ctx=Load()),
                                     Name(id='h',
                                          ctx=Load()),
                                     Name(id='o',
                                          ctx=Load()),
                                     Name(id='b',
                                          ctx=Load())],
                             nl=True)],
                 orelse=[])])
