Module(body=[Assign(targets=[Name(id='l',
                                  ctx=Store())],
                    value=List(elts=[Str(s='b'),
                                     Str(s='a'),
                                     Str(s='d'),
                                     Str(s='c')],
                               ctx=Load())),
             Assign(targets=[Name(id='m',
                                  ctx=Store())],
                    value=List(elts=[Str(s='1'),
                                     Str(s='0'),
                                     Str(s='4'),
                                     Str(s='3')],
                               ctx=Load())),
             Print(dest=None,
                   values=[Name(id='l',
                                ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Name(id='m',
                                ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='min',
                                          ctx=Load()),
                                args=[Name(id='l',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Call(func=Name(id='max',
                                          ctx=Load()),
                                args=[Name(id='l',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='min',
                                          ctx=Load()),
                                args=[Name(id='m',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Call(func=Name(id='max',
                                          ctx=Load()),
                                args=[Name(id='m',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='min',
                                          ctx=Load()),
                                args=[Name(id='l',
                                           ctx=Load()),
                                      Name(id='m',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Call(func=Name(id='max',
                                          ctx=Load()),
                                args=[Name(id='l',
                                           ctx=Load()),
                                      Name(id='m',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Assign(targets=[Name(id='t',
                                  ctx=Store())],
                    value=Call(func=Name(id='tuple',
                                         ctx=Load()),
                               args=[Name(id='l',
                                          ctx=Load())],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Assign(targets=[Name(id='u',
                                  ctx=Store())],
                    value=Call(func=Name(id='tuple',
                                         ctx=Load()),
                               args=[Name(id='m',
                                          ctx=Load())],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Name(id='t',
                                ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='min',
                                          ctx=Load()),
                                args=[Name(id='t',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Call(func=Name(id='max',
                                          ctx=Load()),
                                args=[Name(id='t',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='min',
                                          ctx=Load()),
                                args=[Name(id='u',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Call(func=Name(id='max',
                                          ctx=Load()),
                                args=[Name(id='u',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='min',
                                          ctx=Load()),
                                args=[Name(id='t',
                                           ctx=Load()),
                                      Name(id='u',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Call(func=Name(id='max',
                                          ctx=Load()),
                                args=[Name(id='t',
                                           ctx=Load()),
                                      Name(id='u',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True)])
