Module(body=[Assign(targets=[Name(id='s',
                                  ctx=Store())],
                    value=Call(func=Name(id='set',
                                         ctx=Load()),
                               args=[Call(func=Name(id='range',
                                                    ctx=Load()),
                                          args=[Num(n=9)],
                                          keywords=[],
                                          starargs=None,
                                          kwargs=None)],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Assign(targets=[Name(id='s1',
                                  ctx=Store())],
                    value=Call(func=Name(id='set',
                                         ctx=Load()),
                               args=[Call(func=Name(id='range',
                                                    ctx=Load()),
                                          args=[Num(n=5)],
                                          keywords=[],
                                          starargs=None,
                                          kwargs=None)],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Assign(targets=[Name(id='s2',
                                  ctx=Store())],
                    value=Call(func=Name(id='set',
                                         ctx=Load()),
                               args=[Call(func=Name(id='range',
                                                    ctx=Load()),
                                          args=[Num(n=4),
                                                Num(n=9)],
                                          keywords=[],
                                          starargs=None,
                                          kwargs=None)],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Str(s='\nlen')],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Name(id='len',
                                                       ctx=Load()),
                                             args=[Call(func=Name(id='set',
                                                                  ctx=Load()),
                                                        args=[List(elts=[],
                                                                   ctx=Load())],
                                                        keywords=[],
                                                        starargs=None,
                                                        kwargs=None)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Num(n=0)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Name(id='len',
                                                       ctx=Load()),
                                             args=[Name(id='s1',
                                                        ctx=Load())],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq(),
                                        Eq()],
                                   comparators=[Call(func=Name(id='len',
                                                               ctx=Load()),
                                                     args=[Name(id='s2',
                                                                ctx=Load())],
                                                     keywords=[],
                                                     starargs=None,
                                                     kwargs=None),
                                                Num(n=5)])],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\nx in s')],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Compare(left=Num(n=4),
                                                ops=[In()],
                                                comparators=[Name(id='s1',
                                                                  ctx=Load())]),
                                   ops=[Eq(),
                                        Eq()],
                                   comparators=[Compare(left=Num(n=4),
                                                        ops=[In()],
                                                        comparators=[Name(id='s2',
                                                                          ctx=Load())]),
                                                Name(id='True',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Compare(left=Num(n=8),
                                                ops=[In()],
                                                comparators=[Name(id='s1',
                                                                  ctx=Load())]),
                                   ops=[Eq(),
                                        Eq()],
                                   comparators=[Compare(left=Num(n=1),
                                                        ops=[In()],
                                                        comparators=[Name(id='s2',
                                                                          ctx=Load())]),
                                                Name(id='False',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\nx not in s')],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Compare(left=Num(n=1),
                                                ops=[NotIn()],
                                                comparators=[Name(id='s1',
                                                                  ctx=Load())]),
                                   ops=[Eq(),
                                        Eq()],
                                   comparators=[Compare(left=Num(n=8),
                                                        ops=[NotIn()],
                                                        comparators=[Name(id='s2',
                                                                          ctx=Load())]),
                                                Name(id='False',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Compare(left=Num(n=8),
                                                ops=[NotIn()],
                                                comparators=[Name(id='s1',
                                                                  ctx=Load())]),
                                   ops=[Eq(),
                                        Eq()],
                                   comparators=[Compare(left=Num(n=1),
                                                        ops=[NotIn()],
                                                        comparators=[Name(id='s2',
                                                                          ctx=Load())]),
                                                Name(id='True',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\nisdisjoint(other)')],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Attribute(value=Name(id='s1',
                                                                       ctx=Load()),
                                                            attr='isdisjoint',
                                                            ctx=Load()),
                                             args=[Name(id='s2',
                                                        ctx=Load())],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Name(id='False',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Attribute(value=Name(id='s1',
                                                                       ctx=Load()),
                                                            attr='isdisjoint',
                                                            ctx=Load()),
                                             args=[Call(func=Name(id='set',
                                                                  ctx=Load()),
                                                        args=[Call(func=Name(id='range',
                                                                             ctx=Load()),
                                                                   args=[Num(n=5),
                                                                         Num(n=10)],
                                                                   keywords=[],
                                                                   starargs=None,
                                                                   kwargs=None)],
                                                        keywords=[],
                                                        starargs=None,
                                                        kwargs=None)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Name(id='True',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\nissubset(other)')],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Attribute(value=Name(id='s1',
                                                                       ctx=Load()),
                                                            attr='issubset',
                                                            ctx=Load()),
                                             args=[Name(id='s1',
                                                        ctx=Load())],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Name(id='True',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Attribute(value=Name(id='s1',
                                                                       ctx=Load()),
                                                            attr='issubset',
                                                            ctx=Load()),
                                             args=[Name(id='s',
                                                        ctx=Load())],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Name(id='True',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Attribute(value=Name(id='s1',
                                                                       ctx=Load()),
                                                            attr='issubset',
                                                            ctx=Load()),
                                             args=[Name(id='s2',
                                                        ctx=Load())],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Name(id='False',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\nset <= other')],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Compare(left=Name(id='s1',
                                                          ctx=Load()),
                                                ops=[LtE()],
                                                comparators=[Name(id='s',
                                                                  ctx=Load())]),
                                   ops=[Eq()],
                                   comparators=[Name(id='True',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Compare(left=Name(id='s2',
                                                          ctx=Load()),
                                                ops=[LtE()],
                                                comparators=[Name(id='s',
                                                                  ctx=Load())]),
                                   ops=[Eq()],
                                   comparators=[Name(id='True',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Compare(left=Name(id='s',
                                                          ctx=Load()),
                                                ops=[LtE()],
                                                comparators=[Name(id='s',
                                                                  ctx=Load())]),
                                   ops=[Eq()],
                                   comparators=[Name(id='True',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Compare(left=Name(id='s1',
                                                          ctx=Load()),
                                                ops=[LtE()],
                                                comparators=[Name(id='s2',
                                                                  ctx=Load())]),
                                   ops=[Eq()],
                                   comparators=[Name(id='False',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\nset < other')],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Compare(left=Name(id='s1',
                                                          ctx=Load()),
                                                ops=[Lt()],
                                                comparators=[Name(id='s',
                                                                  ctx=Load())]),
                                   ops=[Eq()],
                                   comparators=[Name(id='True',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Compare(left=Name(id='s2',
                                                          ctx=Load()),
                                                ops=[Lt()],
                                                comparators=[Name(id='s',
                                                                  ctx=Load())]),
                                   ops=[Eq()],
                                   comparators=[Name(id='True',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Compare(left=Name(id='s',
                                                          ctx=Load()),
                                                ops=[Lt()],
                                                comparators=[Name(id='s',
                                                                  ctx=Load())]),
                                   ops=[Eq()],
                                   comparators=[Name(id='False',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Compare(left=Name(id='s1',
                                                          ctx=Load()),
                                                ops=[Lt()],
                                                comparators=[Name(id='s2',
                                                                  ctx=Load())]),
                                   ops=[Eq()],
                                   comparators=[Name(id='False',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\nissuperset(other)')],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Attribute(value=Name(id='s2',
                                                                       ctx=Load()),
                                                            attr='issuperset',
                                                            ctx=Load()),
                                             args=[Name(id='s2',
                                                        ctx=Load())],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Name(id='True',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Attribute(value=Name(id='s',
                                                                       ctx=Load()),
                                                            attr='issuperset',
                                                            ctx=Load()),
                                             args=[Name(id='s1',
                                                        ctx=Load())],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Name(id='True',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Attribute(value=Name(id='s2',
                                                                       ctx=Load()),
                                                            attr='issuperset',
                                                            ctx=Load()),
                                             args=[Name(id='s1',
                                                        ctx=Load())],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Name(id='False',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\nset >= other')],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Compare(left=Name(id='s',
                                                          ctx=Load()),
                                                ops=[GtE()],
                                                comparators=[Name(id='s1',
                                                                  ctx=Load())]),
                                   ops=[Eq()],
                                   comparators=[Name(id='True',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Compare(left=Name(id='s',
                                                          ctx=Load()),
                                                ops=[GtE()],
                                                comparators=[Name(id='s2',
                                                                  ctx=Load())]),
                                   ops=[Eq()],
                                   comparators=[Name(id='True',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Compare(left=Name(id='s',
                                                          ctx=Load()),
                                                ops=[GtE()],
                                                comparators=[Name(id='s',
                                                                  ctx=Load())]),
                                   ops=[Eq()],
                                   comparators=[Name(id='True',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Compare(left=Name(id='s1',
                                                          ctx=Load()),
                                                ops=[GtE()],
                                                comparators=[Name(id='s2',
                                                                  ctx=Load())]),
                                   ops=[Eq()],
                                   comparators=[Name(id='False',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\nset > other')],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Compare(left=Name(id='s',
                                                          ctx=Load()),
                                                ops=[Gt()],
                                                comparators=[Name(id='s1',
                                                                  ctx=Load())]),
                                   ops=[Eq()],
                                   comparators=[Name(id='True',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Compare(left=Name(id='s',
                                                          ctx=Load()),
                                                ops=[Gt()],
                                                comparators=[Name(id='s2',
                                                                  ctx=Load())]),
                                   ops=[Eq()],
                                   comparators=[Name(id='True',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Compare(left=Name(id='s',
                                                          ctx=Load()),
                                                ops=[Gt()],
                                                comparators=[Name(id='s',
                                                                  ctx=Load())]),
                                   ops=[Eq()],
                                   comparators=[Name(id='False',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Compare(left=Name(id='s1',
                                                          ctx=Load()),
                                                ops=[Gt()],
                                                comparators=[Name(id='s2',
                                                                  ctx=Load())]),
                                   ops=[Eq()],
                                   comparators=[Name(id='False',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\nunion(other,...)')],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Attribute(value=Call(func=Name(id='set',
                                                                                 ctx=Load()),
                                                                       args=[List(elts=[],
                                                                                  ctx=Load())],
                                                                       keywords=[],
                                                                       starargs=None,
                                                                       kwargs=None),
                                                            attr='union',
                                                            ctx=Load()),
                                             args=[Name(id='s1',
                                                        ctx=Load())],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Name(id='s1',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Attribute(value=Name(id='s1',
                                                                       ctx=Load()),
                                                            attr='union',
                                                            ctx=Load()),
                                             args=[Call(func=Name(id='set',
                                                                  ctx=Load()),
                                                        args=[List(elts=[],
                                                                   ctx=Load())],
                                                        keywords=[],
                                                        starargs=None,
                                                        kwargs=None)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Name(id='s1',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Attribute(value=Name(id='s1',
                                                                       ctx=Load()),
                                                            attr='union',
                                                            ctx=Load()),
                                             args=[Name(id='s2',
                                                        ctx=Load())],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Name(id='s',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Attribute(value=Name(id='s1',
                                                                       ctx=Load()),
                                                            attr='union',
                                                            ctx=Load()),
                                             args=[Name(id='s2',
                                                        ctx=Load()),
                                                   Call(func=Name(id='set',
                                                                  ctx=Load()),
                                                        args=[List(elts=[Num(n=4),
                                                                         Num(n=5),
                                                                         Num(n=6)],
                                                                   ctx=Load())],
                                                        keywords=[],
                                                        starargs=None,
                                                        kwargs=None)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Name(id='s',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\nintersection(other,...)')],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Attribute(value=Call(func=Name(id='set',
                                                                                 ctx=Load()),
                                                                       args=[List(elts=[],
                                                                                  ctx=Load())],
                                                                       keywords=[],
                                                                       starargs=None,
                                                                       kwargs=None),
                                                            attr='intersection',
                                                            ctx=Load()),
                                             args=[Name(id='s1',
                                                        ctx=Load())],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Call(func=Name(id='set',
                                                               ctx=Load()),
                                                     args=[List(elts=[],
                                                                ctx=Load())],
                                                     keywords=[],
                                                     starargs=None,
                                                     kwargs=None)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Attribute(value=Name(id='s1',
                                                                       ctx=Load()),
                                                            attr='intersection',
                                                            ctx=Load()),
                                             args=[Call(func=Name(id='set',
                                                                  ctx=Load()),
                                                        args=[List(elts=[],
                                                                   ctx=Load())],
                                                        keywords=[],
                                                        starargs=None,
                                                        kwargs=None)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Call(func=Name(id='set',
                                                               ctx=Load()),
                                                     args=[List(elts=[],
                                                                ctx=Load())],
                                                     keywords=[],
                                                     starargs=None,
                                                     kwargs=None)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Attribute(value=Name(id='s1',
                                                                       ctx=Load()),
                                                            attr='intersection',
                                                            ctx=Load()),
                                             args=[Name(id='s2',
                                                        ctx=Load())],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Call(func=Name(id='set',
                                                               ctx=Load()),
                                                     args=[List(elts=[Num(n=4)],
                                                                ctx=Load())],
                                                     keywords=[],
                                                     starargs=None,
                                                     kwargs=None)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Attribute(value=Name(id='s',
                                                                       ctx=Load()),
                                                            attr='intersection',
                                                            ctx=Load()),
                                             args=[Name(id='s1',
                                                        ctx=Load()),
                                                   Name(id='s2',
                                                        ctx=Load())],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Call(func=Name(id='set',
                                                               ctx=Load()),
                                                     args=[List(elts=[Num(n=4)],
                                                                ctx=Load())],
                                                     keywords=[],
                                                     starargs=None,
                                                     kwargs=None)])],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\ndifference(other,...)')],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Attribute(value=Call(func=Name(id='set',
                                                                                 ctx=Load()),
                                                                       args=[List(elts=[],
                                                                                  ctx=Load())],
                                                                       keywords=[],
                                                                       starargs=None,
                                                                       kwargs=None),
                                                            attr='difference',
                                                            ctx=Load()),
                                             args=[Name(id='s1',
                                                        ctx=Load())],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Call(func=Name(id='set',
                                                               ctx=Load()),
                                                     args=[List(elts=[],
                                                                ctx=Load())],
                                                     keywords=[],
                                                     starargs=None,
                                                     kwargs=None)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Attribute(value=Name(id='s1',
                                                                       ctx=Load()),
                                                            attr='difference',
                                                            ctx=Load()),
                                             args=[Call(func=Name(id='set',
                                                                  ctx=Load()),
                                                        args=[List(elts=[],
                                                                   ctx=Load())],
                                                        keywords=[],
                                                        starargs=None,
                                                        kwargs=None)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Name(id='s1',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Attribute(value=Name(id='s1',
                                                                       ctx=Load()),
                                                            attr='difference',
                                                            ctx=Load()),
                                             args=[Name(id='s2',
                                                        ctx=Load())],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Call(func=Name(id='set',
                                                               ctx=Load()),
                                                     args=[List(elts=[Num(n=0),
                                                                      Num(n=1),
                                                                      Num(n=2),
                                                                      Num(n=3)],
                                                                ctx=Load())],
                                                     keywords=[],
                                                     starargs=None,
                                                     kwargs=None)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Attribute(value=Name(id='s',
                                                                       ctx=Load()),
                                                            attr='difference',
                                                            ctx=Load()),
                                             args=[Name(id='s1',
                                                        ctx=Load()),
                                                   Name(id='s2',
                                                        ctx=Load())],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Call(func=Name(id='set',
                                                               ctx=Load()),
                                                     args=[List(elts=[],
                                                                ctx=Load())],
                                                     keywords=[],
                                                     starargs=None,
                                                     kwargs=None)])],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\nsymmetric_difference(other)')],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Attribute(value=Call(func=Name(id='set',
                                                                                 ctx=Load()),
                                                                       args=[List(elts=[],
                                                                                  ctx=Load())],
                                                                       keywords=[],
                                                                       starargs=None,
                                                                       kwargs=None),
                                                            attr='symmetric_difference',
                                                            ctx=Load()),
                                             args=[Name(id='s1',
                                                        ctx=Load())],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Name(id='s1',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Attribute(value=Name(id='s1',
                                                                       ctx=Load()),
                                                            attr='symmetric_difference',
                                                            ctx=Load()),
                                             args=[Call(func=Name(id='set',
                                                                  ctx=Load()),
                                                        args=[List(elts=[],
                                                                   ctx=Load())],
                                                        keywords=[],
                                                        starargs=None,
                                                        kwargs=None)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Name(id='s1',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Attribute(value=Name(id='s1',
                                                                       ctx=Load()),
                                                            attr='symmetric_difference',
                                                            ctx=Load()),
                                             args=[Name(id='s2',
                                                        ctx=Load())],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Call(func=Name(id='set',
                                                               ctx=Load()),
                                                     args=[List(elts=[Num(n=0),
                                                                      Num(n=1),
                                                                      Num(n=2),
                                                                      Num(n=3),
                                                                      Num(n=5),
                                                                      Num(n=6),
                                                                      Num(n=7),
                                                                      Num(n=8)],
                                                                ctx=Load())],
                                                     keywords=[],
                                                     starargs=None,
                                                     kwargs=None)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Attribute(value=Name(id='s',
                                                                       ctx=Load()),
                                                            attr='symmetric_difference',
                                                            ctx=Load()),
                                             args=[Call(func=Attribute(value=Name(id='s1',
                                                                                  ctx=Load()),
                                                                       attr='symmetric_difference',
                                                                       ctx=Load()),
                                                        args=[Name(id='s2',
                                                                   ctx=Load())],
                                                        keywords=[],
                                                        starargs=None,
                                                        kwargs=None)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Call(func=Name(id='set',
                                                               ctx=Load()),
                                                     args=[List(elts=[Num(n=4)],
                                                                ctx=Load())],
                                                     keywords=[],
                                                     starargs=None,
                                                     kwargs=None)])],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\ncopy()')],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Attribute(value=Call(func=Name(id='set',
                                                                                 ctx=Load()),
                                                                       args=[List(elts=[],
                                                                                  ctx=Load())],
                                                                       keywords=[],
                                                                       starargs=None,
                                                                       kwargs=None),
                                                            attr='copy',
                                                            ctx=Load()),
                                             args=[],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Call(func=Name(id='set',
                                                               ctx=Load()),
                                                     args=[List(elts=[],
                                                                ctx=Load())],
                                                     keywords=[],
                                                     starargs=None,
                                                     kwargs=None)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Attribute(value=Name(id='s1',
                                                                       ctx=Load()),
                                                            attr='copy',
                                                            ctx=Load()),
                                             args=[],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Name(id='s1',
                                                     ctx=Load())])],
                   nl=True),
             Assign(targets=[Name(id='s3',
                                  ctx=Store())],
                    value=Call(func=Attribute(value=Name(id='s1',
                                                         ctx=Load()),
                                              attr='copy',
                                              ctx=Load()),
                               args=[],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Assign(targets=[Name(id='s1',
                                  ctx=Store())],
                    value=Call(func=Name(id='set',
                                         ctx=Load()),
                               args=[Call(func=Name(id='range',
                                                    ctx=Load()),
                                          args=[Num(n=1),
                                                Num(n=5)],
                                          keywords=[],
                                          starargs=None,
                                          kwargs=None)],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Compare(left=Name(id='s1',
                                             ctx=Load()),
                                   ops=[NotEq()],
                                   comparators=[Name(id='s3',
                                                     ctx=Load())])],
                   nl=True),
             Assign(targets=[Name(id='s3',
                                  ctx=Store())],
                    value=Call(func=Attribute(value=Name(id='s1',
                                                         ctx=Load()),
                                              attr='copy',
                                              ctx=Load()),
                               args=[],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Expr(value=Call(func=Attribute(value=Name(id='s1',
                                                       ctx=Load()),
                                            attr='add',
                                            ctx=Load()),
                             args=[Num(n=0)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Compare(left=Name(id='s1',
                                             ctx=Load()),
                                   ops=[NotEq()],
                                   comparators=[Name(id='s3',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\nupdate(other,...)')],
                   nl=True),
             Assign(targets=[Name(id='empty',
                                  ctx=Store())],
                    value=Call(func=Name(id='set',
                                         ctx=Load()),
                               args=[List(elts=[],
                                          ctx=Load())],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Expr(value=Call(func=Attribute(value=Name(id='empty',
                                                       ctx=Load()),
                                            attr='update',
                                            ctx=Load()),
                             args=[Name(id='s1',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Compare(left=Name(id='empty',
                                             ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Name(id='s1',
                                                     ctx=Load())])],
                   nl=True),
             Expr(value=Call(func=Attribute(value=Name(id='empty',
                                                       ctx=Load()),
                                            attr='update',
                                            ctx=Load()),
                             args=[Call(func=Name(id='set',
                                                  ctx=Load()),
                                        args=[List(elts=[],
                                                   ctx=Load())],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Compare(left=Name(id='empty',
                                             ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Name(id='s1',
                                                     ctx=Load())])],
                   nl=True),
             Expr(value=Call(func=Attribute(value=Name(id='empty',
                                                       ctx=Load()),
                                            attr='update',
                                            ctx=Load()),
                             args=[Name(id='s2',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Compare(left=Name(id='empty',
                                             ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Name(id='s',
                                                     ctx=Load())])],
                   nl=True),
             Expr(value=Call(func=Attribute(value=Name(id='empty',
                                                       ctx=Load()),
                                            attr='update',
                                            ctx=Load()),
                             args=[Name(id='s1',
                                        ctx=Load()),
                                   Name(id='s2',
                                        ctx=Load()),
                                   Call(func=Name(id='set',
                                                  ctx=Load()),
                                        args=[List(elts=[Num(n=4),
                                                         Num(n=5),
                                                         Num(n=6)],
                                                   ctx=Load())],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Compare(left=Name(id='empty',
                                             ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Name(id='s',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\nintersection_update(other,...)')],
                   nl=True),
             Assign(targets=[Name(id='empty',
                                  ctx=Store())],
                    value=Call(func=Name(id='set',
                                         ctx=Load()),
                               args=[List(elts=[],
                                          ctx=Load())],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Expr(value=Call(func=Attribute(value=Name(id='empty',
                                                       ctx=Load()),
                                            attr='intersection_update',
                                            ctx=Load()),
                             args=[Name(id='s1',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Compare(left=Name(id='empty',
                                             ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Call(func=Name(id='set',
                                                               ctx=Load()),
                                                     args=[List(elts=[],
                                                                ctx=Load())],
                                                     keywords=[],
                                                     starargs=None,
                                                     kwargs=None)])],
                   nl=True),
             Assign(targets=[Name(id='empty',
                                  ctx=Store())],
                    value=Call(func=Attribute(value=Name(id='s1',
                                                         ctx=Load()),
                                              attr='copy',
                                              ctx=Load()),
                               args=[],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Expr(value=Call(func=Attribute(value=Name(id='empty',
                                                       ctx=Load()),
                                            attr='intersection_update',
                                            ctx=Load()),
                             args=[Call(func=Name(id='set',
                                                  ctx=Load()),
                                        args=[List(elts=[],
                                                   ctx=Load())],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Compare(left=Name(id='empty',
                                             ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Call(func=Name(id='set',
                                                               ctx=Load()),
                                                     args=[List(elts=[],
                                                                ctx=Load())],
                                                     keywords=[],
                                                     starargs=None,
                                                     kwargs=None)])],
                   nl=True),
             Assign(targets=[Name(id='empty',
                                  ctx=Store())],
                    value=Call(func=Attribute(value=Name(id='s1',
                                                         ctx=Load()),
                                              attr='copy',
                                              ctx=Load()),
                               args=[],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Expr(value=Call(func=Attribute(value=Name(id='empty',
                                                       ctx=Load()),
                                            attr='intersection_update',
                                            ctx=Load()),
                             args=[Name(id='s2',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Compare(left=Name(id='empty',
                                             ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Call(func=Name(id='set',
                                                               ctx=Load()),
                                                     args=[List(elts=[Num(n=4)],
                                                                ctx=Load())],
                                                     keywords=[],
                                                     starargs=None,
                                                     kwargs=None)])],
                   nl=True),
             Assign(targets=[Name(id='empty',
                                  ctx=Store())],
                    value=Call(func=Attribute(value=Name(id='s',
                                                         ctx=Load()),
                                              attr='copy',
                                              ctx=Load()),
                               args=[],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Expr(value=Call(func=Attribute(value=Name(id='empty',
                                                       ctx=Load()),
                                            attr='intersection_update',
                                            ctx=Load()),
                             args=[Name(id='s1',
                                        ctx=Load()),
                                   Name(id='s2',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Compare(left=Name(id='empty',
                                             ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Call(func=Name(id='set',
                                                               ctx=Load()),
                                                     args=[List(elts=[Num(n=4)],
                                                                ctx=Load())],
                                                     keywords=[],
                                                     starargs=None,
                                                     kwargs=None)])],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\ndifference(other,...)')],
                   nl=True),
             Assign(targets=[Name(id='empty',
                                  ctx=Store())],
                    value=Call(func=Name(id='set',
                                         ctx=Load()),
                               args=[List(elts=[],
                                          ctx=Load())],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Expr(value=Call(func=Attribute(value=Name(id='empty',
                                                       ctx=Load()),
                                            attr='difference_update',
                                            ctx=Load()),
                             args=[Name(id='s1',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Compare(left=Name(id='empty',
                                             ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Call(func=Name(id='set',
                                                               ctx=Load()),
                                                     args=[List(elts=[],
                                                                ctx=Load())],
                                                     keywords=[],
                                                     starargs=None,
                                                     kwargs=None)])],
                   nl=True),
             Assign(targets=[Name(id='empty',
                                  ctx=Store())],
                    value=Call(func=Attribute(value=Name(id='s1',
                                                         ctx=Load()),
                                              attr='copy',
                                              ctx=Load()),
                               args=[],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Expr(value=Call(func=Attribute(value=Name(id='empty',
                                                       ctx=Load()),
                                            attr='difference_update',
                                            ctx=Load()),
                             args=[Call(func=Name(id='set',
                                                  ctx=Load()),
                                        args=[List(elts=[],
                                                   ctx=Load())],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Compare(left=Name(id='empty',
                                             ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Name(id='s1',
                                                     ctx=Load())])],
                   nl=True),
             Expr(value=Call(func=Attribute(value=Name(id='empty',
                                                       ctx=Load()),
                                            attr='difference_update',
                                            ctx=Load()),
                             args=[Name(id='s2',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Compare(left=Name(id='empty',
                                             ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Call(func=Name(id='set',
                                                               ctx=Load()),
                                                     args=[List(elts=[Num(n=0),
                                                                      Num(n=1),
                                                                      Num(n=2),
                                                                      Num(n=3)],
                                                                ctx=Load())],
                                                     keywords=[],
                                                     starargs=None,
                                                     kwargs=None)])],
                   nl=True),
             Assign(targets=[Name(id='empty',
                                  ctx=Store())],
                    value=Call(func=Attribute(value=Name(id='s',
                                                         ctx=Load()),
                                              attr='copy',
                                              ctx=Load()),
                               args=[],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Expr(value=Call(func=Attribute(value=Name(id='empty',
                                                       ctx=Load()),
                                            attr='difference_update',
                                            ctx=Load()),
                             args=[Name(id='s1',
                                        ctx=Load()),
                                   Name(id='s2',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Compare(left=Name(id='empty',
                                             ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Call(func=Name(id='set',
                                                               ctx=Load()),
                                                     args=[List(elts=[],
                                                                ctx=Load())],
                                                     keywords=[],
                                                     starargs=None,
                                                     kwargs=None)])],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\nsymmetric_difference_update(other)')],
                   nl=True),
             Assign(targets=[Name(id='empty',
                                  ctx=Store())],
                    value=Call(func=Name(id='set',
                                         ctx=Load()),
                               args=[List(elts=[],
                                          ctx=Load())],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Expr(value=Call(func=Attribute(value=Name(id='empty',
                                                       ctx=Load()),
                                            attr='symmetric_difference_update',
                                            ctx=Load()),
                             args=[Name(id='s1',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Compare(left=Name(id='empty',
                                             ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Name(id='s1',
                                                     ctx=Load())])],
                   nl=True),
             Expr(value=Call(func=Attribute(value=Name(id='empty',
                                                       ctx=Load()),
                                            attr='symmetric_difference_update',
                                            ctx=Load()),
                             args=[Call(func=Name(id='set',
                                                  ctx=Load()),
                                        args=[List(elts=[],
                                                   ctx=Load())],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Compare(left=Name(id='empty',
                                             ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Name(id='s1',
                                                     ctx=Load())])],
                   nl=True),
             Expr(value=Call(func=Attribute(value=Name(id='empty',
                                                       ctx=Load()),
                                            attr='symmetric_difference_update',
                                            ctx=Load()),
                             args=[Name(id='s2',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Compare(left=Name(id='empty',
                                             ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Call(func=Name(id='set',
                                                               ctx=Load()),
                                                     args=[List(elts=[Num(n=0),
                                                                      Num(n=1),
                                                                      Num(n=2),
                                                                      Num(n=3),
                                                                      Num(n=5),
                                                                      Num(n=6),
                                                                      Num(n=7),
                                                                      Num(n=8)],
                                                                ctx=Load())],
                                                     keywords=[],
                                                     starargs=None,
                                                     kwargs=None)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Attribute(value=Name(id='s',
                                                                       ctx=Load()),
                                                            attr='symmetric_difference',
                                                            ctx=Load()),
                                             args=[Name(id='empty',
                                                        ctx=Load())],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Call(func=Name(id='set',
                                                               ctx=Load()),
                                                     args=[List(elts=[Num(n=4)],
                                                                ctx=Load())],
                                                     keywords=[],
                                                     starargs=None,
                                                     kwargs=None)])],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\nadd(elem)')],
                   nl=True),
             Assign(targets=[Name(id='empty',
                                  ctx=Store())],
                    value=Call(func=Name(id='set',
                                         ctx=Load()),
                               args=[List(elts=[],
                                          ctx=Load())],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Expr(value=Call(func=Attribute(value=Name(id='empty',
                                                       ctx=Load()),
                                            attr='add',
                                            ctx=Load()),
                             args=[Num(n=1)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Compare(left=Name(id='empty',
                                             ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Call(func=Name(id='set',
                                                               ctx=Load()),
                                                     args=[List(elts=[Num(n=1)],
                                                                ctx=Load())],
                                                     keywords=[],
                                                     starargs=None,
                                                     kwargs=None)])],
                   nl=True),
             Expr(value=Call(func=Attribute(value=Name(id='s1',
                                                       ctx=Load()),
                                            attr='add',
                                            ctx=Load()),
                             args=[Num(n=5)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Compare(left=Name(id='s1',
                                             ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Call(func=Name(id='set',
                                                               ctx=Load()),
                                                     args=[List(elts=[Num(n=0),
                                                                      Num(n=1),
                                                                      Num(n=2),
                                                                      Num(n=3),
                                                                      Num(n=4),
                                                                      Num(n=5)],
                                                                ctx=Load())],
                                                     keywords=[],
                                                     starargs=None,
                                                     kwargs=None)])],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\nremove(elem)')],
                   nl=True),
             Expr(value=Call(func=Attribute(value=Name(id='empty',
                                                       ctx=Load()),
                                            attr='remove',
                                            ctx=Load()),
                             args=[Num(n=1)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Compare(left=Name(id='empty',
                                             ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Call(func=Name(id='set',
                                                               ctx=Load()),
                                                     args=[List(elts=[],
                                                                ctx=Load())],
                                                     keywords=[],
                                                     starargs=None,
                                                     kwargs=None)])],
                   nl=True),
             Expr(value=Call(func=Attribute(value=Name(id='s1',
                                                       ctx=Load()),
                                            attr='remove',
                                            ctx=Load()),
                             args=[Num(n=5)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Compare(left=Name(id='s1',
                                             ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Call(func=Name(id='set',
                                                               ctx=Load()),
                                                     args=[List(elts=[Num(n=0),
                                                                      Num(n=1),
                                                                      Num(n=2),
                                                                      Num(n=3),
                                                                      Num(n=4)],
                                                                ctx=Load())],
                                                     keywords=[],
                                                     starargs=None,
                                                     kwargs=None)])],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\ndiscard(elem)')],
                   nl=True),
             Expr(value=Call(func=Attribute(value=Name(id='empty',
                                                       ctx=Load()),
                                            attr='discard',
                                            ctx=Load()),
                             args=[Num(n=500)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Compare(left=Name(id='empty',
                                             ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Call(func=Name(id='set',
                                                               ctx=Load()),
                                                     args=[List(elts=[],
                                                                ctx=Load())],
                                                     keywords=[],
                                                     starargs=None,
                                                     kwargs=None)])],
                   nl=True),
             Expr(value=Call(func=Attribute(value=Name(id='s1',
                                                       ctx=Load()),
                                            attr='discard',
                                            ctx=Load()),
                             args=[Num(n=4)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Compare(left=Name(id='s1',
                                             ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Call(func=Name(id='set',
                                                               ctx=Load()),
                                                     args=[List(elts=[Num(n=0),
                                                                      Num(n=1),
                                                                      Num(n=2),
                                                                      Num(n=3)],
                                                                ctx=Load())],
                                                     keywords=[],
                                                     starargs=None,
                                                     kwargs=None)])],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\npop()')],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Attribute(value=Name(id='s1',
                                                                       ctx=Load()),
                                                            attr='pop',
                                                            ctx=Load()),
                                             args=[],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[In()],
                                   comparators=[Call(func=Name(id='set',
                                                               ctx=Load()),
                                                     args=[List(elts=[Num(n=0),
                                                                      Num(n=1),
                                                                      Num(n=2),
                                                                      Num(n=3)],
                                                                ctx=Load())],
                                                     keywords=[],
                                                     starargs=None,
                                                     kwargs=None)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Attribute(value=Name(id='s1',
                                                                       ctx=Load()),
                                                            attr='pop',
                                                            ctx=Load()),
                                             args=[],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[In()],
                                   comparators=[Call(func=Name(id='set',
                                                               ctx=Load()),
                                                     args=[List(elts=[Num(n=0),
                                                                      Num(n=1),
                                                                      Num(n=2),
                                                                      Num(n=3)],
                                                                ctx=Load())],
                                                     keywords=[],
                                                     starargs=None,
                                                     kwargs=None)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Attribute(value=Name(id='s1',
                                                                       ctx=Load()),
                                                            attr='pop',
                                                            ctx=Load()),
                                             args=[],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[In()],
                                   comparators=[Call(func=Name(id='set',
                                                               ctx=Load()),
                                                     args=[List(elts=[Num(n=0),
                                                                      Num(n=1),
                                                                      Num(n=2),
                                                                      Num(n=3)],
                                                                ctx=Load())],
                                                     keywords=[],
                                                     starargs=None,
                                                     kwargs=None)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Attribute(value=Name(id='s1',
                                                                       ctx=Load()),
                                                            attr='pop',
                                                            ctx=Load()),
                                             args=[],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[In()],
                                   comparators=[Call(func=Name(id='set',
                                                               ctx=Load()),
                                                     args=[List(elts=[Num(n=0),
                                                                      Num(n=1),
                                                                      Num(n=2),
                                                                      Num(n=3)],
                                                                ctx=Load())],
                                                     keywords=[],
                                                     starargs=None,
                                                     kwargs=None)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Name(id='len',
                                                       ctx=Load()),
                                             args=[Name(id='s1',
                                                        ctx=Load())],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Num(n=0)])],
                   nl=True)])
