Module(body=[Assign(targets=[Name(id='d1',
                                  ctx=Store())],
                    value=Dict(keys=[Num(n=1),
                                     Num(n=3)],
                               values=[Num(n=2),
                                       Num(n=4)])),
             Assign(targets=[Name(id='d2',
                                  ctx=Store())],
                    value=Dict(keys=[],
                               values=[])),
             Assign(targets=[Subscript(value=Name(id='d2',
                                                  ctx=Load()),
                                       slice=Index(value=Num(n=1)),
                                       ctx=Store())],
                    value=Num(n=2)),
             Assign(targets=[Subscript(value=Name(id='d2',
                                                  ctx=Load()),
                                       slice=Index(value=Num(n=3)),
                                       ctx=Store())],
                    value=Num(n=4)),
             Print(dest=None,
                   values=[Subscript(value=Name(id='d1',
                                                ctx=Load()),
                                     slice=Index(value=Num(n=1)),
                                     ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Subscript(value=Name(id='d2',
                                                ctx=Load()),
                                     slice=Index(value=Num(n=3)),
                                     ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Num(n=1),
                                   ops=[In()],
                                   comparators=[Name(id='d1',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Num(n=2),
                                   ops=[In()],
                                   comparators=[Name(id='d1',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='d1',
                                             ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Name(id='d2',
                                                     ctx=Load())])],
                   nl=True),
             Delete(targets=[Subscript(value=Name(id='d1',
                                                  ctx=Load()),
                                       slice=Index(value=Num(n=3)),
                                       ctx=Del())]),
             Print(dest=None,
                   values=[Compare(left=Name(id='d1',
                                             ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Name(id='d2',
                                                     ctx=Load())])],
                   nl=True)])
