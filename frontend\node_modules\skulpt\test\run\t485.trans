Module(body=[TryExcept(body=[Assert(test=Compare(left=Num(n=1),
                                                 ops=[Gt()],
                                                 comparators=[Num(n=10)]),
                                    msg=None)],
                       handlers=[ExceptHandler(type=Name(id='AssertionError',
                                                         ctx=Load()),
                                               name=None,
                                               body=[Print(dest=None,
                                                           values=[Str(s='Caught AssertionError')],
                                                           nl=True)]),
                                 Except<PERSON>andler(type=None,
                                               name=None,
                                               body=[Print(dest=None,
                                                           values=[Str(s='Did not catch AssertionError')],
                                                           nl=True)])],
                       orelse=[]),
             TryExcept(body=[Print(dest=None,
                                   values=[Attribute(value=Name(id='None',
                                                                ctx=Load()),
                                                     attr='notAnAttribute',
                                                     ctx=Load())],
                                   nl=True)],
                       handlers=[ExceptHandler(type=Name(id='AttributeError',
                                                         ctx=Load()),
                                               name=None,
                                               body=[Print(dest=None,
                                                           values=[Str(s='Caught AttributeError')],
                                                           nl=True)]),
                                 ExceptHandler(type=None,
                                               name=None,
                                               body=[Print(dest=None,
                                                           values=[Str(s='Did not catch AttributeError')],
                                                           nl=True)])],
                       orelse=[]),
             TryExcept(body=[Import(names=[alias(name='notAModule',
                                                 asname=None)])],
                       handlers=[ExceptHandler(type=Name(id='ImportError',
                                                         ctx=Load()),
                                               name=None,
                                               body=[Print(dest=None,
                                                           values=[Str(s='Caught ImportError')],
                                                           nl=True)]),
                                 ExceptHandler(type=None,
                                               name=None,
                                               body=[Print(dest=None,
                                                           values=[Str(s='Did not catch ImportError')],
                                                           nl=True)])],
                       orelse=[]),
             TryExcept(body=[Print(dest=None,
                                   values=[Subscript(value=List(elts=[Num(n=0),
                                                                      Num(n=1),
                                                                      Num(n=2),
                                                                      Num(n=3),
                                                                      Num(n=4)],
                                                                ctx=Load()),
                                                     slice=Index(value=Num(n=5)),
                                                     ctx=Load())],
                                   nl=True)],
                       handlers=[ExceptHandler(type=Name(id='IndexError',
                                                         ctx=Load()),
                                               name=None,
                                               body=[Print(dest=None,
                                                           values=[Str(s='Caught IndexError')],
                                                           nl=True)]),
                                 ExceptHandler(type=None,
                                               name=None,
                                               body=[Print(dest=None,
                                                           values=[Str(s='Did not catch IndexError')],
                                                           nl=True)])],
                       orelse=[]),
             TryExcept(body=[Print(dest=None,
                                   values=[Subscript(value=Dict(keys=[Num(n=1),
                                                                      Num(n=3)],
                                                                values=[Num(n=2),
                                                                        Num(n=4)]),
                                                     slice=Index(value=Num(n=5)),
                                                     ctx=Load())],
                                   nl=True)],
                       handlers=[ExceptHandler(type=Name(id='KeyError',
                                                         ctx=Load()),
                                               name=None,
                                               body=[Print(dest=None,
                                                           values=[Str(s='Caught KeyError')],
                                                           nl=True)]),
                                 ExceptHandler(type=None,
                                               name=None,
                                               body=[Print(dest=None,
                                                           values=[Str(s='Did not catch KeyError')],
                                                           nl=True)])],
                       orelse=[]),
             TryExcept(body=[Print(dest=None,
                                   values=[Name(id='x',
                                                ctx=Load())],
                                   nl=True)],
                       handlers=[ExceptHandler(type=Name(id='NameError',
                                                         ctx=Load()),
                                               name=None,
                                               body=[Print(dest=None,
                                                           values=[Str(s='Caught NameError')],
                                                           nl=True)]),
                                 ExceptHandler(type=None,
                                               name=None,
                                               body=[Print(dest=None,
                                                           values=[Str(s='Did not catch NameError')],
                                                           nl=True)])],
                       orelse=[]),
             TryExcept(body=[Print(dest=None,
                                   values=[BinOp(left=Num(n=1e-64),
                                                 op=Pow(),
                                                 right=Num(n=-30))],
                                   nl=True)],
                       handlers=[ExceptHandler(type=Name(id='OverflowError',
                                                         ctx=Load()),
                                               name=None,
                                               body=[Print(dest=None,
                                                           values=[Str(s='Caught OverflowError')],
                                                           nl=True)]),
                                 ExceptHandler(type=None,
                                               name=None,
                                               body=[Print(dest=None,
                                                           values=[Str(s='Did not catch OverflowError')],
                                                           nl=True)])],
                       orelse=[]),
             TryExcept(body=[Expr(value=BinOp(left=Str(s='10'),
                                              op=Div(),
                                              right=Str(s='1')))],
                       handlers=[ExceptHandler(type=Name(id='TypeError',
                                                         ctx=Load()),
                                               name=None,
                                               body=[Print(dest=None,
                                                           values=[Str(s='Caught TypeError')],
                                                           nl=True)]),
                                 ExceptHandler(type=None,
                                               name=None,
                                               body=[Print(dest=None,
                                                           values=[Str(s='Did not catch TypeError')],
                                                           nl=True)])],
                       orelse=[]),
             TryExcept(body=[Print(dest=None,
                                   values=[Call(func=Attribute(value=Str(s='hello'),
                                                               attr='index',
                                                               ctx=Load()),
                                                args=[Str(s='S')],
                                                keywords=[],
                                                starargs=None,
                                                kwargs=None)],
                                   nl=True)],
                       handlers=[ExceptHandler(type=Name(id='ValueError',
                                                         ctx=Load()),
                                               name=None,
                                               body=[Print(dest=None,
                                                           values=[Str(s='Caught ValueError')],
                                                           nl=True)]),
                                 ExceptHandler(type=None,
                                               name=None,
                                               body=[Print(dest=None,
                                                           values=[Str(s='Did not catch ValueError')],
                                                           nl=True)])],
                       orelse=[]),
             TryExcept(body=[Print(dest=None,
                                   values=[BinOp(left=Num(n=1),
                                                 op=Div(),
                                                 right=Num(n=0))],
                                   nl=True)],
                       handlers=[ExceptHandler(type=Name(id='ZeroDivisionError',
                                                         ctx=Load()),
                                               name=None,
                                               body=[Print(dest=None,
                                                           values=[Str(s='Caught ZeroDivisionError')],
                                                           nl=True)]),
                                 ExceptHandler(type=None,
                                               name=None,
                                               body=[Print(dest=None,
                                                           values=[Str(s='Did not catch ZeroDivisionError')],
                                                           nl=True)])],
                       orelse=[])])
