Module(body=[ClassDef(name='MyClass',
                      bases=[],
                      body=[FunctionDef(name='my_method',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param()),
                                                             Name(id='mandatory_arg',
                                                                  ctx=Param()),
                                                             Name(id='x',
                                                                  ctx=Param()),
                                                             Name(id='y',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg='more_args',
                                                       defaults=[Num(n=0),
                                                                 Num(n=0)]),
                                        body=[Print(dest=None,
                                                    values=[BinOp(left=Str(s='Hello! x = '),
                                                                  op=Add(),
                                                                  right=Call(func=Name(id='str',
                                                                                       ctx=Load()),
                                                                             args=[Name(id='x',
                                                                                        ctx=Load())],
                                                                             keywords=[],
                                                                             starargs=None,
                                                                             kwargs=None))],
                                                    nl=True),
                                              Print(dest=None,
                                                    values=[BinOp(left=Str(s='Hello! bla = '),
                                                                  op=Add(),
                                                                  right=Call(func=Name(id='str',
                                                                                       ctx=Load()),
                                                                             args=[Subscript(value=Name(id='more_args',
                                                                                                        ctx=Load()),
                                                                                             slice=Index(value=Str(s='bla')),
                                                                                             ctx=Load())],
                                                                             keywords=[],
                                                                             starargs=None,
                                                                             kwargs=None))],
                                                    nl=True)],
                                        decorator_list=[]),
                            FunctionDef(name='my_method2',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Expr(value=Call(func=Attribute(value=Name(id='self',
                                                                                        ctx=Load()),
                                                                             attr='my_method',
                                                                             ctx=Load()),
                                                              args=[Str(s='hi')],
                                                              keywords=[keyword(arg='y',
                                                                                value=Num(n=2)),
                                                                        keyword(arg='bla',
                                                                                value=Str(s='from method2'))],
                                                              starargs=None,
                                                              kwargs=None))],
                                        decorator_list=[])],
                      decorator_list=[]),
             Assign(targets=[Name(id='k',
                                  ctx=Store())],
                    value=Call(func=Name(id='MyClass',
                                         ctx=Load()),
                               args=[],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Expr(value=Call(func=Attribute(value=Name(id='k',
                                                       ctx=Load()),
                                            attr='my_method',
                                            ctx=Load()),
                             args=[Str(s='test')],
                             keywords=[keyword(arg='x',
                                               value=Num(n=5)),
                                       keyword(arg='bla',
                                               value=Str(s='seven'))],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Attribute(value=Name(id='k',
                                                       ctx=Load()),
                                            attr='my_method2',
                                            ctx=Load()),
                             args=[],
                             keywords=[],
                             starargs=None,
                             kwargs=None))])
