Module(body=[Import(names=[alias(name='operator',
                                 asname=None)]),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='lt',
                                               ctx=Load()),
                                args=[Num(n=1),
                                      Num(n=2)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='lt',
                                               ctx=Load()),
                                args=[Num(n=2),
                                      Num(n=1)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='le',
                                               ctx=Load()),
                                args=[Num(n=1),
                                      Num(n=2)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='le',
                                               ctx=Load()),
                                args=[Num(n=2),
                                      Num(n=1)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='le',
                                               ctx=Load()),
                                args=[Num(n=2),
                                      Num(n=2)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='eq',
                                               ctx=Load()),
                                args=[Num(n=2),
                                      Num(n=2)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='eq',
                                               ctx=Load()),
                                args=[Num(n=3),
                                      Num(n=2)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='ne',
                                               ctx=Load()),
                                args=[Num(n=2),
                                      Num(n=3)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='ne',
                                               ctx=Load()),
                                args=[Num(n=2),
                                      Num(n=2)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='ge',
                                               ctx=Load()),
                                args=[Num(n=2),
                                      Num(n=1)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='ge',
                                               ctx=Load()),
                                args=[Num(n=1),
                                      Num(n=2)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='ge',
                                               ctx=Load()),
                                args=[Num(n=2),
                                      Num(n=2)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='gt',
                                               ctx=Load()),
                                args=[Num(n=2),
                                      Num(n=1)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='gt',
                                               ctx=Load()),
                                args=[Num(n=1),
                                      Num(n=2)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='truth',
                                               ctx=Load()),
                                args=[Name(id='True',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='truth',
                                               ctx=Load()),
                                args=[Name(id='False',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='truth',
                                               ctx=Load()),
                                args=[Num(n=1)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='truth',
                                               ctx=Load()),
                                args=[Num(n=0)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='is_',
                                               ctx=Load()),
                                args=[Str(s='hello'),
                                      Str(s='hello')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='is_',
                                               ctx=Load()),
                                args=[Str(s='hello'),
                                      Str(s='goodbye')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='is_',
                                               ctx=Load()),
                                args=[Num(n=1),
                                      Num(n=1)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='is_',
                                               ctx=Load()),
                                args=[Num(n=2),
                                      Num(n=1)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='is_not',
                                               ctx=Load()),
                                args=[Str(s='hello'),
                                      Str(s='goodbye')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='is_not',
                                               ctx=Load()),
                                args=[Str(s='hello'),
                                      Str(s='hello')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='is_not',
                                               ctx=Load()),
                                args=[Num(n=1),
                                      Num(n=2)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='is_not',
                                               ctx=Load()),
                                args=[Num(n=1),
                                      Num(n=1)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='abs',
                                               ctx=Load()),
                                args=[Num(n=5)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='abs',
                                               ctx=Load()),
                                args=[Num(n=-5)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='abs',
                                               ctx=Load()),
                                args=[Num(n=1.1)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='abs',
                                               ctx=Load()),
                                args=[Num(n=-1.1)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='add',
                                               ctx=Load()),
                                args=[Num(n=1),
                                      Num(n=2)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='add',
                                               ctx=Load()),
                                args=[Num(n=-4),
                                      Num(n=2)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='add',
                                               ctx=Load()),
                                args=[Str(s='he'),
                                      Str(s='llo')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='and_',
                                               ctx=Load()),
                                args=[Num(n=2),
                                      Num(n=3)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='and_',
                                               ctx=Load()),
                                args=[Num(n=5),
                                      Num(n=3)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='and_',
                                               ctx=Load()),
                                args=[Num(n=-4),
                                      Num(n=3)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='div',
                                               ctx=Load()),
                                args=[Num(n=10),
                                      Num(n=5)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='div',
                                               ctx=Load()),
                                args=[Num(n=5),
                                      Num(n=2)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='div',
                                               ctx=Load()),
                                args=[Num(n=2.2),
                                      Num(n=2)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='div',
                                               ctx=Load()),
                                args=[Num(n=-5.0),
                                      Num(n=2)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='floordiv',
                                               ctx=Load()),
                                args=[Num(n=10),
                                      Num(n=5)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='floordiv',
                                               ctx=Load()),
                                args=[Num(n=5),
                                      Num(n=2)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='floordiv',
                                               ctx=Load()),
                                args=[Num(n=2.2),
                                      Num(n=2)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='floordiv',
                                               ctx=Load()),
                                args=[Num(n=-5.0),
                                      Num(n=2)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='lshift',
                                               ctx=Load()),
                                args=[Num(n=5),
                                      Num(n=2)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='lshift',
                                               ctx=Load()),
                                args=[Num(n=-5),
                                      Num(n=3)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='mod',
                                               ctx=Load()),
                                args=[Num(n=10),
                                      Num(n=5)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='mod',
                                               ctx=Load()),
                                args=[Num(n=10),
                                      Num(n=3)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='mod',
                                               ctx=Load()),
                                args=[Num(n=15),
                                      Num(n=4)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='mul',
                                               ctx=Load()),
                                args=[Num(n=2),
                                      Num(n=1)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='mul',
                                               ctx=Load()),
                                args=[Num(n=-2),
                                      Num(n=1)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='mul',
                                               ctx=Load()),
                                args=[Num(n=2),
                                      Num(n=-1)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='mul',
                                               ctx=Load()),
                                args=[Num(n=10),
                                      Num(n=20)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='neg',
                                               ctx=Load()),
                                args=[Num(n=-5)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='neg',
                                               ctx=Load()),
                                args=[Num(n=5)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='neg',
                                               ctx=Load()),
                                args=[Name(id='True',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='neg',
                                               ctx=Load()),
                                args=[Name(id='False',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='or_',
                                               ctx=Load()),
                                args=[Num(n=1),
                                      Num(n=2)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='or_',
                                               ctx=Load()),
                                args=[Num(n=4),
                                      Num(n=3)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='or_',
                                               ctx=Load()),
                                args=[Num(n=5),
                                      Num(n=2)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='pos',
                                               ctx=Load()),
                                args=[Num(n=5)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='pos',
                                               ctx=Load()),
                                args=[Num(n=-5)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='pos',
                                               ctx=Load()),
                                args=[Name(id='True',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='pos',
                                               ctx=Load()),
                                args=[Name(id='False',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='pow',
                                               ctx=Load()),
                                args=[Num(n=2),
                                      Num(n=2)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='pow',
                                               ctx=Load()),
                                args=[Num(n=5),
                                      Num(n=3)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='rshift',
                                               ctx=Load()),
                                args=[Num(n=5),
                                      Num(n=2)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='rshift',
                                               ctx=Load()),
                                args=[Num(n=-5),
                                      Num(n=3)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='sub',
                                               ctx=Load()),
                                args=[Num(n=4),
                                      Num(n=2)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='sub',
                                               ctx=Load()),
                                args=[Num(n=2),
                                      Num(n=4)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='sub',
                                               ctx=Load()),
                                args=[Num(n=-4),
                                      Num(n=2)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='xor',
                                               ctx=Load()),
                                args=[Num(n=4),
                                      Num(n=2)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='xor',
                                               ctx=Load()),
                                args=[Num(n=8),
                                      Num(n=5)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='concat',
                                               ctx=Load()),
                                args=[Str(s='he'),
                                      Str(s='llo')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='concat',
                                               ctx=Load()),
                                args=[List(elts=[Num(n=1),
                                                 Num(n=2),
                                                 Num(n=3),
                                                 Num(n=4)],
                                           ctx=Load()),
                                      List(elts=[Num(n=5),
                                                 Num(n=6),
                                                 Num(n=7)],
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='concat',
                                               ctx=Load()),
                                args=[Tuple(elts=[Num(n=1),
                                                  Num(n=2)],
                                            ctx=Load()),
                                      Tuple(elts=[Num(n=3),
                                                  Num(n=4)],
                                            ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Assign(targets=[Name(id='l',
                                  ctx=Store())],
                    value=List(elts=[Num(n=1),
                                     Num(n=2),
                                     Num(n=3),
                                     Num(n=4),
                                     Num(n=5),
                                     Num(n=6),
                                     Num(n=7),
                                     Num(n=8),
                                     Num(n=9),
                                     Num(n=9),
                                     Num(n=9),
                                     Num(n=9)],
                               ctx=Load())),
             Assign(targets=[Name(id='s',
                                  ctx=Store())],
                    value=Str(s='hello world')),
             Assign(targets=[Name(id='t',
                                  ctx=Store())],
                    value=Tuple(elts=[Str(s='a'),
                                      Str(s='b'),
                                      Str(s='c')],
                                ctx=Load())),
             Assign(targets=[Name(id='d',
                                  ctx=Store())],
                    value=Dict(keys=[Num(n=1),
                                     Num(n=2),
                                     Num(n=3),
                                     Num(n=4),
                                     Num(n=5)],
                               values=[Num(n=1),
                                       Num(n=2),
                                       Num(n=3),
                                       Num(n=4),
                                       Num(n=5)])),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='contains',
                                               ctx=Load()),
                                args=[Name(id='l',
                                           ctx=Load()),
                                      Num(n=2)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='contains',
                                               ctx=Load()),
                                args=[Name(id='l',
                                           ctx=Load()),
                                      Num(n=30)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='contains',
                                               ctx=Load()),
                                args=[Name(id='s',
                                           ctx=Load()),
                                      Str(s='ll')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='contains',
                                               ctx=Load()),
                                args=[Name(id='s',
                                           ctx=Load()),
                                      Str(s='z')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='contains',
                                               ctx=Load()),
                                args=[Name(id='t',
                                           ctx=Load()),
                                      Str(s='a')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='contains',
                                               ctx=Load()),
                                args=[Name(id='t',
                                           ctx=Load()),
                                      Num(n=2)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='contains',
                                               ctx=Load()),
                                args=[Name(id='d',
                                           ctx=Load()),
                                      Num(n=3)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='contains',
                                               ctx=Load()),
                                args=[Name(id='d',
                                           ctx=Load()),
                                      Num(n=0)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='countOf',
                                               ctx=Load()),
                                args=[Name(id='l',
                                           ctx=Load()),
                                      Num(n=9)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='countOf',
                                               ctx=Load()),
                                args=[Name(id='l',
                                           ctx=Load()),
                                      Num(n=30)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='countOf',
                                               ctx=Load()),
                                args=[Name(id='s',
                                           ctx=Load()),
                                      Str(s='l')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='countOf',
                                               ctx=Load()),
                                args=[Name(id='t',
                                           ctx=Load()),
                                      Str(s='a')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Expr(value=Call(func=Attribute(value=Name(id='operator',
                                                       ctx=Load()),
                                            attr='delitem',
                                            ctx=Load()),
                             args=[Name(id='l',
                                        ctx=Load()),
                                   Num(n=9)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Name(id='l',
                                ctx=Load())],
                   nl=True),
             Expr(value=Call(func=Attribute(value=Name(id='operator',
                                                       ctx=Load()),
                                            attr='delitem',
                                            ctx=Load()),
                             args=[Name(id='l',
                                        ctx=Load()),
                                   Num(n=0)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Name(id='l',
                                ctx=Load())],
                   nl=True),
             Assign(targets=[Name(id='l',
                                  ctx=Store())],
                    value=List(elts=[Num(n=1),
                                     Num(n=2),
                                     Num(n=3),
                                     Num(n=4),
                                     Num(n=5),
                                     Num(n=6),
                                     Num(n=7),
                                     Num(n=8),
                                     Num(n=9),
                                     Num(n=9),
                                     Num(n=9),
                                     Num(n=9)],
                               ctx=Load())),
             Assign(targets=[Name(id='s',
                                  ctx=Store())],
                    value=Str(s='hello world')),
             Assign(targets=[Name(id='t',
                                  ctx=Store())],
                    value=Tuple(elts=[Str(s='a'),
                                      Str(s='b'),
                                      Str(s='c')],
                                ctx=Load())),
             Assign(targets=[Name(id='d',
                                  ctx=Store())],
                    value=Dict(keys=[Num(n=1),
                                     Num(n=2),
                                     Num(n=3),
                                     Num(n=4),
                                     Num(n=5)],
                               values=[Num(n=1),
                                       Num(n=2),
                                       Num(n=3),
                                       Num(n=4),
                                       Num(n=5)])),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='getitem',
                                               ctx=Load()),
                                args=[Name(id='l',
                                           ctx=Load()),
                                      Num(n=2)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='getitem',
                                               ctx=Load()),
                                args=[Name(id='s',
                                           ctx=Load()),
                                      Num(n=0)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='getitem',
                                               ctx=Load()),
                                args=[Name(id='t',
                                           ctx=Load()),
                                      Num(n=1)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='getitem',
                                               ctx=Load()),
                                args=[Name(id='d',
                                           ctx=Load()),
                                      Num(n=4)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='indexOf',
                                               ctx=Load()),
                                args=[Name(id='l',
                                           ctx=Load()),
                                      Num(n=5)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='indexOf',
                                               ctx=Load()),
                                args=[Name(id='s',
                                           ctx=Load()),
                                      Str(s='l')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='operator',
                                                          ctx=Load()),
                                               attr='indexOf',
                                               ctx=Load()),
                                args=[Name(id='t',
                                           ctx=Load()),
                                      Str(s='a')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Expr(value=Call(func=Attribute(value=Name(id='operator',
                                                       ctx=Load()),
                                            attr='setitem',
                                            ctx=Load()),
                             args=[Name(id='l',
                                        ctx=Load()),
                                   Num(n=0),
                                   Num(n=10)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Name(id='l',
                                ctx=Load())],
                   nl=True),
             Expr(value=Call(func=Attribute(value=Name(id='operator',
                                                       ctx=Load()),
                                            attr='setitem',
                                            ctx=Load()),
                             args=[Name(id='d',
                                        ctx=Load()),
                                   Num(n=1),
                                   Num(n=10)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Name(id='d',
                                ctx=Load())],
                   nl=True),
             Expr(value=Call(func=Attribute(value=Name(id='operator',
                                                       ctx=Load()),
                                            attr='setitem',
                                            ctx=Load()),
                             args=[Name(id='d',
                                        ctx=Load()),
                                   Num(n=6),
                                   Num(n=6)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Name(id='d',
                                ctx=Load())],
                   nl=True)])
