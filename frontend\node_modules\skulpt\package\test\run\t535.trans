Module(body=[ImportFrom(module='math',
                        names=[alias(name='*',
                                     asname=None)],
                        level=0),
             FunctionDef(name='differentiate',
                         args=arguments(args=[Name(id='f',
                                                   ctx=Param()),
                                              Name(id='method',
                                                   ctx=Param()),
                                              Name(id='h',
                                                   ctx=Param())],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[Num(n=1e-05)]),
                         body=[If(test=Compare(left=Name(id='method',
                                                         ctx=Load()),
                                               ops=[Eq()],
                                               comparators=[Str(s='Forward1')]),
                                  body=[FunctionDef(name='Forward1',
                                                    args=arguments(args=[Name(id='x',
                                                                              ctx=Param())],
                                                                   vararg=None,
                                                                   kwarg=None,
                                                                   defaults=[]),
                                                    body=[Return(value=BinOp(left=BinOp(left=Call(func=Name(id='f',
                                                                                                            ctx=Load()),
                                                                                                  args=[BinOp(left=Name(id='x',
                                                                                                                        ctx=Load()),
                                                                                                              op=Add(),
                                                                                                              right=Name(id='h',
                                                                                                                         ctx=Load()))],
                                                                                                  keywords=[],
                                                                                                  starargs=None,
                                                                                                  kwargs=None),
                                                                                        op=Sub(),
                                                                                        right=Call(func=Name(id='f',
                                                                                                             ctx=Load()),
                                                                                                   args=[Name(id='x',
                                                                                                              ctx=Load())],
                                                                                                   keywords=[],
                                                                                                   starargs=None,
                                                                                                   kwargs=None)),
                                                                             op=Div(),
                                                                             right=Name(id='h',
                                                                                        ctx=Load())))],
                                                    decorator_list=[]),
                                        Return(value=Name(id='Forward1',
                                                          ctx=Load()))],
                                  orelse=[If(test=Compare(left=Name(id='method',
                                                                    ctx=Load()),
                                                          ops=[Eq()],
                                                          comparators=[Str(s='Backward1')]),
                                             body=[FunctionDef(name='Backward1',
                                                               args=arguments(args=[Name(id='x',
                                                                                         ctx=Param())],
                                                                              vararg=None,
                                                                              kwarg=None,
                                                                              defaults=[]),
                                                               body=[Return(value=BinOp(left=BinOp(left=Call(func=Name(id='f',
                                                                                                                       ctx=Load()),
                                                                                                             args=[Name(id='x',
                                                                                                                        ctx=Load())],
                                                                                                             keywords=[],
                                                                                                             starargs=None,
                                                                                                             kwargs=None),
                                                                                                   op=Sub(),
                                                                                                   right=Call(func=Name(id='f',
                                                                                                                        ctx=Load()),
                                                                                                              args=[BinOp(left=Name(id='x',
                                                                                                                                    ctx=Load()),
                                                                                                                          op=Sub(),
                                                                                                                          right=Name(id='h',
                                                                                                                                     ctx=Load()))],
                                                                                                              keywords=[],
                                                                                                              starargs=None,
                                                                                                              kwargs=None)),
                                                                                        op=Div(),
                                                                                        right=Name(id='h',
                                                                                                   ctx=Load())))],
                                                               decorator_list=[]),
                                                   Return(value=Name(id='Backward1',
                                                                     ctx=Load()))],
                                             orelse=[])])],
                         decorator_list=[]),
             Assign(targets=[Name(id='mycos',
                                  ctx=Store())],
                    value=Call(func=Name(id='differentiate',
                                         ctx=Load()),
                               args=[Name(id='sin',
                                          ctx=Load()),
                                     Str(s='Forward1')],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Assign(targets=[Name(id='mysin',
                                  ctx=Store())],
                    value=Call(func=Name(id='differentiate',
                                         ctx=Load()),
                               args=[Name(id='mycos',
                                          ctx=Load()),
                                     Str(s='Backward1'),
                                     Num(n=1e-06)],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Assign(targets=[Name(id='x',
                                  ctx=Store())],
                    value=Name(id='pi',
                               ctx=Load())),
             Print(dest=None,
                   values=[BinOp(left=Str(s='%.10f %.10f %.5f %.10f'),
                                 op=Mod(),
                                 right=Tuple(elts=[Call(func=Name(id='mycos',
                                                                  ctx=Load()),
                                                        args=[Name(id='x',
                                                                   ctx=Load())],
                                                        keywords=[],
                                                        starargs=None,
                                                        kwargs=None),
                                                   Call(func=Name(id='cos',
                                                                  ctx=Load()),
                                                        args=[Name(id='x',
                                                                   ctx=Load())],
                                                        keywords=[],
                                                        starargs=None,
                                                        kwargs=None),
                                                   Call(func=Name(id='mysin',
                                                                  ctx=Load()),
                                                        args=[Name(id='x',
                                                                   ctx=Load())],
                                                        keywords=[],
                                                        starargs=None,
                                                        kwargs=None),
                                                   UnaryOp(op=USub(),
                                                           operand=Call(func=Name(id='sin',
                                                                                  ctx=Load()),
                                                                        args=[Name(id='x',
                                                                                   ctx=Load())],
                                                                        keywords=[],
                                                                        starargs=None,
                                                                        kwargs=None))],
                                             ctx=Load()))],
                   nl=True)])
