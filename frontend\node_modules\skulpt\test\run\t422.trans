Module(body=[Assign(targets=[Name(id='bugs',
                                  ctx=Store())],
                    value=List(elts=[Num(n=-0.5),
                                     <PERSON>um(n=-0.025),
                                     <PERSON><PERSON>(n=-0.055),
                                     <PERSON><PERSON>(n=0.045),
                                     <PERSON><PERSON>(n=-0.0025),
                                     <PERSON><PERSON>(n=-0.0035),
                                     <PERSON><PERSON>(n=0.0045),
                                     <PERSON><PERSON>(n=0.0055),
                                     <PERSON><PERSON>(n=-250),
                                     <PERSON><PERSON>(n=-350),
                                     <PERSON><PERSON>(n=-450),
                                     <PERSON>um(n=-550)],
                               ctx=Load())),
             FunctionDef(name='helper',
                         args=arguments(args=[Name(id='iterable',
                                                   ctx=Param()),
                                              Name(id='expect',
                                                   ctx=Param()),
                                              Name(id='n',
                                                   ctx=Param())],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[Name(id='None',
                                                       ctx=Load())]),
                         body=[If(test=Name(id='n',
                                            ctx=Load()),
                                  body=[For(target=Name(id='i',
                                                        ctx=Store()),
                                            iter=Name(id='iterable',
                                                      ctx=Load()),
                                            body=[Assign(targets=[Name(id='r',
                                                                       ctx=Store())],
                                                         value=Call(func=Name(id='round',
                                                                              ctx=Load()),
                                                                    args=[Name(id='i',
                                                                               ctx=Load()),
                                                                          Name(id='n',
                                                                               ctx=Load())],
                                                                    keywords=[],
                                                                    starargs=None,
                                                                    kwargs=None)),
                                                  If(test=BoolOp(op=And(),
                                                                 values=[Compare(left=Call(func=Name(id='abs',
                                                                                                     ctx=Load()),
                                                                                           args=[BinOp(left=Name(id='r',
                                                                                                                 ctx=Load()),
                                                                                                       op=Sub(),
                                                                                                       right=Name(id='expect',
                                                                                                                  ctx=Load()))],
                                                                                           keywords=[],
                                                                                           starargs=None,
                                                                                           kwargs=None),
                                                                                 ops=[Gt()],
                                                                                 comparators=[BinOp(left=Num(n=1),
                                                                                                    op=Div(),
                                                                                                    right=BinOp(left=Num(n=10.0),
                                                                                                                op=Pow(),
                                                                                                                right=BinOp(left=Name(id='n',
                                                                                                                                      ctx=Load()),
                                                                                                                            op=Add(),
                                                                                                                            right=Num(n=1))))]),
                                                                         Compare(left=Name(id='i',
                                                                                           ctx=Load()),
                                                                                 ops=[NotIn()],
                                                                                 comparators=[Name(id='bugs',
                                                                                                   ctx=Load())])]),
                                                     body=[Print(dest=None,
                                                                 values=[Name(id='False',
                                                                              ctx=Load()),
                                                                         Name(id='i',
                                                                              ctx=Load()),
                                                                         Str(s='  expected: '),
                                                                         Name(id='expect',
                                                                              ctx=Load()),
                                                                         Str(s='  result: '),
                                                                         Name(id='r',
                                                                              ctx=Load()),
                                                                         Call(func=Name(id='abs',
                                                                                        ctx=Load()),
                                                                              args=[BinOp(left=Name(id='r',
                                                                                                    ctx=Load()),
                                                                                          op=Sub(),
                                                                                          right=Name(id='expect',
                                                                                                     ctx=Load()))],
                                                                              keywords=[],
                                                                              starargs=None,
                                                                              kwargs=None)],
                                                                 nl=True)],
                                                     orelse=[])],
                                            orelse=[])],
                                  orelse=[For(target=Name(id='i',
                                                          ctx=Store()),
                                              iter=Name(id='iterable',
                                                        ctx=Load()),
                                              body=[Assign(targets=[Name(id='r',
                                                                         ctx=Store())],
                                                           value=Call(func=Name(id='round',
                                                                                ctx=Load()),
                                                                      args=[Name(id='i',
                                                                                 ctx=Load())],
                                                                      keywords=[],
                                                                      starargs=None,
                                                                      kwargs=None)),
                                                    If(test=BoolOp(op=And(),
                                                                   values=[Compare(left=Call(func=Name(id='abs',
                                                                                                       ctx=Load()),
                                                                                             args=[BinOp(left=Name(id='r',
                                                                                                                   ctx=Load()),
                                                                                                         op=Sub(),
                                                                                                         right=Name(id='expect',
                                                                                                                    ctx=Load()))],
                                                                                             keywords=[],
                                                                                             starargs=None,
                                                                                             kwargs=None),
                                                                                   ops=[Gt()],
                                                                                   comparators=[Num(n=1e-06)]),
                                                                           Compare(left=Name(id='i',
                                                                                             ctx=Load()),
                                                                                   ops=[NotIn()],
                                                                                   comparators=[Name(id='bugs',
                                                                                                     ctx=Load())])]),
                                                       body=[Print(dest=None,
                                                                   values=[Name(id='False',
                                                                                ctx=Load()),
                                                                           Name(id='i',
                                                                                ctx=Load()),
                                                                           Str(s='  expected: '),
                                                                           Name(id='expect',
                                                                                ctx=Load()),
                                                                           Str(s='  result: '),
                                                                           Name(id='r',
                                                                                ctx=Load()),
                                                                           Call(func=Name(id='abs',
                                                                                          ctx=Load()),
                                                                                args=[BinOp(left=Name(id='r',
                                                                                                      ctx=Load()),
                                                                                            op=Sub(),
                                                                                            right=Name(id='expect',
                                                                                                       ctx=Load()))],
                                                                                keywords=[],
                                                                                starargs=None,
                                                                                kwargs=None)],
                                                                   nl=True)],
                                                       orelse=[])],
                                              orelse=[])])],
                         decorator_list=[]),
             Print(dest=None,
                   values=[Str(s='\n-1.4 to 1.4, no ndigit')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[ListComp(elt=BinOp(left=Name(id='x',
                                                                ctx=Load()),
                                                      op=Div(),
                                                      right=Num(n=10.0)),
                                            generators=[comprehension(target=Name(id='x',
                                                                                  ctx=Store()),
                                                                      iter=Call(func=Name(id='range',
                                                                                          ctx=Load()),
                                                                                args=[Num(n=-5),
                                                                                      Num(n=-15),
                                                                                      Num(n=-1)],
                                                                                keywords=[],
                                                                                starargs=None,
                                                                                kwargs=None),
                                                                      ifs=[])]),
                                   Num(n=-1)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[ListComp(elt=BinOp(left=Name(id='x',
                                                                ctx=Load()),
                                                      op=Div(),
                                                      right=Num(n=10.0)),
                                            generators=[comprehension(target=Name(id='x',
                                                                                  ctx=Store()),
                                                                      iter=Call(func=Name(id='range',
                                                                                          ctx=Load()),
                                                                                args=[Num(n=4),
                                                                                      Num(n=-5),
                                                                                      Num(n=-1)],
                                                                                keywords=[],
                                                                                starargs=None,
                                                                                kwargs=None),
                                                                      ifs=[])]),
                                   Num(n=0)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[ListComp(elt=BinOp(left=Name(id='x',
                                                                ctx=Load()),
                                                      op=Div(),
                                                      right=Num(n=10.0)),
                                            generators=[comprehension(target=Name(id='x',
                                                                                  ctx=Store()),
                                                                      iter=Call(func=Name(id='range',
                                                                                          ctx=Load()),
                                                                                args=[Num(n=5),
                                                                                      Num(n=15)],
                                                                                keywords=[],
                                                                                starargs=None,
                                                                                kwargs=None),
                                                                      ifs=[])]),
                                   Num(n=1)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Str(s='\n-1.49 to 1.49, no ndigit')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[ListComp(elt=BinOp(left=Name(id='x',
                                                                ctx=Load()),
                                                      op=Div(),
                                                      right=Num(n=100.0)),
                                            generators=[comprehension(target=Name(id='x',
                                                                                  ctx=Store()),
                                                                      iter=Call(func=Name(id='range',
                                                                                          ctx=Load()),
                                                                                args=[Num(n=-50),
                                                                                      Num(n=-150),
                                                                                      Num(n=-1)],
                                                                                keywords=[],
                                                                                starargs=None,
                                                                                kwargs=None),
                                                                      ifs=[])]),
                                   Num(n=-1)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[ListComp(elt=BinOp(left=Name(id='x',
                                                                ctx=Load()),
                                                      op=Div(),
                                                      right=Num(n=100.0)),
                                            generators=[comprehension(target=Name(id='x',
                                                                                  ctx=Store()),
                                                                      iter=Call(func=Name(id='range',
                                                                                          ctx=Load()),
                                                                                args=[Num(n=40),
                                                                                      Num(n=-50),
                                                                                      Num(n=-1)],
                                                                                keywords=[],
                                                                                starargs=None,
                                                                                kwargs=None),
                                                                      ifs=[])]),
                                   Num(n=0)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[ListComp(elt=BinOp(left=Name(id='x',
                                                                ctx=Load()),
                                                      op=Div(),
                                                      right=Num(n=100.0)),
                                            generators=[comprehension(target=Name(id='x',
                                                                                  ctx=Store()),
                                                                      iter=Call(func=Name(id='range',
                                                                                          ctx=Load()),
                                                                                args=[Num(n=50),
                                                                                      Num(n=150)],
                                                                                keywords=[],
                                                                                starargs=None,
                                                                                kwargs=None),
                                                                      ifs=[])]),
                                   Num(n=1)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Str(s='\n-0.064 to -0.025, ndigit=2')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[ListComp(elt=BinOp(left=Name(id='x',
                                                                ctx=Load()),
                                                      op=Div(),
                                                      right=Num(n=1000.0)),
                                            generators=[comprehension(target=Name(id='x',
                                                                                  ctx=Store()),
                                                                      iter=Call(func=Name(id='range',
                                                                                          ctx=Load()),
                                                                                args=[Num(n=-25),
                                                                                      Num(n=-35),
                                                                                      Num(n=-1)],
                                                                                keywords=[],
                                                                                starargs=None,
                                                                                kwargs=None),
                                                                      ifs=[])]),
                                   Num(n=-0.03),
                                   Num(n=2)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[ListComp(elt=BinOp(left=Name(id='x',
                                                                ctx=Load()),
                                                      op=Div(),
                                                      right=Num(n=1000.0)),
                                            generators=[comprehension(target=Name(id='x',
                                                                                  ctx=Store()),
                                                                      iter=Call(func=Name(id='range',
                                                                                          ctx=Load()),
                                                                                args=[Num(n=-35),
                                                                                      Num(n=-46),
                                                                                      Num(n=-1)],
                                                                                keywords=[],
                                                                                starargs=None,
                                                                                kwargs=None),
                                                                      ifs=[])]),
                                   Num(n=-0.04),
                                   Num(n=2)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[ListComp(elt=BinOp(left=Name(id='x',
                                                                ctx=Load()),
                                                      op=Div(),
                                                      right=Num(n=1000.0)),
                                            generators=[comprehension(target=Name(id='x',
                                                                                  ctx=Store()),
                                                                      iter=Call(func=Name(id='range',
                                                                                          ctx=Load()),
                                                                                args=[Num(n=-46),
                                                                                      Num(n=-55),
                                                                                      Num(n=-1)],
                                                                                keywords=[],
                                                                                starargs=None,
                                                                                kwargs=None),
                                                                      ifs=[])]),
                                   Num(n=-0.05),
                                   Num(n=2)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[ListComp(elt=BinOp(left=Name(id='x',
                                                                ctx=Load()),
                                                      op=Div(),
                                                      right=Num(n=1000.0)),
                                            generators=[comprehension(target=Name(id='x',
                                                                                  ctx=Store()),
                                                                      iter=Call(func=Name(id='range',
                                                                                          ctx=Load()),
                                                                                args=[Num(n=-55),
                                                                                      Num(n=-65),
                                                                                      Num(n=-1)],
                                                                                keywords=[],
                                                                                starargs=None,
                                                                                kwargs=None),
                                                                      ifs=[])]),
                                   Num(n=-0.06),
                                   Num(n=2)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Str(s='\n0.025 to 0.064, ndigit=2')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[ListComp(elt=BinOp(left=Name(id='x',
                                                                ctx=Load()),
                                                      op=Div(),
                                                      right=Num(n=1000.0)),
                                            generators=[comprehension(target=Name(id='x',
                                                                                  ctx=Store()),
                                                                      iter=Call(func=Name(id='range',
                                                                                          ctx=Load()),
                                                                                args=[Num(n=25),
                                                                                      Num(n=35)],
                                                                                keywords=[],
                                                                                starargs=None,
                                                                                kwargs=None),
                                                                      ifs=[])]),
                                   Num(n=0.03),
                                   Num(n=2)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[ListComp(elt=BinOp(left=Name(id='x',
                                                                ctx=Load()),
                                                      op=Div(),
                                                      right=Num(n=1000.0)),
                                            generators=[comprehension(target=Name(id='x',
                                                                                  ctx=Store()),
                                                                      iter=Call(func=Name(id='range',
                                                                                          ctx=Load()),
                                                                                args=[Num(n=35),
                                                                                      Num(n=46)],
                                                                                keywords=[],
                                                                                starargs=None,
                                                                                kwargs=None),
                                                                      ifs=[])]),
                                   Num(n=0.04),
                                   Num(n=2)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[ListComp(elt=BinOp(left=Name(id='x',
                                                                ctx=Load()),
                                                      op=Div(),
                                                      right=Num(n=1000.0)),
                                            generators=[comprehension(target=Name(id='x',
                                                                                  ctx=Store()),
                                                                      iter=Call(func=Name(id='range',
                                                                                          ctx=Load()),
                                                                                args=[Num(n=46),
                                                                                      Num(n=55)],
                                                                                keywords=[],
                                                                                starargs=None,
                                                                                kwargs=None),
                                                                      ifs=[])]),
                                   Num(n=0.05),
                                   Num(n=2)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[ListComp(elt=BinOp(left=Name(id='x',
                                                                ctx=Load()),
                                                      op=Div(),
                                                      right=Num(n=1000.0)),
                                            generators=[comprehension(target=Name(id='x',
                                                                                  ctx=Store()),
                                                                      iter=Call(func=Name(id='range',
                                                                                          ctx=Load()),
                                                                                args=[Num(n=55),
                                                                                      Num(n=65)],
                                                                                keywords=[],
                                                                                starargs=None,
                                                                                kwargs=None),
                                                                      ifs=[])]),
                                   Num(n=0.06),
                                   Num(n=2)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Str(s='\n-0.0064 to -0.0025, ndigit=3')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[ListComp(elt=BinOp(left=Name(id='x',
                                                                ctx=Load()),
                                                      op=Div(),
                                                      right=Num(n=10000.0)),
                                            generators=[comprehension(target=Name(id='x',
                                                                                  ctx=Store()),
                                                                      iter=Call(func=Name(id='range',
                                                                                          ctx=Load()),
                                                                                args=[Num(n=-25),
                                                                                      Num(n=-35),
                                                                                      Num(n=-1)],
                                                                                keywords=[],
                                                                                starargs=None,
                                                                                kwargs=None),
                                                                      ifs=[])]),
                                   Num(n=-0.003),
                                   Num(n=3)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[ListComp(elt=BinOp(left=Name(id='x',
                                                                ctx=Load()),
                                                      op=Div(),
                                                      right=Num(n=10000.0)),
                                            generators=[comprehension(target=Name(id='x',
                                                                                  ctx=Store()),
                                                                      iter=Call(func=Name(id='range',
                                                                                          ctx=Load()),
                                                                                args=[Num(n=-35),
                                                                                      Num(n=-46),
                                                                                      Num(n=-1)],
                                                                                keywords=[],
                                                                                starargs=None,
                                                                                kwargs=None),
                                                                      ifs=[])]),
                                   Num(n=-0.004),
                                   Num(n=3)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[ListComp(elt=BinOp(left=Name(id='x',
                                                                ctx=Load()),
                                                      op=Div(),
                                                      right=Num(n=10000.0)),
                                            generators=[comprehension(target=Name(id='x',
                                                                                  ctx=Store()),
                                                                      iter=Call(func=Name(id='range',
                                                                                          ctx=Load()),
                                                                                args=[Num(n=-46),
                                                                                      Num(n=-56),
                                                                                      Num(n=-1)],
                                                                                keywords=[],
                                                                                starargs=None,
                                                                                kwargs=None),
                                                                      ifs=[])]),
                                   Num(n=-0.005),
                                   Num(n=3)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[ListComp(elt=BinOp(left=Name(id='x',
                                                                ctx=Load()),
                                                      op=Div(),
                                                      right=Num(n=10000.0)),
                                            generators=[comprehension(target=Name(id='x',
                                                                                  ctx=Store()),
                                                                      iter=Call(func=Name(id='range',
                                                                                          ctx=Load()),
                                                                                args=[Num(n=-56),
                                                                                      Num(n=-65),
                                                                                      Num(n=-1)],
                                                                                keywords=[],
                                                                                starargs=None,
                                                                                kwargs=None),
                                                                      ifs=[])]),
                                   Num(n=-0.006),
                                   Num(n=3)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Str(s='\n0.0025 to 0.0064, ndigit=3')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[ListComp(elt=BinOp(left=Name(id='x',
                                                                ctx=Load()),
                                                      op=Div(),
                                                      right=Num(n=10000.0)),
                                            generators=[comprehension(target=Name(id='x',
                                                                                  ctx=Store()),
                                                                      iter=Call(func=Name(id='range',
                                                                                          ctx=Load()),
                                                                                args=[Num(n=25),
                                                                                      Num(n=35)],
                                                                                keywords=[],
                                                                                starargs=None,
                                                                                kwargs=None),
                                                                      ifs=[])]),
                                   Num(n=0.003),
                                   Num(n=3)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[ListComp(elt=BinOp(left=Name(id='x',
                                                                ctx=Load()),
                                                      op=Div(),
                                                      right=Num(n=10000.0)),
                                            generators=[comprehension(target=Name(id='x',
                                                                                  ctx=Store()),
                                                                      iter=Call(func=Name(id='range',
                                                                                          ctx=Load()),
                                                                                args=[Num(n=35),
                                                                                      Num(n=46)],
                                                                                keywords=[],
                                                                                starargs=None,
                                                                                kwargs=None),
                                                                      ifs=[])]),
                                   Num(n=0.004),
                                   Num(n=3)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[ListComp(elt=BinOp(left=Name(id='x',
                                                                ctx=Load()),
                                                      op=Div(),
                                                      right=Num(n=10000.0)),
                                            generators=[comprehension(target=Name(id='x',
                                                                                  ctx=Store()),
                                                                      iter=Call(func=Name(id='range',
                                                                                          ctx=Load()),
                                                                                args=[Num(n=46),
                                                                                      Num(n=56)],
                                                                                keywords=[],
                                                                                starargs=None,
                                                                                kwargs=None),
                                                                      ifs=[])]),
                                   Num(n=0.005),
                                   Num(n=3)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[ListComp(elt=BinOp(left=Name(id='x',
                                                                ctx=Load()),
                                                      op=Div(),
                                                      right=Num(n=10000.0)),
                                            generators=[comprehension(target=Name(id='x',
                                                                                  ctx=Store()),
                                                                      iter=Call(func=Name(id='range',
                                                                                          ctx=Load()),
                                                                                args=[Num(n=56),
                                                                                      Num(n=65)],
                                                                                keywords=[],
                                                                                starargs=None,
                                                                                kwargs=None),
                                                                      ifs=[])]),
                                   Num(n=0.006),
                                   Num(n=3)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Str(s='\n-649 to -250, ndigit=-2')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Name(id='range',
                                                  ctx=Load()),
                                        args=[Num(n=-250),
                                              Num(n=-350),
                                              Num(n=-1)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Num(n=-300),
                                   Num(n=-2)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Name(id='range',
                                                  ctx=Load()),
                                        args=[Num(n=-350),
                                              Num(n=-450),
                                              Num(n=-1)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Num(n=-400),
                                   Num(n=-2)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Name(id='range',
                                                  ctx=Load()),
                                        args=[Num(n=-450),
                                              Num(n=-550),
                                              Num(n=-1)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Num(n=-500),
                                   Num(n=-2)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Name(id='range',
                                                  ctx=Load()),
                                        args=[Num(n=-550),
                                              Num(n=-650),
                                              Num(n=-1)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Num(n=-600),
                                   Num(n=-2)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Str(s='\n250 to 649, ndigit=-2')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Name(id='range',
                                                  ctx=Load()),
                                        args=[Num(n=250),
                                              Num(n=350)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Num(n=300),
                                   Num(n=-2)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Name(id='range',
                                                  ctx=Load()),
                                        args=[Num(n=350),
                                              Num(n=450)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Num(n=400),
                                   Num(n=-2)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Name(id='range',
                                                  ctx=Load()),
                                        args=[Num(n=450),
                                              Num(n=550)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Num(n=500),
                                   Num(n=-2)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Name(id='range',
                                                  ctx=Load()),
                                        args=[Num(n=550),
                                              Num(n=650)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Num(n=600),
                                   Num(n=-2)],
                             keywords=[],
                             starargs=None,
                             kwargs=None))])
