Module(body=[Assign(targets=[Name(id='d',
                                  ctx=Store())],
                    value=Dict(keys=[Str(s='x'),
                                     Str(s='y'),
                                     Str(s='z')],
                               values=[Num(n=1),
                                       Num(n=2),
                                       Num(n=3)])),
             FunctionDef(name='a',
                         args=arguments(args=[Name(id='x',
                                                   ctx=Param()),
                                              Name(id='y',
                                                   ctx=Param()),
                                              Name(id='z',
                                                   ctx=Param())],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[]),
                         body=[Return(value=Tuple(elts=[Name(id='x',
                                                             ctx=Load()),
                                                        Name(id='y',
                                                             ctx=Load()),
                                                        Name(id='z',
                                                             ctx=Load())],
                                                  ctx=Load()))],
                         decorator_list=[]),
             Print(dest=None,
                   values=[Str(s='\nFunction')],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='a',
                                          ctx=Load()),
                                args=[Num(n=1),
                                      Num(n=2),
                                      Num(n=3)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='a',
                                          ctx=Load()),
                                args=[],
                                keywords=[keyword(arg='z',
                                                  value=Num(n=3)),
                                          keyword(arg='x',
                                                  value=Num(n=1)),
                                          keyword(arg='y',
                                                  value=Num(n=2))],
                                starargs=None,
                                kwargs=None),
                           Call(func=Name(id='a',
                                          ctx=Load()),
                                args=[],
                                keywords=[keyword(arg='z',
                                                  value=Num(n=3)),
                                          keyword(arg='y',
                                                  value=Num(n=2)),
                                          keyword(arg='x',
                                                  value=Num(n=1))],
                                starargs=None,
                                kwargs=None),
                           Call(func=Name(id='a',
                                          ctx=Load()),
                                args=[],
                                keywords=[keyword(arg='y',
                                                  value=Num(n=2)),
                                          keyword(arg='z',
                                                  value=Num(n=3)),
                                          keyword(arg='x',
                                                  value=Num(n=1))],
                                starargs=None,
                                kwargs=None),
                           Call(func=Name(id='a',
                                          ctx=Load()),
                                args=[],
                                keywords=[keyword(arg='y',
                                                  value=Num(n=2)),
                                          keyword(arg='x',
                                                  value=Num(n=1)),
                                          keyword(arg='z',
                                                  value=Num(n=3))],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             FunctionDef(name='b',
                         args=arguments(args=[Name(id='x',
                                                   ctx=Param()),
                                              Name(id='y',
                                                   ctx=Param()),
                                              Name(id='z',
                                                   ctx=Param())],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[Num(n=0),
                                                  Num(n=0),
                                                  Num(n=0)]),
                         body=[Return(value=Tuple(elts=[Name(id='x',
                                                             ctx=Load()),
                                                        Name(id='y',
                                                             ctx=Load()),
                                                        Name(id='z',
                                                             ctx=Load())],
                                                  ctx=Load()))],
                         decorator_list=[]),
             Print(dest=None,
                   values=[Str(s='\nFunction with defaults')],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='b',
                                          ctx=Load()),
                                args=[],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='b',
                                          ctx=Load()),
                                args=[Num(n=1),
                                      Num(n=2),
                                      Num(n=3)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='b',
                                          ctx=Load()),
                                args=[Num(n=1)],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Call(func=Name(id='b',
                                          ctx=Load()),
                                args=[Num(n=2)],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Call(func=Name(id='b',
                                          ctx=Load()),
                                args=[Num(n=3)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='b',
                                          ctx=Load()),
                                args=[],
                                keywords=[keyword(arg='x',
                                                  value=Num(n=1))],
                                starargs=None,
                                kwargs=None),
                           Call(func=Name(id='b',
                                          ctx=Load()),
                                args=[],
                                keywords=[keyword(arg='y',
                                                  value=Num(n=2))],
                                starargs=None,
                                kwargs=None),
                           Call(func=Name(id='b',
                                          ctx=Load()),
                                args=[],
                                keywords=[keyword(arg='z',
                                                  value=Num(n=3))],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='b',
                                          ctx=Load()),
                                args=[],
                                keywords=[keyword(arg='x',
                                                  value=Num(n=1)),
                                          keyword(arg='z',
                                                  value=Num(n=3))],
                                starargs=None,
                                kwargs=None),
                           Call(func=Name(id='b',
                                          ctx=Load()),
                                args=[],
                                keywords=[keyword(arg='z',
                                                  value=Num(n=3)),
                                          keyword(arg='x',
                                                  value=Num(n=1))],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='b',
                                          ctx=Load()),
                                args=[],
                                keywords=[keyword(arg='x',
                                                  value=Num(n=1)),
                                          keyword(arg='y',
                                                  value=Num(n=2))],
                                starargs=None,
                                kwargs=None),
                           Call(func=Name(id='b',
                                          ctx=Load()),
                                args=[],
                                keywords=[keyword(arg='y',
                                                  value=Num(n=2)),
                                          keyword(arg='x',
                                                  value=Num(n=1))],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='b',
                                          ctx=Load()),
                                args=[],
                                keywords=[keyword(arg='z',
                                                  value=Num(n=3)),
                                          keyword(arg='y',
                                                  value=Num(n=2))],
                                starargs=None,
                                kwargs=None),
                           Call(func=Name(id='b',
                                          ctx=Load()),
                                args=[],
                                keywords=[keyword(arg='y',
                                                  value=Num(n=2)),
                                          keyword(arg='z',
                                                  value=Num(n=3))],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='b',
                                          ctx=Load()),
                                args=[],
                                keywords=[keyword(arg='z',
                                                  value=Num(n=3)),
                                          keyword(arg='x',
                                                  value=Num(n=1)),
                                          keyword(arg='y',
                                                  value=Num(n=2))],
                                starargs=None,
                                kwargs=None),
                           Call(func=Name(id='b',
                                          ctx=Load()),
                                args=[],
                                keywords=[keyword(arg='z',
                                                  value=Num(n=3)),
                                          keyword(arg='y',
                                                  value=Num(n=2)),
                                          keyword(arg='x',
                                                  value=Num(n=1))],
                                starargs=None,
                                kwargs=None),
                           Call(func=Name(id='b',
                                          ctx=Load()),
                                args=[],
                                keywords=[keyword(arg='y',
                                                  value=Num(n=2)),
                                          keyword(arg='z',
                                                  value=Num(n=3)),
                                          keyword(arg='x',
                                                  value=Num(n=1))],
                                starargs=None,
                                kwargs=None),
                           Call(func=Name(id='b',
                                          ctx=Load()),
                                args=[],
                                keywords=[keyword(arg='y',
                                                  value=Num(n=2)),
                                          keyword(arg='x',
                                                  value=Num(n=1)),
                                          keyword(arg='z',
                                                  value=Num(n=3))],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             ClassDef(name='A',
                      bases=[],
                      body=[FunctionDef(name='__init__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param()),
                                                             Name(id='x',
                                                                  ctx=Param()),
                                                             Name(id='y',
                                                                  ctx=Param()),
                                                             Name(id='z',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Assign(targets=[Attribute(value=Name(id='self',
                                                                                   ctx=Load()),
                                                                        attr='x',
                                                                        ctx=Store())],
                                                     value=Name(id='x',
                                                                ctx=Load())),
                                              Assign(targets=[Attribute(value=Name(id='self',
                                                                                   ctx=Load()),
                                                                        attr='y',
                                                                        ctx=Store())],
                                                     value=Name(id='y',
                                                                ctx=Load())),
                                              Assign(targets=[Attribute(value=Name(id='self',
                                                                                   ctx=Load()),
                                                                        attr='z',
                                                                        ctx=Store())],
                                                     value=Name(id='z',
                                                                ctx=Load()))],
                                        decorator_list=[]),
                            FunctionDef(name='__str__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Return(value=Call(func=Name(id='str',
                                                                          ctx=Load()),
                                                                args=[Tuple(elts=[Attribute(value=Name(id='self',
                                                                                                       ctx=Load()),
                                                                                            attr='x',
                                                                                            ctx=Load()),
                                                                                  Attribute(value=Name(id='self',
                                                                                                       ctx=Load()),
                                                                                            attr='y',
                                                                                            ctx=Load()),
                                                                                  Attribute(value=Name(id='self',
                                                                                                       ctx=Load()),
                                                                                            attr='z',
                                                                                            ctx=Load())],
                                                                            ctx=Load())],
                                                                keywords=[],
                                                                starargs=None,
                                                                kwargs=None))],
                                        decorator_list=[])],
                      decorator_list=[]),
             Print(dest=None,
                   values=[Str(s='\nClass')],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='A',
                                          ctx=Load()),
                                args=[Num(n=1),
                                      Num(n=2),
                                      Num(n=3)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             ClassDef(name='B',
                      bases=[],
                      body=[FunctionDef(name='__init__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param()),
                                                             Name(id='x',
                                                                  ctx=Param()),
                                                             Name(id='y',
                                                                  ctx=Param()),
                                                             Name(id='z',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[Num(n=0),
                                                                 Num(n=0),
                                                                 Num(n=0)]),
                                        body=[Assign(targets=[Attribute(value=Name(id='self',
                                                                                   ctx=Load()),
                                                                        attr='x',
                                                                        ctx=Store())],
                                                     value=Name(id='x',
                                                                ctx=Load())),
                                              Assign(targets=[Attribute(value=Name(id='self',
                                                                                   ctx=Load()),
                                                                        attr='y',
                                                                        ctx=Store())],
                                                     value=Name(id='y',
                                                                ctx=Load())),
                                              Assign(targets=[Attribute(value=Name(id='self',
                                                                                   ctx=Load()),
                                                                        attr='z',
                                                                        ctx=Store())],
                                                     value=Name(id='z',
                                                                ctx=Load()))],
                                        decorator_list=[]),
                            FunctionDef(name='__str__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Return(value=Call(func=Name(id='str',
                                                                          ctx=Load()),
                                                                args=[Tuple(elts=[Attribute(value=Name(id='self',
                                                                                                       ctx=Load()),
                                                                                            attr='x',
                                                                                            ctx=Load()),
                                                                                  Attribute(value=Name(id='self',
                                                                                                       ctx=Load()),
                                                                                            attr='y',
                                                                                            ctx=Load()),
                                                                                  Attribute(value=Name(id='self',
                                                                                                       ctx=Load()),
                                                                                            attr='z',
                                                                                            ctx=Load())],
                                                                            ctx=Load())],
                                                                keywords=[],
                                                                starargs=None,
                                                                kwargs=None))],
                                        decorator_list=[])],
                      decorator_list=[]),
             Print(dest=None,
                   values=[Str(s='\nClass with defaults')],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='B',
                                          ctx=Load()),
                                args=[],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='B',
                                          ctx=Load()),
                                args=[Num(n=1),
                                      Num(n=2),
                                      Num(n=3)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='B',
                                          ctx=Load()),
                                args=[Num(n=1)],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Call(func=Name(id='B',
                                          ctx=Load()),
                                args=[Num(n=2)],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Call(func=Name(id='B',
                                          ctx=Load()),
                                args=[Num(n=3)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True)])
