Module(body=[Import(names=[alias(name='math',
                                 asname=None)]),
             Print(dest=None,
                   values=[Str(s='\nmath.sinh')],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='sinh',
                                               ctx=Load()),
                                args=[Num(n=2)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='sinh',
                                               ctx=Load()),
                                args=[Num(n=-7.5)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='sinh',
                                               ctx=Load()),
                                args=[Num(n=0)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='sinh',
                                               ctx=Load()),
                                args=[Num(n=13)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\nmath.cosh')],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='cosh',
                                               ctx=Load()),
                                args=[Num(n=2)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='cosh',
                                               ctx=Load()),
                                args=[Num(n=-7.5)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='cosh',
                                               ctx=Load()),
                                args=[Num(n=0)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='cosh',
                                               ctx=Load()),
                                args=[Num(n=13)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\nmath.tanh')],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='tanh',
                                               ctx=Load()),
                                args=[Num(n=2)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='tanh',
                                               ctx=Load()),
                                args=[Num(n=-7.5)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='tanh',
                                               ctx=Load()),
                                args=[Num(n=0)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='tanh',
                                               ctx=Load()),
                                args=[Num(n=13)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\nmath.asinh')],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='asinh',
                                               ctx=Load()),
                                args=[Num(n=2)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='asinh',
                                               ctx=Load()),
                                args=[Num(n=-7.5)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='asinh',
                                               ctx=Load()),
                                args=[Num(n=0)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='asinh',
                                               ctx=Load()),
                                args=[Num(n=13)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\nmath.acosh')],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='acosh',
                                               ctx=Load()),
                                args=[Num(n=2)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='acosh',
                                               ctx=Load()),
                                args=[Num(n=1.2)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='acosh',
                                               ctx=Load()),
                                args=[Num(n=13)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\nmath.atanh')],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='atanh',
                                               ctx=Load()),
                                args=[Num(n=-0.2)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='atanh',
                                               ctx=Load()),
                                args=[Num(n=0)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='atanh',
                                               ctx=Load()),
                                args=[Num(n=0.5)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True)])
