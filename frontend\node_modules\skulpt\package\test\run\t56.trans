Module(body=[FunctionDef(name='test',
                         args=arguments(args=[],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[]),
                         body=[Pass()],
                         decorator_list=[]),
             Assign(targets=[Name(id='x',
                                  ctx=Store())],
                    value=Num(n=1)),
             Print(dest=None,
                   values=[Call(func=Name(id='test',
                                          ctx=Load()),
                                args=[],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True)])
