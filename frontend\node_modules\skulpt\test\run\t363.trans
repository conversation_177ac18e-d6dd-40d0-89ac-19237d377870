Module(body=[For(target=Name(id='x',
                             ctx=Store()),
                 iter=Call(func=Name(id='enumerate',
                                     ctx=Load()),
                           args=[Call(func=Name(id='range',
                                                ctx=Load()),
                                      args=[Num(n=3)],
                                      keywords=[],
                                      starargs=None,
                                      kwargs=None)],
                           keywords=[],
                           starargs=None,
                           kwargs=None),
                 body=[Print(dest=None,
                             values=[Name(id='x',
                                          ctx=Load())],
                             nl=True)],
                 orelse=[]),
             For(target=Tuple(elts=[Name(id='i',
                                         ctx=Store()),
                                    Name(id='v',
                                         ctx=Store())],
                              ctx=Store()),
                 iter=Call(func=Name(id='enumerate',
                                     ctx=Load()),
                           args=[Call(func=Name(id='range',
                                                ctx=Load()),
                                      args=[Num(n=4),
                                            Num(n=8)],
                                      keywords=[],
                                      starargs=None,
                                      kwargs=None)],
                           keywords=[],
                           starargs=None,
                           kwargs=None),
                 body=[Print(dest=None,
                             values=[Name(id='i',
                                          ctx=Load()),
                                     Name(id='v',
                                          ctx=Load())],
                             nl=True)],
                 orelse=[]),
             For(target=Name(id='x',
                             ctx=Store()),
                 iter=Call(func=Name(id='enumerate',
                                     ctx=Load()),
                           args=[List(elts=[Num(n=14),
                                            Num(n=8),
                                            Num(n=2),
                                            Str(s='abc'),
                                            Num(n=-7)],
                                      ctx=Load()),
                                 Num(n=2)],
                           keywords=[],
                           starargs=None,
                           kwargs=None),
                 body=[Print(dest=None,
                             values=[Name(id='x',
                                          ctx=Load())],
                             nl=True)],
                 orelse=[]),
             Print(dest=None,
                   values=[Name(id='enumerate',
                                ctx=Load())],
                   nl=True),
             Assign(targets=[Name(id='e',
                                  ctx=Store())],
                    value=Call(func=Name(id='enumerate',
                                         ctx=Load()),
                               args=[List(elts=[Num(n=4),
                                                Num(n=8),
                                                Num(n=12)],
                                          ctx=Load()),
                                     Num(n=-3)],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Name(id='e',
                                ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='repr',
                                          ctx=Load()),
                                args=[Name(id='e',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='e',
                                                          ctx=Load()),
                                               attr='next',
                                               ctx=Load()),
                                args=[],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Assign(targets=[Name(id='d',
                                  ctx=Store())],
                    value=Dict(keys=[Name(id='e',
                                          ctx=Load())],
                               values=[Num(n=3)])),
             Print(dest=None,
                   values=[Name(id='d',
                                ctx=Load())],
                   nl=True),
             For(target=Name(id='x',
                             ctx=Store()),
                 iter=Name(id='e',
                           ctx=Load()),
                 body=[Print(dest=None,
                             values=[Name(id='x',
                                          ctx=Load())],
                             nl=True)],
                 orelse=[]),
             For(target=Name(id='y',
                             ctx=Store()),
                 iter=Name(id='e',
                           ctx=Load()),
                 body=[Print(dest=None,
                             values=[Name(id='y',
                                          ctx=Load())],
                             nl=True)],
                 orelse=[])])
