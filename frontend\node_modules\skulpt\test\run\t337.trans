Module(body=[Assign(targets=[Name(id='l',
                                  ctx=Store())],
                    value=Call(func=Name(id='list',
                                         ctx=Load()),
                               args=[],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Name(id='l',
                                ctx=Load())],
                   nl=True),
             Expr(value=Call(func=Attribute(value=Name(id='l',
                                                       ctx=Load()),
                                            attr='append',
                                            ctx=Load()),
                             args=[Num(n=1)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Name(id='l',
                                ctx=Load())],
                   nl=True),
             Assign(targets=[Name(id='d',
                                  ctx=Store())],
                    value=Call(func=Name(id='dict',
                                         ctx=Load()),
                               args=[],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Name(id='d',
                                ctx=Load())],
                   nl=True),
             Assign(targets=[Subscript(value=Name(id='d',
                                                  ctx=Load()),
                                       slice=Index(value=Str(s='zap')),
                                       ctx=Store())],
                    value=Num(n=1)),
             Print(dest=None,
                   values=[Name(id='d',
                                ctx=Load())],
                   nl=True),
             Assign(targets=[Name(id='l2',
                                  ctx=Store())],
                    value=Call(func=Name(id='list',
                                         ctx=Load()),
                               args=[Name(id='l',
                                          ctx=Load())],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Name(id='l2',
                                ctx=Load())],
                   nl=True),
             Assign(targets=[Name(id='l3',
                                  ctx=Store())],
                    value=Call(func=Name(id='list',
                                         ctx=Load()),
                               args=[Name(id='d',
                                          ctx=Load())],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Name(id='l3',
                                ctx=Load())],
                   nl=True)])
