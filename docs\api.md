# API Documentation

Base URL: `http://localhost:5000/api` (development) or `https://your-domain.com/api` (production)

## Authentication

The API uses JWT (JSON Web Tokens) for authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

## Response Format

All API responses follow this format:

### Success Response
```json
{
  "data": { ... },
  "message": "Success message"
}
```

### Error Response
```json
{
  "error": "Error message",
  "details": "Additional error details (optional)"
}
```

## Authentication Endpoints

### Register User
**POST** `/auth/register`

Register a new user account.

**Request Body:**
```json
{
  "username": "string (3+ characters)",
  "email": "string (valid email)",
  "password": "string (6+ characters, must contain letter and number)"
}
```

**Response:**
```json
{
  "message": "User registered successfully",
  "user": {
    "id": "string",
    "username": "string",
    "email": "string",
    "points": 0,
    "level": 1,
    "current_streak": 0,
    "lessons_completed": 0,
    "is_guest": false,
    "badges": [],
    "created_at": "ISO date string"
  },
  "token": "JWT token string"
}
```

### Login User
**POST** `/auth/login`

Authenticate a user and receive a JWT token.

**Request Body:**
```json
{
  "email": "string",
  "password": "string"
}
```

**Response:**
```json
{
  "message": "Login successful",
  "user": { ... },
  "token": "JWT token string"
}
```

### Get Current User
**GET** `/auth/me`

Get information about the currently authenticated user.

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
{
  "user": { ... }
}
```

### Refresh Token
**POST** `/auth/refresh`

Get a new JWT token using the current token.

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
{
  "token": "New JWT token string",
  "user": { ... }
}
```

## Lesson Endpoints

### Get All Lessons
**GET** `/lessons`

Get a list of all published lessons. Authentication is optional - guest users can access this endpoint.

**Response:**
```json
{
  "lessons": [
    {
      "id": "string",
      "title": "string",
      "description": "string",
      "content": "string (markdown)",
      "order": "number",
      "difficulty": "beginner|intermediate|advanced",
      "estimated_time": "number (minutes)",
      "prerequisites": ["lesson_id_1", "lesson_id_2"],
      "exercises": [
        {
          "id": "string",
          "title": "string",
          "description": "string",
          "starter_code": "string",
          "solution": "string",
          "hints": ["hint1", "hint2"],
          "test_cases": [
            {
              "input": "string",
              "expected_output": "string",
              "description": "string"
            }
          ]
        }
      ],
      "is_unlocked": "boolean",
      "is_completed": "boolean"
    }
  ]
}
```

### Get Specific Lesson
**GET** `/lessons/{lesson_id}`

Get details for a specific lesson.

**Response:**
```json
{
  "lesson": { ... }
}
```

### Complete Lesson
**POST** `/lessons/{lesson_id}/complete`

Mark a lesson as completed and update user progress.

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "score": "number (0-100)",
  "time_spent": "number (minutes)"
}
```

**Response:**
```json
{
  "message": "Lesson completed successfully",
  "progress": {
    "id": "number",
    "user_id": "number",
    "lesson_id": "string",
    "completed": true,
    "score": "number",
    "time_spent": "number",
    "completed_at": "ISO date string",
    "created_at": "ISO date string"
  },
  "user": { ... },
  "new_badges": [
    {
      "id": "number",
      "name": "string",
      "description": "string",
      "icon": "string",
      "criteria": { ... }
    }
  ]
}
```

### Get User Progress
**GET** `/lessons/progress`

Get the current user's progress on all lessons.

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
{
  "progress": [
    {
      "id": "number",
      "user_id": "number",
      "lesson_id": "string",
      "completed": "boolean",
      "score": "number",
      "time_spent": "number",
      "completed_at": "ISO date string or null",
      "created_at": "ISO date string"
    }
  ]
}
```

### Get Lesson Statistics
**GET** `/lessons/stats`

Get statistics about the user's lesson progress.

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
{
  "stats": {
    "total_lessons": "number",
    "completed_lessons": "number",
    "completion_percentage": "number",
    "total_time_spent": "number",
    "average_score": "number",
    "recent_activity": [ ... ]
  }
}
```

## User Endpoints

### Get User Profile
**GET** `/users/profile`

Get detailed profile information for the current user.

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
{
  "profile": {
    "id": "string",
    "username": "string",
    "email": "string",
    "points": "number",
    "level": "number",
    "current_streak": "number",
    "lessons_completed": "number",
    "is_guest": "boolean",
    "badges_earned": [
      {
        "id": "number",
        "name": "string",
        "description": "string",
        "icon": "string",
        "earned_at": "ISO date string"
      }
    ],
    "total_time_spent": "number",
    "average_score": "number",
    "created_at": "ISO date string"
  }
}
```

### Update User Profile
**PUT** `/users/profile`

Update the current user's profile information.

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "username": "string (optional)"
}
```

**Response:**
```json
{
  "message": "Profile updated successfully",
  "user": { ... }
}
```

### Get User Badges
**GET** `/users/badges`

Get all badges and the user's progress towards earning them.

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
{
  "badges": [
    {
      "id": "number",
      "name": "string",
      "description": "string",
      "icon": "string",
      "criteria": { ... },
      "points_required": "number",
      "lessons_required": "number",
      "earned": "boolean",
      "earned_at": "ISO date string or null"
    }
  ]
}
```

### Get Dashboard Data
**GET** `/users/dashboard`

Get comprehensive dashboard data for the current user.

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
{
  "user": { ... },
  "stats": {
    "completion_percentage": "number",
    "total_lessons": "number",
    "completed_lessons": "number"
  },
  "recent_progress": [ ... ],
  "recent_badges": [ ... ],
  "next_lesson": { ... }
}
```

### Get Leaderboard
**GET** `/users/leaderboard`

Get the top users by points (public endpoint).

**Response:**
```json
{
  "leaderboard": [
    {
      "rank": "number",
      "username": "string",
      "points": "number",
      "lessons_completed": "number",
      "level": "number"
    }
  ]
}
```

## Error Codes

| Status Code | Description |
|-------------|-------------|
| 200 | Success |
| 201 | Created |
| 400 | Bad Request - Invalid input data |
| 401 | Unauthorized - Invalid or missing token |
| 403 | Forbidden - Access denied |
| 404 | Not Found - Resource doesn't exist |
| 409 | Conflict - Resource already exists |
| 500 | Internal Server Error |

## Rate Limiting

The API implements rate limiting to prevent abuse:

- **Authentication endpoints**: 5 requests per minute per IP
- **General endpoints**: 100 requests per minute per user
- **Lesson completion**: 10 requests per minute per user

## Example Usage

### JavaScript/Fetch Example

```javascript
// Register a new user
const registerUser = async (userData) => {
  const response = await fetch('/api/auth/register', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(userData),
  });
  
  const data = await response.json();
  
  if (response.ok) {
    // Store the token
    localStorage.setItem('token', data.token);
    return data.user;
  } else {
    throw new Error(data.error);
  }
};

// Make authenticated requests
const getProfile = async () => {
  const token = localStorage.getItem('token');
  
  const response = await fetch('/api/users/profile', {
    headers: {
      'Authorization': `Bearer ${token}`,
    },
  });
  
  const data = await response.json();
  
  if (response.ok) {
    return data.profile;
  } else {
    throw new Error(data.error);
  }
};
```

### Python/Requests Example

```python
import requests

# Register a new user
def register_user(user_data):
    response = requests.post(
        'http://localhost:5000/api/auth/register',
        json=user_data
    )
    
    if response.status_code == 201:
        data = response.json()
        return data['user'], data['token']
    else:
        raise Exception(response.json()['error'])

# Make authenticated requests
def get_profile(token):
    headers = {'Authorization': f'Bearer {token}'}
    response = requests.get(
        'http://localhost:5000/api/users/profile',
        headers=headers
    )
    
    if response.status_code == 200:
        return response.json()['profile']
    else:
        raise Exception(response.json()['error'])
```
