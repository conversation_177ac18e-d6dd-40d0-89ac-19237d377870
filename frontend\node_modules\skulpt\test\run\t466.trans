Module(body=[Import(names=[alias(name='re',
                                 asname=None)]),
             FunctionDef(name='helper',
                         args=arguments(args=[Name(id='string',
                                                   ctx=Param()),
                                              Name(id='pattern',
                                                   ctx=Param())],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[]),
                         body=[Assign(targets=[Name(id='match',
                                                    ctx=Store())],
                                      value=Call(func=Attribute(value=Name(id='re',
                                                                           ctx=Load()),
                                                                attr='match',
                                                                ctx=Load()),
                                                 args=[Name(id='string',
                                                            ctx=Load()),
                                                       Name(id='pattern',
                                                            ctx=Load())],
                                                 keywords=[],
                                                 starargs=None,
                                                 kwargs=None)),
                               If(test=Name(id='match',
                                            ctx=Load()),
                                  body=[Print(dest=None,
                                              values=[Call(func=Attribute(value=Name(id='match',
                                                                                     ctx=Load()),
                                                                          attr='group',
                                                                          ctx=Load()),
                                                           args=[Num(n=0)],
                                                           keywords=[],
                                                           starargs=None,
                                                           kwargs=None)],
                                              nl=True)],
                                  orelse=[Print(dest=None,
                                                values=[Name(id='False',
                                                             ctx=Load())],
                                                nl=True)]),
                               Assign(targets=[Name(id='search',
                                                    ctx=Store())],
                                      value=Call(func=Attribute(value=Name(id='re',
                                                                           ctx=Load()),
                                                                attr='search',
                                                                ctx=Load()),
                                                 args=[Name(id='string',
                                                            ctx=Load()),
                                                       Name(id='pattern',
                                                            ctx=Load())],
                                                 keywords=[],
                                                 starargs=None,
                                                 kwargs=None)),
                               If(test=Name(id='search',
                                            ctx=Load()),
                                  body=[Print(dest=None,
                                              values=[Call(func=Attribute(value=Name(id='search',
                                                                                     ctx=Load()),
                                                                          attr='group',
                                                                          ctx=Load()),
                                                           args=[Num(n=0)],
                                                           keywords=[],
                                                           starargs=None,
                                                           kwargs=None)],
                                              nl=True)],
                                  orelse=[Print(dest=None,
                                                values=[Name(id='False',
                                                             ctx=Load())],
                                                nl=True)]),
                               Assign(targets=[Name(id='find',
                                                    ctx=Store())],
                                      value=Call(func=Attribute(value=Name(id='re',
                                                                           ctx=Load()),
                                                                attr='findall',
                                                                ctx=Load()),
                                                 args=[Name(id='string',
                                                            ctx=Load()),
                                                       Name(id='pattern',
                                                            ctx=Load())],
                                                 keywords=[],
                                                 starargs=None,
                                                 kwargs=None)),
                               Print(dest=None,
                                     values=[Name(id='find',
                                                  ctx=Load())],
                                     nl=True)],
                         decorator_list=[]),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Str(s='a{,2}b'),
                                   Str(s='b')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Str(s='a{,2}b'),
                                   Str(s='ab')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Str(s='a[{,}]b'),
                                   Str(s='a{b')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Str(s='a\\{,b'),
                                   Str(s='a{,b')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Str(s='a{,2}[a-z]'),
                                   Str(s='ab')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Str(s='a{,2}b{,5}'),
                                   Str(s='')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Str(s='a[{,[a-z]]b'),
                                   Str(s='a,cb')],
                             keywords=[],
                             starargs=None,
                             kwargs=None))])
