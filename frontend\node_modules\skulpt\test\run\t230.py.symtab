Sym_type: module
Sym_name: top
Sym_lineno: 0
Sym_nested: False
Sym_haschildren: True
-- Identifiers --
name: f
  is_referenced: True
  is_imported: False
  is_parameter: False
  is_global: False
  is_declared_global: False
  is_local: True
  is_free: False
  is_assigned: True
  is_namespace: True
  namespaces: [
    Sym_type: function
    Sym_name: f
    Sym_lineno: 1
    Sym_nested: False
    Sym_haschildren: False
    Func_params: ['n']
    Func_locals: ['i', 'n']
    Func_globals: ['range']
    Func_frees: []
    -- Identifiers --
    name: i
      is_referenced: True
      is_imported: False
      is_parameter: False
      is_global: False
      is_declared_global: False
      is_local: True
      is_free: False
      is_assigned: True
      is_namespace: False
      namespaces: [
      ]
    name: n
      is_referenced: True
      is_imported: False
      is_parameter: True
      is_global: False
      is_declared_global: False
      is_local: True
      is_free: False
      is_assigned: False
      is_namespace: False
      namespaces: [
      ]
    name: range
      is_referenced: True
      is_imported: False
      is_parameter: False
      is_global: True
      is_declared_global: False
      is_local: False
      is_free: False
      is_assigned: False
      is_namespace: False
      namespaces: [
      ]
  ]
name: g
  is_referenced: True
  is_imported: False
  is_parameter: False
  is_global: False
  is_declared_global: False
  is_local: True
  is_free: False
  is_assigned: True
  is_namespace: False
  namespaces: [
  ]
