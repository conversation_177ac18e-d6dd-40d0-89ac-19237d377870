Mo<PERSON>le(body=[Import(names=[alias(name='re',
                                 asname=None)]),
             Assign(targets=[Name(id='m',
                                  ctx=Store())],
                    value=Call(func=Attribute(value=Name(id='re',
                                                         ctx=Load()),
                                              attr='match',
                                              ctx=Load()),
                               args=[Str(s='([0-9]+)([a-z]+)'),
                                     Str(s='345abu')],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='m',
                                                          ctx=Load()),
                                               attr='groups',
                                               ctx=Load()),
                                args=[],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='m',
                                                          ctx=Load()),
                                               attr='group',
                                               ctx=Load()),
                                args=[Num(n=0)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='m',
                                                          ctx=Load()),
                                               attr='group',
                                               ctx=Load()),
                                args=[Num(n=1)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='m',
                                                          ctx=Load()),
                                               attr='group',
                                               ctx=Load()),
                                args=[Num(n=2)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True)])
