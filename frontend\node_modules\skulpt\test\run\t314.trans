Module(body=[Assign(targets=[Name(id='s',
                                  ctx=Store())],
                    value=Call(func=Name(id='set',
                                         ctx=Load()),
                               args=[List(elts=[Num(n=2),
                                                Num(n=3),
                                                Num(n=4)],
                                          ctx=Load())],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Assign(targets=[Name(id='t',
                                  ctx=Store())],
                    value=Call(func=Name(id='set',
                                         ctx=Load()),
                               args=[List(elts=[Num(n=3),
                                                Num(n=4),
                                                Num(n=5)],
                                          ctx=Load())],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Assign(targets=[Name(id='u',
                                  ctx=Store())],
                    value=Call(func=Name(id='set',
                                         ctx=Load()),
                               args=[List(elts=[Num(n=1),
                                                Num(n=3),
                                                Num(n=5)],
                                          ctx=Load())],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Name(id='s',
                                ctx=Load())],
                   nl=True),
             Expr(value=Call(func=Attribute(value=Name(id='s',
                                                       ctx=Load()),
                                            attr='difference_update',
                                            ctx=Load()),
                             args=[Name(id='t',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Attribute(value=Name(id='u',
                                                       ctx=Load()),
                                            attr='difference_update',
                                            ctx=Load()),
                             args=[Name(id='t',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Name(id='s',
                                ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Name(id='u',
                                ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='s',
                                             ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Call(func=Name(id='set',
                                                               ctx=Load()),
                                                     args=[List(elts=[Num(n=2)],
                                                                ctx=Load())],
                                                     keywords=[],
                                                     starargs=None,
                                                     kwargs=None)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='u',
                                             ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Call(func=Name(id='set',
                                                               ctx=Load()),
                                                     args=[List(elts=[Num(n=1)],
                                                                ctx=Load())],
                                                     keywords=[],
                                                     starargs=None,
                                                     kwargs=None)])],
                   nl=True),
             Assign(targets=[Name(id='s',
                                  ctx=Store())],
                    value=Call(func=Name(id='set',
                                         ctx=Load()),
                               args=[List(elts=[Num(n=2),
                                                Num(n=3),
                                                Num(n=4)],
                                          ctx=Load())],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Assign(targets=[Name(id='t',
                                  ctx=Store())],
                    value=Call(func=Name(id='set',
                                         ctx=Load()),
                               args=[List(elts=[Num(n=3),
                                                Num(n=4),
                                                Num(n=5)],
                                          ctx=Load())],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Expr(value=Call(func=Attribute(value=Name(id='t',
                                                       ctx=Load()),
                                            attr='difference_update',
                                            ctx=Load()),
                             args=[Name(id='s',
                                        ctx=Load()),
                                   Name(id='u',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Name(id='t',
                                ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='t',
                                             ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Call(func=Name(id='set',
                                                               ctx=Load()),
                                                     args=[List(elts=[Num(n=5)],
                                                                ctx=Load())],
                                                     keywords=[],
                                                     starargs=None,
                                                     kwargs=None)])],
                   nl=True)])
