Module(body=[Assign(targets=[Name(id='d',
                                  ctx=Store())],
                    value=Dict(keys=[Num(n=1)],
                               values=[Num(n=2)])),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='d',
                                                          ctx=Load()),
                                               attr='has_key',
                                               ctx=Load()),
                                args=[Num(n=1)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='d',
                                                          ctx=Load()),
                                               attr='has_key',
                                               ctx=Load()),
                                args=[Num(n=4)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='d',
                                                          ctx=Load()),
                                               attr='has_key',
                                               ctx=Load()),
                                args=[Str(s='abc')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True)])
