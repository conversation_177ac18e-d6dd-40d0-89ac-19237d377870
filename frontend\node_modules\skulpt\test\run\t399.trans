Module(body=[Import(names=[alias(name='random',
                                 asname=None)]),
             Expr(value=Call(func=Attribute(value=Name(id='random',
                                                       ctx=Load()),
                                            attr='seed',
                                            ctx=Load()),
                             args=[Num(n=0)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Str(s='randint')],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='randint',
                                               ctx=Load()),
                                args=[Num(n=4),
                                      Num(n=9)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='randint',
                                               ctx=Load()),
                                args=[Num(n=4),
                                      Num(n=9)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='randint',
                                               ctx=Load()),
                                args=[Num(n=4),
                                      Num(n=9)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='randint',
                                               ctx=Load()),
                                args=[Num(n=4),
                                      Num(n=9)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='randint',
                                               ctx=Load()),
                                args=[Num(n=4),
                                      Num(n=9)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='randint',
                                               ctx=Load()),
                                args=[Num(n=4),
                                      Num(n=9)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='randrange')],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='randrange',
                                               ctx=Load()),
                                args=[Num(n=4),
                                      Num(n=9)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='randrange',
                                               ctx=Load()),
                                args=[Num(n=4),
                                      Num(n=9)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='randrange',
                                               ctx=Load()),
                                args=[Num(n=4),
                                      Num(n=9)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='randrange',
                                               ctx=Load()),
                                args=[Num(n=4),
                                      Num(n=9)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='randrange',
                                               ctx=Load()),
                                args=[Num(n=4),
                                      Num(n=9)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='randrange',
                                               ctx=Load()),
                                args=[Num(n=4),
                                      Num(n=9)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='step -2')],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='randrange',
                                               ctx=Load()),
                                args=[Num(n=8),
                                      Num(n=-4),
                                      Num(n=-2)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='randrange',
                                               ctx=Load()),
                                args=[Num(n=8),
                                      Num(n=-4),
                                      Num(n=-2)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='randrange',
                                               ctx=Load()),
                                args=[Num(n=8),
                                      Num(n=-4),
                                      Num(n=-2)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='randrange',
                                               ctx=Load()),
                                args=[Num(n=8),
                                      Num(n=-4),
                                      Num(n=-2)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='randrange',
                                               ctx=Load()),
                                args=[Num(n=8),
                                      Num(n=-4),
                                      Num(n=-2)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='randrange',
                                               ctx=Load()),
                                args=[Num(n=8),
                                      Num(n=-4),
                                      Num(n=-2)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='randrange',
                                               ctx=Load()),
                                args=[Num(n=8),
                                      Num(n=-4),
                                      Num(n=-2)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='randrange',
                                               ctx=Load()),
                                args=[Num(n=8),
                                      Num(n=-4),
                                      Num(n=-2)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='step 3')],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='randrange',
                                               ctx=Load()),
                                args=[Num(n=5),
                                      Num(n=15),
                                      Num(n=3)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='randrange',
                                               ctx=Load()),
                                args=[Num(n=5),
                                      Num(n=15),
                                      Num(n=3)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='randrange',
                                               ctx=Load()),
                                args=[Num(n=5),
                                      Num(n=15),
                                      Num(n=3)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='randrange',
                                               ctx=Load()),
                                args=[Num(n=5),
                                      Num(n=15),
                                      Num(n=3)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='randrange',
                                               ctx=Load()),
                                args=[Num(n=5),
                                      Num(n=15),
                                      Num(n=3)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='randrange',
                                               ctx=Load()),
                                args=[Num(n=5),
                                      Num(n=15),
                                      Num(n=3)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='randrange',
                                               ctx=Load()),
                                args=[Num(n=5),
                                      Num(n=15),
                                      Num(n=3)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='randrange',
                                               ctx=Load()),
                                args=[Num(n=5),
                                      Num(n=15),
                                      Num(n=3)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='list')],
                   nl=True),
             Assign(targets=[Name(id='l',
                                  ctx=Store())],
                    value=Call(func=Name(id='range',
                                         ctx=Load()),
                               args=[Num(n=9)],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='choice',
                                               ctx=Load()),
                                args=[Name(id='l',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Expr(value=Call(func=Attribute(value=Name(id='random',
                                                       ctx=Load()),
                                            attr='shuffle',
                                            ctx=Load()),
                             args=[Name(id='l',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Name(id='l',
                                ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='choice',
                                               ctx=Load()),
                                args=[Name(id='l',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Expr(value=Call(func=Attribute(value=Name(id='random',
                                                       ctx=Load()),
                                            attr='shuffle',
                                            ctx=Load()),
                             args=[Name(id='l',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Name(id='l',
                                ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='choice',
                                               ctx=Load()),
                                args=[Name(id='l',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Expr(value=Call(func=Attribute(value=Name(id='random',
                                                       ctx=Load()),
                                            attr='shuffle',
                                            ctx=Load()),
                             args=[Name(id='l',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Name(id='l',
                                ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='choice',
                                               ctx=Load()),
                                args=[Name(id='l',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Expr(value=Call(func=Attribute(value=Name(id='random',
                                                       ctx=Load()),
                                            attr='shuffle',
                                            ctx=Load()),
                             args=[Name(id='l',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Name(id='l',
                                ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='choice',
                                               ctx=Load()),
                                args=[Name(id='l',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Expr(value=Call(func=Attribute(value=Name(id='random',
                                                       ctx=Load()),
                                            attr='shuffle',
                                            ctx=Load()),
                             args=[Name(id='l',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Name(id='l',
                                ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='choice',
                                               ctx=Load()),
                                args=[Name(id='l',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Expr(value=Call(func=Attribute(value=Name(id='random',
                                                       ctx=Load()),
                                            attr='shuffle',
                                            ctx=Load()),
                             args=[Name(id='l',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Name(id='l',
                                ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='choice',
                                               ctx=Load()),
                                args=[Name(id='l',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Expr(value=Call(func=Attribute(value=Name(id='random',
                                                       ctx=Load()),
                                            attr='shuffle',
                                            ctx=Load()),
                             args=[Name(id='l',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Name(id='l',
                                ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='choice',
                                               ctx=Load()),
                                args=[Name(id='l',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Expr(value=Call(func=Attribute(value=Name(id='random',
                                                       ctx=Load()),
                                            attr='shuffle',
                                            ctx=Load()),
                             args=[Name(id='l',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Name(id='l',
                                ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='choice',
                                               ctx=Load()),
                                args=[Name(id='l',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Expr(value=Call(func=Attribute(value=Name(id='random',
                                                       ctx=Load()),
                                            attr='shuffle',
                                            ctx=Load()),
                             args=[Name(id='l',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Name(id='l',
                                ctx=Load())],
                   nl=True)])
