Module(body=[Assign(targets=[Name(id='m',
                                  ctx=Store())],
                    value=List(elts=[List(elts=[Num(n=1),
                                                Num(n=2),
                                                Num(n=3)],
                                          ctx=Load()),
                                     Num(n=2),
                                     Num(n=3)],
                               ctx=Load())),
             Print(dest=None,
                   values=[Name(id='m',
                                ctx=Load())],
                   nl=True),
             Expr(value=Call(func=Attribute(value=Name(id='m',
                                                       ctx=Load()),
                                            attr='extend',
                                            ctx=Load()),
                             args=[Name(id='m',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Name(id='m',
                                ctx=Load())],
                   nl=True)])
