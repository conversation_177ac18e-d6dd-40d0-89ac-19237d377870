Sym_type: module
Sym_name: top
Sym_lineno: 0
Sym_nested: False
Sym_haschildren: True
-- Identifiers --
name: Base
  is_referenced: True
  is_imported: False
  is_parameter: False
  is_global: False
  is_declared_global: False
  is_local: True
  is_free: False
  is_assigned: True
  is_namespace: True
  namespaces: [
    Sym_type: class
    Sym_name: Base
    Sym_lineno: 1
    Sym_nested: False
    Sym_haschildren: True
    Class_methods: ['myfunc', 'stuff']
    -- Identifiers --
    name: myfunc
      is_referenced: False
      is_imported: False
      is_parameter: False
      is_global: False
      is_declared_global: False
      is_local: True
      is_free: False
      is_assigned: True
      is_namespace: True
      namespaces: [
        Sym_type: function
        Sym_name: myfunc
        Sym_lineno: 2
        Sym_nested: False
        Sym_haschildren: False
        Func_params: ['self']
        Func_locals: ['self']
        Func_globals: []
        Func_frees: []
        -- Identifiers --
        name: self
          is_referenced: False
          is_imported: False
          is_parameter: True
          is_global: False
          is_declared_global: False
          is_local: True
          is_free: False
          is_assigned: False
          is_namespace: False
          namespaces: [
          ]
      ]
    name: stuff
      is_referenced: False
      is_imported: False
      is_parameter: False
      is_global: False
      is_declared_global: False
      is_local: True
      is_free: False
      is_assigned: True
      is_namespace: True
      namespaces: [
        Sym_type: function
        Sym_name: stuff
        Sym_lineno: 5
        Sym_nested: False
        Sym_haschildren: False
        Func_params: ['self']
        Func_locals: ['self']
        Func_globals: []
        Func_frees: []
        -- Identifiers --
        name: self
          is_referenced: True
          is_imported: False
          is_parameter: True
          is_global: False
          is_declared_global: False
          is_local: True
          is_free: False
          is_assigned: False
          is_namespace: False
          namespaces: [
          ]
      ]
  ]
name: Derived
  is_referenced: True
  is_imported: False
  is_parameter: False
  is_global: False
  is_declared_global: False
  is_local: True
  is_free: False
  is_assigned: True
  is_namespace: True
  namespaces: [
    Sym_type: class
    Sym_name: Derived
    Sym_lineno: 9
    Sym_nested: False
    Sym_haschildren: True
    Class_methods: ['myfunc']
    -- Identifiers --
    name: myfunc
      is_referenced: False
      is_imported: False
      is_parameter: False
      is_global: False
      is_declared_global: False
      is_local: True
      is_free: False
      is_assigned: True
      is_namespace: True
      namespaces: [
        Sym_type: function
        Sym_name: myfunc
        Sym_lineno: 10
        Sym_nested: False
        Sym_haschildren: False
        Func_params: ['self']
        Func_locals: ['self']
        Func_globals: ['Base']
        Func_frees: []
        -- Identifiers --
        name: Base
          is_referenced: True
          is_imported: False
          is_parameter: False
          is_global: True
          is_declared_global: False
          is_local: False
          is_free: False
          is_assigned: False
          is_namespace: False
          namespaces: [
          ]
        name: self
          is_referenced: True
          is_imported: False
          is_parameter: True
          is_global: False
          is_declared_global: False
          is_local: True
          is_free: False
          is_assigned: False
          is_namespace: False
          namespaces: [
          ]
      ]
  ]
name: b
  is_referenced: True
  is_imported: False
  is_parameter: False
  is_global: False
  is_declared_global: False
  is_local: True
  is_free: False
  is_assigned: True
  is_namespace: False
  namespaces: [
  ]
name: d
  is_referenced: True
  is_imported: False
  is_parameter: False
  is_global: False
  is_declared_global: False
  is_local: True
  is_free: False
  is_assigned: True
  is_namespace: False
  namespaces: [
  ]
name: object
  is_referenced: True
  is_imported: False
  is_parameter: False
  is_global: True
  is_declared_global: False
  is_local: False
  is_free: False
  is_assigned: False
  is_namespace: False
  namespaces: [
  ]
