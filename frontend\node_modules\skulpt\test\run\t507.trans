Mo<PERSON>le(body=[Import(names=[alias(name='random',
                                 asname=None)]),
             Assign(targets=[Name(id='l',
                                  ctx=Store())],
                    value=List(elts=[Name(id='None',
                                          ctx=Load()),
                                     Call(func=Name(id='float',
                                                    ctx=Load()),
                                          args=[Str(s='-inf')],
                                          keywords=[],
                                          starargs=None,
                                          kwargs=None),
                                     Num(n=-1),
                                     Name(id='False',
                                          ctx=Load()),
                                     Num(n=0.1),
                                     Name(id='True',
                                          ctx=Load()),
                                     <PERSON>um(n=2.7),
                                     <PERSON>um(n=123456789123456789123456789),
                                     Call(func=Name(id='float',
                                                    ctx=Load()),
                                          args=[Str(s='inf')],
                                          keywords=[],
                                          starargs=None,
                                          kwargs=None),
                                     Dict(keys=[Num(n=1),
                                                Num(n=3)],
                                          values=[Num(n=2),
                                                  <PERSON>um(n=4)]),
                                     List(elts=[Num(n=1),
                                                Num(n=2),
                                                Num(n=3)],
                                          ctx=Load()),
                                     Str(s='hello'),
                                     Tuple(elts=[Num(n=1),
                                                 Num(n=2),
                                                 Num(n=3)],
                                           ctx=Load())],
                               ctx=Load())),
             For(target=Name(id='x',
                             ctx=Store()),
                 iter=Call(func=Name(id='range',
                                     ctx=Load()),
                           args=[Num(n=10)],
                           keywords=[],
                           starargs=None,
                           kwargs=None),
                 body=[Expr(value=Call(func=Attribute(value=Name(id='random',
                                                                 ctx=Load()),
                                                      attr='shuffle',
                                                      ctx=Load()),
                                       args=[Name(id='l',
                                                  ctx=Load())],
                                       keywords=[],
                                       starargs=None,
                                       kwargs=None)),
                       Expr(value=Call(func=Attribute(value=Name(id='l',
                                                                 ctx=Load()),
                                                      attr='sort',
                                                      ctx=Load()),
                                       args=[],
                                       keywords=[],
                                       starargs=None,
                                       kwargs=None)),
                       Print(dest=None,
                             values=[Name(id='l',
                                          ctx=Load())],
                             nl=True)],
                 orelse=[])])
