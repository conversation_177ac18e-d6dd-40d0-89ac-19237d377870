Module(body=[Print(dest=None,
                   values=[Compare(left=Num(n=0),
                                   ops=[Eq()],
                                   comparators=[Name(id='None',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Num(n=0.0),
                                   ops=[Eq()],
                                   comparators=[Name(id='None',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Num(n=0),
                                   ops=[Eq()],
                                   comparators=[Name(id='None',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='None',
                                             ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Num(n=0)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='None',
                                             ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Num(n=0.0)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='None',
                                             ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Num(n=0)])],
                   nl=True),
             Print(dest=None,
                   values=[],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Num(n=0),
                                   ops=[NotEq()],
                                   comparators=[Name(id='None',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Num(n=0.0),
                                   ops=[NotEq()],
                                   comparators=[Name(id='None',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Num(n=0),
                                   ops=[NotEq()],
                                   comparators=[Name(id='None',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='None',
                                             ctx=Load()),
                                   ops=[NotEq()],
                                   comparators=[Num(n=0)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='None',
                                             ctx=Load()),
                                   ops=[NotEq()],
                                   comparators=[Num(n=0.0)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='None',
                                             ctx=Load()),
                                   ops=[NotEq()],
                                   comparators=[Num(n=0)])],
                   nl=True)])
