Module(body=[FunctionDef(name='helper',
                         args=arguments(args=[Name(id='got',
                                                   ctx=Param()),
                                              Name(id='expect',
                                                   ctx=Param())],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[]),
                         body=[If(test=Compare(left=Name(id='got',
                                                         ctx=Load()),
                                               ops=[Eq()],
                                               comparators=[Name(id='expect',
                                                                 ctx=Load())]),
                                  body=[Print(dest=None,
                                              values=[Name(id='True',
                                                           ctx=Load())],
                                              nl=True)],
                                  orelse=[Print(dest=None,
                                                values=[Name(id='False',
                                                             ctx=Load()),
                                                        Name(id='expect',
                                                             ctx=Load()),
                                                        Name(id='got',
                                                             ctx=Load())],
                                                nl=True)])],
                         decorator_list=[]),
             Print(dest=None,
                   values=[Str(s='\nstr.count')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='abcd abcba '),
                                                       attr='count',
                                                       ctx=Load()),
                                        args=[Str(s='abc')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Num(n=2)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='abcd abcba '),
                                                       attr='count',
                                                       ctx=Load()),
                                        args=[Str(s='z')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Num(n=0)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='abcd abcba '),
                                                       attr='count',
                                                       ctx=Load()),
                                        args=[Str(s='abc'),
                                              Num(n=1)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Num(n=1)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='abcd abcba '),
                                                       attr='count',
                                                       ctx=Load()),
                                        args=[Str(s='abc'),
                                              Num(n=-1)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Num(n=0)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='abcd abcba '),
                                                       attr='count',
                                                       ctx=Load()),
                                        args=[Str(s='abc'),
                                              Num(n=5)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Num(n=1)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='abcd abcba '),
                                                       attr='count',
                                                       ctx=Load()),
                                        args=[Str(s='abc'),
                                              Num(n=-5)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Num(n=0)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='abcd abcba '),
                                                       attr='count',
                                                       ctx=Load()),
                                        args=[Str(s='abc'),
                                              Num(n=1),
                                              Num(n=8)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Num(n=1)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='abcd abcba '),
                                                       attr='count',
                                                       ctx=Load()),
                                        args=[Str(s='abc'),
                                              Num(n=-6),
                                              Num(n=-3)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Num(n=1)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='abcd abcba '),
                                                       attr='count',
                                                       ctx=Load()),
                                        args=[Str(s='abc'),
                                              Num(n=4),
                                              Num(n=-1)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Num(n=1)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='abcd abcba '),
                                                       attr='count',
                                                       ctx=Load()),
                                        args=[Str(s='abc'),
                                              Num(n=-6),
                                              Num(n=10)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Num(n=1)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='abcd abcda '),
                                                       attr='count',
                                                       ctx=Load()),
                                        args=[Str(s='ad'),
                                              Num(n=-6),
                                              Num(n=-3)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Num(n=0)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='abcd abcba '),
                                                       attr='count',
                                                       ctx=Load()),
                                        args=[Str(s='a'),
                                              Num(n=-6),
                                              Num(n=-6)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Num(n=0)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='abcd abcba '),
                                                       attr='count',
                                                       ctx=Load()),
                                        args=[Str(s='a'),
                                              Num(n=6),
                                              Num(n=-7)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Num(n=0)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='abcd abcba '),
                                                       attr='count',
                                                       ctx=Load()),
                                        args=[Str(s='a'),
                                              Num(n=3),
                                              Num(n=1)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Num(n=0)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='abcd abcba '),
                                                       attr='count',
                                                       ctx=Load()),
                                        args=[Str(s='a'),
                                              Num(n=-100),
                                              Num(n=100)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Num(n=3)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='abcd abcba '),
                                                       attr='count',
                                                       ctx=Load()),
                                        args=[Str(s='a'),
                                              Num(n=100),
                                              Num(n=-100)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Num(n=0)],
                             keywords=[],
                             starargs=None,
                             kwargs=None))])
