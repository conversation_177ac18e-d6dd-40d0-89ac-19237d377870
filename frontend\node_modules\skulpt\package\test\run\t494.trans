Module(body=[Import(names=[alias(name='re',
                                 asname=None)]),
             FunctionDef(name='helper',
                         args=arguments(args=[Name(id='match',
                                                   ctx=Param()),
                                              Name(id='expected',
                                                   ctx=Param())],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[]),
                         body=[If(test=Compare(left=Call(func=Name(id='type',
                                                                   ctx=Load()),
                                                         args=[Name(id='expected',
                                                                    ctx=Load())],
                                                         keywords=[],
                                                         starargs=None,
                                                         kwargs=None),
                                               ops=[Eq()],
                                               comparators=[Name(id='str',
                                                                 ctx=Load())]),
                                  body=[If(test=Name(id='match',
                                                     ctx=Load()),
                                           body=[If(test=Compare(left=Call(func=Attribute(value=Name(id='match',
                                                                                                     ctx=Load()),
                                                                                          attr='group',
                                                                                          ctx=Load()),
                                                                           args=[Num(n=0)],
                                                                           keywords=[],
                                                                           starargs=None,
                                                                           kwargs=None),
                                                                 ops=[Eq()],
                                                                 comparators=[Name(id='expected',
                                                                                   ctx=Load())]),
                                                    body=[Print(dest=None,
                                                                values=[Name(id='True',
                                                                             ctx=Load())],
                                                                nl=True)],
                                                    orelse=[Print(dest=None,
                                                                  values=[Call(func=Attribute(value=Name(id='match',
                                                                                                         ctx=Load()),
                                                                                              attr='group',
                                                                                              ctx=Load()),
                                                                               args=[Num(n=0)],
                                                                               keywords=[],
                                                                               starargs=None,
                                                                               kwargs=None),
                                                                          Name(id='expected',
                                                                               ctx=Load())],
                                                                  nl=True)])],
                                           orelse=[Print(dest=None,
                                                         values=[Str(s="didn't get a match")],
                                                         nl=True)])],
                                  orelse=[If(test=Name(id='match',
                                                       ctx=Load()),
                                             body=[Print(dest=None,
                                                         values=[Compare(left=Name(id='True',
                                                                                   ctx=Load()),
                                                                         ops=[Eq()],
                                                                         comparators=[Name(id='expected',
                                                                                           ctx=Load())])],
                                                         nl=True)],
                                             orelse=[Print(dest=None,
                                                           values=[Compare(left=Name(id='False',
                                                                                     ctx=Load()),
                                                                           ops=[Eq()],
                                                                           comparators=[Name(id='expected',
                                                                                             ctx=Load())])],
                                                           nl=True)])])],
                         decorator_list=[]),
             Print(dest=None,
                   values=[Str(s='\nSyntax: .')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='.'),
                                              Str(s='')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Name(id='False',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='.'),
                                              Str(s='a')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Name(id='True',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='.a'),
                                              Str(s='a')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Name(id='False',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='a'),
                                              Str(s='a')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Name(id='True',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='a.'),
                                              Str(s='a\n')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Name(id='False',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='.a'),
                                              Str(s='ba')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Name(id='True',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Str(s='\nSyntax: ^')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='^'),
                                              Str(s='')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Name(id='True',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='a^'),
                                              Str(s='')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Name(id='False',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='^a'),
                                              Str(s='ba')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Name(id='False',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='^a'),
                                              Str(s='ab')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Name(id='True',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='^a'),
                                              Str(s='\na')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Name(id='False',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='a^'),
                                              Str(s='a')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Name(id='False',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Str(s='\nSyntax: $')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='$'),
                                              Str(s='')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Name(id='True',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='$a'),
                                              Str(s='a')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Name(id='False',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='a$'),
                                              Str(s='a')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Name(id='True',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='a$'),
                                              Str(s='ab')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Name(id='False',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='a$'),
                                              Str(s='a\nb')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Name(id='False',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='a$'),
                                              Str(s='a\n')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Name(id='True',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Str(s='\nSyntax: *')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='a*'),
                                              Str(s='')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='ab*'),
                                              Str(s='a')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='a')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='ab*'),
                                              Str(s='ab')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='ab')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='ab*'),
                                              Str(s='abbbbb')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='abbbbb')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='ab*'),
                                              Str(s='ba')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Name(id='False',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='ab*'),
                                              Str(s='bbbb')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Name(id='False',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Str(s='\nSyntax: +')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='a+'),
                                              Str(s='')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Name(id='False',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='ab+'),
                                              Str(s='a')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Name(id='False',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='ab+'),
                                              Str(s='ab')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='ab')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='ab+'),
                                              Str(s='abbbbb')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='abbbbb')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='ab+'),
                                              Str(s='ba')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Name(id='False',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='ab+'),
                                              Str(s='bbbb')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Name(id='False',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Str(s='\nSyntax: ?')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='a?'),
                                              Str(s='')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='ab?'),
                                              Str(s='a')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='a')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='ab?'),
                                              Str(s='ab')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='ab')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='ab?'),
                                              Str(s='abbbbb')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='ab')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='ab?'),
                                              Str(s='ba')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Name(id='False',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='ab?'),
                                              Str(s='bbbb')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Name(id='False',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Str(s='\nSyntax: *?')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='a*?'),
                                              Str(s='a')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='ab*?'),
                                              Str(s='abbbb')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='a')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='ab*?'),
                                              Str(s='a')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='a')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='ab*?'),
                                              Str(s='')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Name(id='False',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Str(s='\nSyntax: +?')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='a+?'),
                                              Str(s='a')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='a')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='ab+?'),
                                              Str(s='abbbb')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='ab')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='ab+?'),
                                              Str(s='a')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Name(id='False',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='ab+?'),
                                              Str(s='')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Name(id='False',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Str(s='\nSyntax: ??')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='a??'),
                                              Str(s='a')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='ab??'),
                                              Str(s='abbbb')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='a')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='ab??'),
                                              Str(s='a')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='a')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='ab??'),
                                              Str(s='')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Name(id='False',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Str(s='\nSyntax: {m}')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='a{2}'),
                                              Str(s='a')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Name(id='False',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='a{2}'),
                                              Str(s='aa')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='aa')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='a{2}'),
                                              Str(s='aaa')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='aa')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Str(s='\nSyntax: {m,n}')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='a{1,2}b'),
                                              Str(s='b')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Name(id='False',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='a{1,2}b'),
                                              Str(s='ab')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='ab')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='a{1,2}b'),
                                              Str(s='aab')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='aab')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='a{1,2}b'),
                                              Str(s='aaab')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Name(id='False',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='a{,2}b'),
                                              Str(s='b')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='b')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='a{,2}b'),
                                              Str(s='ab')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='ab')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='a{,2}b'),
                                              Str(s='aab')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='aab')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='a{,2}b'),
                                              Str(s='aaab')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Name(id='False',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='a{2,}b'),
                                              Str(s='b')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Name(id='False',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='a{2,}b'),
                                              Str(s='ab')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Name(id='False',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='a{2,}b'),
                                              Str(s='aab')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='aab')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='a{2,}b'),
                                              Str(s='aaab')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='aaab')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='a{3,5}'),
                                              Str(s='aaaaaaaaaa')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='aaaaa')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='a{,5}'),
                                              Str(s='aaaaaaaaaa')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='aaaaa')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='a{3,}'),
                                              Str(s='aaaaaaaaaa')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='aaaaaaaaaa')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Str(s='\nSyntax: {m,n}?')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='a{1,2}?b'),
                                              Str(s='b')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Name(id='False',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='a{1,2}?b'),
                                              Str(s='ab')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='ab')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='a{1,2}?b'),
                                              Str(s='aab')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='aab')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='a{1,2}?b'),
                                              Str(s='aaab')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Name(id='False',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='a{,2}?b'),
                                              Str(s='b')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='b')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='a{,2}?b'),
                                              Str(s='ab')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='ab')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='a{,2}?b'),
                                              Str(s='aab')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='aab')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='a{,2}?b'),
                                              Str(s='aaab')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Name(id='False',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='a{2,}?b'),
                                              Str(s='b')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Name(id='False',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='a{2,}?b'),
                                              Str(s='ab')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Name(id='False',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='a{2,}?b'),
                                              Str(s='aab')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='aab')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='a{2,}?b'),
                                              Str(s='aaab')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='aaab')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='a{3,5}?'),
                                              Str(s='aaaaaaaaaa')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='aaa')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='a{,5}?'),
                                              Str(s='aaaaaaaaaa')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='a{3,}?'),
                                              Str(s='aaaaaaaaaa')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='aaa')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Str(s='\nSyntax: []')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='[a,b,c]'),
                                              Str(s='abc')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='a')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='[a-z]'),
                                              Str(s='bc')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='b')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='[A-Z,0-9]'),
                                              Str(s='abcdefg')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Name(id='False',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='[^A-Z]'),
                                              Str(s='ABCDEFGaHIJKL')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Name(id='False',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='[a*bc]'),
                                              Str(s='*')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='*')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Str(s='\nSyntax: |')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='|'),
                                              Str(s='')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='|a'),
                                              Str(s='')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='a|b'),
                                              Str(s='ba')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='b')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='h|ello'),
                                              Str(s='hello')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='h')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Str(s='\nSyntax: (...)')],
                   nl=True),
             Assign(targets=[Name(id='match',
                                  ctx=Store())],
                    value=Call(func=Attribute(value=Name(id='re',
                                                         ctx=Load()),
                                              attr='match',
                                              ctx=Load()),
                               args=[Str(s='(b*)'),
                                     Str(s='bbbba')],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Compare(left=Call(func=Name(id='len',
                                                       ctx=Load()),
                                             args=[Call(func=Attribute(value=Name(id='match',
                                                                                  ctx=Load()),
                                                                       attr='groups',
                                                                       ctx=Load()),
                                                        args=[],
                                                        keywords=[],
                                                        starargs=None,
                                                        kwargs=None)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Num(n=1)])],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\nSyntax: (?...)')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='(?:b*)'),
                                              Str(s='bbbba')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='bbbb')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='a(?=b)'),
                                              Str(s='a')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Name(id='False',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='a(?=b)'),
                                              Str(s='ab')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='a')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='a(?!b)'),
                                              Str(s='a')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='a')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Name(id='re',
                                                                  ctx=Load()),
                                                       attr='match',
                                                       ctx=Load()),
                                        args=[Str(s='a(?!b)'),
                                              Str(s='ab')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Name(id='False',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None))])
