Module(body=[Import(names=[alias(name='math',
                                 asname=None)]),
             ClassDef(name='F',
                      bases=[],
                      body=[FunctionDef(name='__init__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Assign(targets=[Attribute(value=Name(id='self',
                                                                                   ctx=Load()),
                                                                        attr='a',
                                                                        ctx=Store())],
                                                     value=Num(n=1)),
                                              Assign(targets=[Attribute(value=Name(id='self',
                                                                                   ctx=Load()),
                                                                        attr='b',
                                                                        ctx=Store())],
                                                     value=Num(n=2)),
                                              Assign(targets=[Attribute(value=Name(id='self',
                                                                                   ctx=Load()),
                                                                        attr='d',
                                                                        ctx=Store())],
                                                     value=Num(n=4))],
                                        decorator_list=[])],
                      decorator_list=[]),
             Assign(targets=[Name(id='f',
                                  ctx=Store())],
                    value=Call(func=Name(id='F',
                                         ctx=Load()),
                               args=[],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Call(func=Name(id='hasattr',
                                          ctx=Load()),
                                args=[Name(id='f',
                                           ctx=Load()),
                                      Str(s='a')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='hasattr',
                                          ctx=Load()),
                                args=[Name(id='f',
                                           ctx=Load()),
                                      Str(s='c')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='hasattr',
                                          ctx=Load()),
                                args=[Name(id='f',
                                           ctx=Load()),
                                      Str(s='D')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             TryExcept(body=[Print(dest=None,
                                   values=[Call(func=Name(id='hasattr',
                                                          ctx=Load()),
                                                args=[Name(id='f',
                                                           ctx=Load()),
                                                      Name(id='b',
                                                           ctx=Load())],
                                                keywords=[],
                                                starargs=None,
                                                kwargs=None)],
                                   nl=True),
                             Print(dest=None,
                                   values=[Str(s="You shouldn't see this.")],
                                   nl=True)],
                       handlers=[ExceptHandler(type=None,
                                               name=None,
                                               body=[Print(dest=None,
                                                           values=[Call(func=Name(id='hasattr',
                                                                                  ctx=Load()),
                                                                        args=[Name(id='f',
                                                                                   ctx=Load()),
                                                                              Str(s='b')],
                                                                        keywords=[],
                                                                        starargs=None,
                                                                        kwargs=None)],
                                                           nl=True)])],
                       orelse=[]),
             Print(dest=None,
                   values=[],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='hasattr',
                                          ctx=Load()),
                                args=[Name(id='str',
                                           ctx=Load()),
                                      Str(s='center')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='hasattr',
                                          ctx=Load()),
                                args=[Name(id='str',
                                           ctx=Load()),
                                      Str(s='ljust')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='hasattr',
                                          ctx=Load()),
                                args=[Name(id='math',
                                           ctx=Load()),
                                      Str(s='pi')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='hasattr',
                                          ctx=Load()),
                                args=[Name(id='math',
                                           ctx=Load()),
                                      Str(s='tau')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             TryExcept(body=[Print(dest=None,
                                   values=[Call(func=Name(id='hasattr',
                                                          ctx=Load()),
                                                args=[Name(id='math',
                                                           ctx=Load()),
                                                      Name(id='None',
                                                           ctx=Load())],
                                                keywords=[],
                                                starargs=None,
                                                kwargs=None)],
                                   nl=True),
                             Print(dest=None,
                                   values=[Str(s="You shouldn't see this.")],
                                   nl=True)],
                       handlers=[ExceptHandler(type=None,
                                               name=None,
                                               body=[Print(dest=None,
                                                           values=[Call(func=Name(id='hasattr',
                                                                                  ctx=Load()),
                                                                        args=[Name(id='F',
                                                                                   ctx=Load()),
                                                                              Str(s='a')],
                                                                        keywords=[],
                                                                        starargs=None,
                                                                        kwargs=None)],
                                                           nl=True)])],
                       orelse=[])])
