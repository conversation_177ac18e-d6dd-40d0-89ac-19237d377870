Module(body=[ClassDef(name='Foo',
                      bases=[],
                      body=[FunctionDef(name='__init__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param()),
                                                             Name(id='x',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Assign(targets=[Attribute(value=Name(id='self',
                                                                                   ctx=Load()),
                                                                        attr='lst',
                                                                        ctx=Store())],
                                                     value=List(elts=[Name(id='x',
                                                                           ctx=Load())],
                                                                ctx=Load()))],
                                        decorator_list=[]),
                            FunctionDef(name='__eq__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param()),
                                                             Name(id='other',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Return(value=Compare(left=Attribute(value=Name(id='self',
                                                                                             ctx=Load()),
                                                                                  attr='lst',
                                                                                  ctx=Load()),
                                                                   ops=[Eq()],
                                                                   comparators=[Attribute(value=Name(id='other',
                                                                                                     ctx=Load()),
                                                                                          attr='lst',
                                                                                          ctx=Load())]))],
                                        decorator_list=[])],
                      decorator_list=[]),
             Assign(targets=[Name(id='f1',
                                  ctx=Store())],
                    value=Call(func=Name(id='Foo',
                                         ctx=Load()),
                               args=[Num(n=3)],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Assign(targets=[Name(id='f2',
                                  ctx=Store())],
                    value=Call(func=Name(id='Foo',
                                         ctx=Load()),
                               args=[Num(n=3)],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Assign(targets=[Name(id='f3',
                                  ctx=Store())],
                    value=Call(func=Name(id='Foo',
                                         ctx=Load()),
                               args=[Num(n=4)],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Compare(left=Name(id='f1',
                                             ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Name(id='f1',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='f1',
                                             ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Name(id='f2',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='f1',
                                             ctx=Load()),
                                   ops=[NotEq()],
                                   comparators=[Name(id='f3',
                                                     ctx=Load())])],
                   nl=True),

