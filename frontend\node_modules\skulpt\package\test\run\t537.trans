Module(body=[TryExcept(body=[Expr(value=Call(func=Name(id='min',
                                                       ctx=Load()),
                                             args=[Num(n=3)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None))],
                       handlers=[ExceptHandler(type=Name(id='TypeError',
                                                         ctx=Load()),
                                               name=None,
                                               body=[Print(dest=None,
                                                           values=[Str(s='min(3) raises type error')],
                                                           nl=True)])],
                       orelse=[]),
             TryExcept(body=[Expr(value=Call(func=Name(id='max',
                                                       ctx=Load()),
                                             args=[Num(n=3)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None))],
                       handlers=[ExceptHandler(type=Name(id='TypeError',
                                                         ctx=Load()),
                                               name=None,
                                               body=[Print(dest=None,
                                                           values=[Str(s='max(3) raises type error')],
                                                           nl=True)])],
                       orelse=[]),
             TryExcept(body=[Expr(value=Call(func=Name(id='min',
                                                       ctx=Load()),
                                             args=[List(elts=[],
                                                        ctx=Load())],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None))],
                       handlers=[ExceptHandler(type=Name(id='ValueError',
                                                         ctx=Load()),
                                               name=None,
                                               body=[Print(dest=None,
                                                           values=[Str(s='min([]) raises value error')],
                                                           nl=True)])],
                       orelse=[]),
             TryExcept(body=[Expr(value=Call(func=Name(id='max',
                                                       ctx=Load()),
                                             args=[Call(func=Name(id='tuple',
                                                                  ctx=Load()),
                                                        args=[],
                                                        keywords=[],
                                                        starargs=None,
                                                        kwargs=None)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None))],
                       handlers=[ExceptHandler(type=Name(id='ValueError',
                                                         ctx=Load()),
                                               name=None,
                                               body=[Print(dest=None,
                                                           values=[Str(s='max(tuple()) raises value error')],
                                                           nl=True)])],
                       orelse=[]),
             Print(dest=None,
                   values=[Call(func=Name(id='max',
                                          ctx=Load()),
                                args=[GeneratorExp(elt=Name(id='i',
                                                            ctx=Load()),
                                                   generators=[comprehension(target=Name(id='i',
                                                                                         ctx=Store()),
                                                                             iter=Call(func=Name(id='range',
                                                                                                 ctx=Load()),
                                                                                       args=[Num(n=7)],
                                                                                       keywords=[],
                                                                                       starargs=None,
                                                                                       kwargs=None),
                                                                             ifs=[])])],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='min',
                                          ctx=Load()),
                                args=[GeneratorExp(elt=Name(id='j',
                                                            ctx=Load()),
                                                   generators=[comprehension(target=Name(id='j',
                                                                                         ctx=Store()),
                                                                             iter=Call(func=Name(id='range',
                                                                                                 ctx=Load()),
                                                                                       args=[Num(n=4),
                                                                                             Num(n=1),
                                                                                             Num(n=-1)],
                                                                                       keywords=[],
                                                                                       starargs=None,
                                                                                       kwargs=None),
                                                                             ifs=[])])],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True)])
