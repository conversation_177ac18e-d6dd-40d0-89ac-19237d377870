Module(body=[Print(dest=None,
                   values=[Call(func=Name(id='repr',
                                          ctx=Load()),
                                args=[Num(n=1)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='repr',
                                          ctx=Load()),
                                args=[Num(n=100)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='repr',
                                          ctx=Load()),
                                args=[Num(n=1.5)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='repr',
                                          ctx=Load()),
                                args=[List(elts=[],
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='repr',
                                          ctx=Load()),
                                args=[List(elts=[Num(n=1),
                                                 Num(n=2),
                                                 Num(n=3),
                                                 Num(n=4)],
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='repr',
                                          ctx=Load()),
                                args=[Tuple(elts=[],
                                            ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='repr',
                                          ctx=Load()),
                                args=[Tuple(elts=[Num(n=1),
                                                  Num(n=2),
                                                  Num(n=3),
                                                  Num(n=4)],
                                            ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='repr',
                                          ctx=Load()),
                                args=[Dict(keys=[],
                                           values=[])],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='repr',
                                          ctx=Load()),
                                args=[Dict(keys=[Num(n=1),
                                                 Num(n=3)],
                                           values=[Num(n=2),
                                                   Num(n=4)])],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='repr',
                                          ctx=Load()),
                                args=[Str(s='')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='repr',
                                          ctx=Load()),
                                args=[Str(s='hello world')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='repr',
                                          ctx=Load()),
                                args=[Call(func=Name(id='object',
                                                     ctx=Load()),
                                           args=[],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             ClassDef(name='A',
                      bases=[Name(id='object',
                                  ctx=Load())],
                      body=[FunctionDef(name='__init__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Pass()],
                                        decorator_list=[])],
                      decorator_list=[]),
             Print(dest=None,
                   values=[Call(func=Name(id='repr',
                                          ctx=Load()),
                                args=[Call(func=Name(id='A',
                                                     ctx=Load()),
                                           args=[],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             ClassDef(name='B',
                      bases=[],
                      body=[FunctionDef(name='__init__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Pass()],
                                        decorator_list=[]),
                            FunctionDef(name='__repr__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Return(value=Str(s='custom repr'))],
                                        decorator_list=[])],
                      decorator_list=[]),
             Print(dest=None,
                   values=[Call(func=Name(id='repr',
                                          ctx=Load()),
                                args=[Call(func=Name(id='B',
                                                     ctx=Load()),
                                           args=[],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True)])
