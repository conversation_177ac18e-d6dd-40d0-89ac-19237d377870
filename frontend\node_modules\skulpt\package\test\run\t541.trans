Module(body=[ClassDef(name='A',
                      bases=[Name(id='object',
                                  ctx=Load())],
                      body=[Assign(targets=[Name(id='message',
                                                 ctx=Store())],
                                   value=Str(s='a')),
                            FunctionDef(name='test',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Print(dest=None,
                                                    values=[BinOp(left=Str(s='a>'),
                                                                  op=Add(),
                                                                  right=Attribute(value=Attribute(value=Name(id='self',
                                                                                                             ctx=Load()),
                                                                                                  attr='__class__',
                                                                                                  ctx=Load()),
                                                                                  attr='__name__',
                                                                                  ctx=Load()))],
                                                    nl=True)],
                                        decorator_list=[])],
                      decorator_list=[]),
             ClassDef(name='B',
                      bases=[Name(id='object',
                                  ctx=Load())],
                      body=[FunctionDef(name='test',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Print(dest=None,
                                                    values=[BinOp(left=Str(s='b>'),
                                                                  op=Add(),
                                                                  right=Attribute(value=Attribute(value=Name(id='self',
                                                                                                             ctx=Load()),
                                                                                                  attr='__class__',
                                                                                                  ctx=Load()),
                                                                                  attr='__name__',
                                                                                  ctx=Load()))],
                                                    nl=True)],
                                        decorator_list=[])],
                      decorator_list=[]),
             ClassDef(name='C',
                      bases=[Name(id='A',
                                  ctx=Load()),
                             Name(id='B',
                                  ctx=Load())],
                      body=[FunctionDef(name='test',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Expr(value=Call(func=Attribute(value=Name(id='A',
                                                                                        ctx=Load()),
                                                                             attr='test',
                                                                             ctx=Load()),
                                                              args=[Name(id='self',
                                                                         ctx=Load())],
                                                              keywords=[],
                                                              starargs=None,
                                                              kwargs=None)),
                                              Expr(value=Call(func=Attribute(value=Name(id='B',
                                                                                        ctx=Load()),
                                                                             attr='test',
                                                                             ctx=Load()),
                                                              args=[Name(id='self',
                                                                         ctx=Load())],
                                                              keywords=[],
                                                              starargs=None,
                                                              kwargs=None))],
                                        decorator_list=[])],
                      decorator_list=[]),
             Expr(value=Call(func=Attribute(value=Call(func=Name(id='C',
                                                                 ctx=Load()),
                                                       args=[],
                                                       keywords=[],
                                                       starargs=None,
                                                       kwargs=None),
                                            attr='test',
                                            ctx=Load()),
                             args=[],
                             keywords=[],
                             starargs=None,
                             kwargs=None))])
