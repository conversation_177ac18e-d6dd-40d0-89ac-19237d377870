Mo<PERSON><PERSON>(body=[Assign(targets=[Name(id='a',
                                  ctx=Store())],
                    value=List(elts=[Num(n=47),
                                     <PERSON><PERSON>(n=65),
                                     <PERSON><PERSON>(n=50),
                                     <PERSON><PERSON>(n=12),
                                     <PERSON><PERSON>(n=38),
                                     <PERSON><PERSON>(n=55),
                                     <PERSON><PERSON>(n=61),
                                     <PERSON><PERSON>(n=35),
                                     <PERSON><PERSON>(n=28),
                                     <PERSON><PERSON>(n=46),
                                     <PERSON><PERSON>(n=54),
                                     <PERSON><PERSON>(n=14),
                                     <PERSON><PERSON>(n=40),
                                     <PERSON><PERSON>(n=51),
                                     <PERSON><PERSON>(n=1),
                                     <PERSON><PERSON>(n=21),
                                     <PERSON><PERSON>(n=16),
                                     <PERSON><PERSON>(n=22),
                                     <PERSON><PERSON>(n=35),
                                     <PERSON>um(n=9),
                                     <PERSON><PERSON>(n=57),
                                     <PERSON><PERSON>(n=52),
                                     <PERSON><PERSON>(n=44),
                                     <PERSON><PERSON>(n=18),
                                     <PERSON><PERSON>(n=8),
                                     <PERSON><PERSON>(n=4),
                                     <PERSON>um(n=58),
                                     <PERSON>um(n=55),
                                     <PERSON>um(n=42),
                                     <PERSON><PERSON>(n=23),
                                     <PERSON><PERSON>(n=5),
                                     <PERSON><PERSON>(n=61),
                                     <PERSON><PERSON>(n=2),
                                     <PERSON><PERSON>(n=20),
                                     <PERSON><PERSON>(n=58),
                                     <PERSON><PERSON>(n=28),
                                     <PERSON><PERSON>(n=54),
                                     <PERSON><PERSON>(n=60),
                                     <PERSON><PERSON>(n=44),
                                     <PERSON><PERSON>(n=4),
                                     <PERSON><PERSON>(n=57),
                                     <PERSON><PERSON>(n=53),
                                     <PERSON><PERSON>(n=40),
                                     Num(n=33),
                                     Num(n=54),
                                     Num(n=48),
                                     Num(n=2),
                                     Num(n=20),
                                     Num(n=34),
                                     Num(n=66),
                                     Num(n=48),
                                     Num(n=39),
                                     Num(n=47),
                                     Num(n=25),
                                     Num(n=17),
                                     Num(n=2),
                                     Num(n=35),
                                     Num(n=4),
                                     Num(n=58),
                                     Num(n=63),
                                     Num(n=37),
                                     Num(n=61),
                                     Num(n=47),
                                     Num(n=51)],
                               ctx=Load())),
             Expr(value=Call(func=Attribute(value=Name(id='a',
                                                       ctx=Load()),
                                            attr='sort',
                                            ctx=Load()),
                             args=[],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Name(id='a',
                                ctx=Load())],
                   nl=True),
             Assign(targets=[Name(id='b',
                                  ctx=Store())],
                    value=List(elts=[Num(n=0),
                                     Num(n=17),
                                     Num(n=11),
                                     Num(n=37),
                                     Num(n=13),
                                     Num(n=39),
                                     Num(n=13),
                                     Num(n=14),
                                     Num(n=13),
                                     Num(n=29),
                                     Num(n=32),
                                     Num(n=35),
                                     Num(n=9),
                                     Num(n=4),
                                     Num(n=20),
                                     Num(n=20),
                                     Num(n=15),
                                     Num(n=23),
                                     Num(n=26),
                                     Num(n=24),
                                     Num(n=32),
                                     Num(n=16),
                                     Num(n=2),
                                     Num(n=37),
                                     Num(n=17),
                                     Num(n=41),
                                     Num(n=25),
                                     Num(n=33),
                                     Num(n=37),
                                     Num(n=9),
                                     Num(n=35),
                                     Num(n=41),
                                     Num(n=1),
                                     Num(n=21),
                                     Num(n=17),
                                     Num(n=1),
                                     Num(n=16),
                                     Num(n=17),
                                     Num(n=9),
                                     Num(n=17),
                                     Num(n=5),
                                     Num(n=25),
                                     Num(n=25),
                                     Num(n=24),
                                     Num(n=8),
                                     Num(n=23),
                                     Num(n=11),
                                     Num(n=0),
                                     Num(n=30),
                                     Num(n=26),
                                     Num(n=2),
                                     Num(n=6),
                                     Num(n=27),
                                     Num(n=30),
                                     Num(n=14),
                                     Num(n=14),
                                     Num(n=19),
                                     Num(n=40),
                                     Num(n=14),
                                     Num(n=15),
                                     Num(n=3),
                                     Num(n=19),
                                     Num(n=29),
                                     Num(n=38),
                                     Num(n=4),
                                     Num(n=19),
                                     Num(n=16)],
                               ctx=Load())),
             Expr(value=Call(func=Attribute(value=Name(id='b',
                                                       ctx=Load()),
                                            attr='sort',
                                            ctx=Load()),
                             args=[],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Name(id='b',
                                ctx=Load())],
                   nl=True),
             Assign(targets=[Name(id='c',
                                  ctx=Store())],
                    value=List(elts=[Num(n=93),
                                     Num(n=79),
                                     Num(n=89),
                                     Num(n=55),
                                     Num(n=1),
                                     Num(n=66),
                                     Num(n=17),
                                     Num(n=80),
                                     Num(n=22),
                                     Num(n=37),
                                     Num(n=5),
                                     Num(n=83),
                                     Num(n=86),
                                     Num(n=34),
                                     Num(n=29),
                                     Num(n=41),
                                     Num(n=52),
                                     Num(n=93),
                                     Num(n=43),
                                     Num(n=82),
                                     Num(n=76),
                                     Num(n=19),
                                     Num(n=62),
                                     Num(n=57),
                                     Num(n=30),
                                     Num(n=74),
                                     Num(n=22),
                                     Num(n=32),
                                     Num(n=29),
                                     Num(n=50),
                                     Num(n=68),
                                     Num(n=73),
                                     Num(n=38),
                                     Num(n=25),
                                     Num(n=16),
                                     Num(n=61),
                                     Num(n=32),
                                     Num(n=88),
                                     Num(n=28),
                                     Num(n=31),
                                     Num(n=4),
                                     Num(n=3),
                                     Num(n=81),
                                     Num(n=76),
                                     Num(n=29),
                                     Num(n=38),
                                     Num(n=63),
                                     Num(n=39),
                                     Num(n=76),
                                     Num(n=75),
                                     Num(n=62),
                                     Num(n=61),
                                     Num(n=72),
                                     Num(n=79),
                                     Num(n=63),
                                     Num(n=12),
                                     Num(n=93),
                                     Num(n=50),
                                     Num(n=6),
                                     Num(n=46),
                                     Num(n=54),
                                     Num(n=33),
                                     Num(n=70),
                                     Num(n=33),
                                     Num(n=57),
                                     Num(n=1),
                                     Num(n=26)],
                               ctx=Load())),
             Expr(value=Call(func=Attribute(value=Name(id='c',
                                                       ctx=Load()),
                                            attr='sort',
                                            ctx=Load()),
                             args=[],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Name(id='c',
                                ctx=Load())],
                   nl=True)])
