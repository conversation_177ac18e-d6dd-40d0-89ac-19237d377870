Module(body=[Import(names=[alias(name='random',
                                 asname=None)]),
             While(test=Compare(left=Call(func=Attribute(value=Name(id='random',
                                                                    ctx=Load()),
                                                         attr='randrange',
                                                         ctx=Load()),
                                          args=[Num(n=10)],
                                          keywords=[],
                                          starargs=None,
                                          kwargs=None),
                                ops=[NotEq()],
                                comparators=[Num(n=8)]),
                   body=[Pass()],
                   orelse=[]),
             Print(dest=None,
                   values=[Str(s='8')],
                   nl=True)])
