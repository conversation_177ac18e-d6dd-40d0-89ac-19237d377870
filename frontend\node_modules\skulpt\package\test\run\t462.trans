Module(body=[FunctionDef(name='helper',
                         args=arguments(args=[Name(id='func',
                                                   ctx=Param()),
                                              Name(id='x',
                                                   ctx=Param())],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[]),
                         body=[Print(dest=None,
                                     values=[Call(func=Name(id='func',
                                                            ctx=Load()),
                                                  args=[Name(id='x',
                                                             ctx=Load())],
                                                  keywords=[],
                                                  starargs=None,
                                                  kwargs=None),
                                             Call(func=Name(id='func',
                                                            ctx=Load()),
                                                  args=[UnaryOp(op=USub(),
                                                                operand=Name(id='x',
                                                                             ctx=Load()))],
                                                  keywords=[],
                                                  starargs=None,
                                                  kwargs=None),
                                             Call(func=Name(id='func',
                                                            ctx=Load()),
                                                  args=[Call(func=Name(id='long',
                                                                       ctx=Load()),
                                                             args=[Name(id='x',
                                                                        ctx=Load())],
                                                             keywords=[],
                                                             starargs=None,
                                                             kwargs=None)],
                                                  keywords=[],
                                                  starargs=None,
                                                  kwargs=None),
                                             Call(func=Name(id='func',
                                                            ctx=Load()),
                                                  args=[UnaryOp(op=USub(),
                                                                operand=Call(func=Name(id='long',
                                                                                       ctx=Load()),
                                                                             args=[Name(id='x',
                                                                                        ctx=Load())],
                                                                             keywords=[],
                                                                             starargs=None,
                                                                             kwargs=None))],
                                                  keywords=[],
                                                  starargs=None,
                                                  kwargs=None)],
                                     nl=True)],
                         decorator_list=[]),
             Assign(targets=[Name(id='big',
                                  ctx=Store())],
                    value=Num(n=123456789123456789123456789123456789)),
             Print(dest=None,
                   values=[Str(s='\nHEX')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Name(id='hex',
                                        ctx=Load()),
                                   Num(n=10)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Name(id='hex',
                                        ctx=Load()),
                                   Num(n=255)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Name(id='hex',
                                        ctx=Load()),
                                   Num(n=28)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Name(id='hex',
                                        ctx=Load()),
                                   Num(n=115)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Name(id='hex',
                                        ctx=Load()),
                                   Name(id='big',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Str(s='\nOCT')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Name(id='oct',
                                        ctx=Load()),
                                   Num(n=10)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Name(id='oct',
                                        ctx=Load()),
                                   Num(n=255)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Name(id='oct',
                                        ctx=Load()),
                                   Num(n=28)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Name(id='oct',
                                        ctx=Load()),
                                   Num(n=115)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Name(id='oct',
                                        ctx=Load()),
                                   Name(id='big',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Str(s='\nBIN')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Name(id='bin',
                                        ctx=Load()),
                                   Num(n=10)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Name(id='bin',
                                        ctx=Load()),
                                   Num(n=255)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Name(id='bin',
                                        ctx=Load()),
                                   Num(n=28)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Name(id='bin',
                                        ctx=Load()),
                                   Num(n=115)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Name(id='bin',
                                        ctx=Load()),
                                   Name(id='big',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None))])
