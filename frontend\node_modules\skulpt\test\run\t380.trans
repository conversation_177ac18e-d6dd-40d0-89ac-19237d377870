Module(body=[Assign(targets=[Name(id='lst',
                                  ctx=Store())],
                    value=List(elts=[Num(n=1),
                                     Num(n=2)],
                               ctx=Load())),
             Assign(targets=[Name(id='lst',
                                  ctx=Store())],
                    value=BinOp(left=Name(id='lst',
                                          ctx=Load()),
                                op=Add(),
                                right=Num(n=1))),
             Print(dest=None,
                   values=[Name(id='lst',
                                ctx=Load())],
                   nl=True)])
