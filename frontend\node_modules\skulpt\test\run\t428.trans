Module(body=[Print(dest=None,
                   values=[Compare(left=BoolOp(op=Or(),
                                               values=[Name(id='True',
                                                            ctx=Load()),
                                                       Name(id='False',
                                                            ctx=Load())]),
                                   ops=[Eq()],
                                   comparators=[Name(id='True',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=BoolOp(op=Or(),
                                               values=[Name(id='True',
                                                            ctx=Load()),
                                                       Name(id='True',
                                                            ctx=Load())]),
                                   ops=[Eq()],
                                   comparators=[Name(id='True',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=BoolOp(op=Or(),
                                               values=[Name(id='False',
                                                            ctx=Load()),
                                                       Name(id='False',
                                                            ctx=Load())]),
                                   ops=[Eq()],
                                   comparators=[Name(id='False',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=BoolOp(op=And(),
                                               values=[Name(id='True',
                                                            ctx=Load()),
                                                       Name(id='False',
                                                            ctx=Load())]),
                                   ops=[Eq()],
                                   comparators=[Name(id='False',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=BoolOp(op=And(),
                                               values=[Name(id='True',
                                                            ctx=Load()),
                                                       Name(id='True',
                                                            ctx=Load())]),
                                   ops=[Eq()],
                                   comparators=[Name(id='True',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=BoolOp(op=And(),
                                               values=[Name(id='False',
                                                            ctx=Load()),
                                                       Name(id='False',
                                                            ctx=Load())]),
                                   ops=[Eq()],
                                   comparators=[Name(id='False',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=UnaryOp(op=Not(),
                                                operand=Name(id='True',
                                                             ctx=Load())),
                                   ops=[Eq()],
                                   comparators=[Name(id='False',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=UnaryOp(op=Not(),
                                                operand=Name(id='False',
                                                             ctx=Load())),
                                   ops=[Eq()],
                                   comparators=[Name(id='True',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=BoolOp(op=Or(),
                                               values=[UnaryOp(op=Not(),
                                                               operand=Name(id='True',
                                                                            ctx=Load())),
                                                       Name(id='False',
                                                            ctx=Load())]),
                                   ops=[Eq()],
                                   comparators=[BoolOp(op=Or(),
                                                       values=[UnaryOp(op=Not(),
                                                                       operand=Name(id='True',
                                                                                    ctx=Load())),
                                                               Name(id='False',
                                                                    ctx=Load())])])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=BoolOp(op=Or(),
                                               values=[UnaryOp(op=Not(),
                                                               operand=Name(id='False',
                                                                            ctx=Load())),
                                                       Name(id='False',
                                                            ctx=Load())]),
                                   ops=[Eq()],
                                   comparators=[BoolOp(op=Or(),
                                                       values=[UnaryOp(op=Not(),
                                                                       operand=Name(id='False',
                                                                                    ctx=Load())),
                                                               Name(id='False',
                                                                    ctx=Load())])])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=BoolOp(op=And(),
                                               values=[UnaryOp(op=Not(),
                                                               operand=Name(id='True',
                                                                            ctx=Load())),
                                                       Name(id='True',
                                                            ctx=Load())]),
                                   ops=[Eq()],
                                   comparators=[BoolOp(op=And(),
                                                       values=[UnaryOp(op=Not(),
                                                                       operand=Name(id='True',
                                                                                    ctx=Load())),
                                                               Name(id='True',
                                                                    ctx=Load())])])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=BoolOp(op=And(),
                                               values=[UnaryOp(op=Not(),
                                                               operand=Name(id='False',
                                                                            ctx=Load())),
                                                       Name(id='True',
                                                            ctx=Load())]),
                                   ops=[Eq()],
                                   comparators=[BoolOp(op=And(),
                                                       values=[UnaryOp(op=Not(),
                                                                       operand=Name(id='False',
                                                                                    ctx=Load())),
                                                               Name(id='True',
                                                                    ctx=Load())])])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=BoolOp(op=Or(),
                                               values=[BoolOp(op=And(),
                                                              values=[UnaryOp(op=Not(),
                                                                              operand=Name(id='True',
                                                                                           ctx=Load())),
                                                                      UnaryOp(op=Not(),
                                                                              operand=Name(id='False',
                                                                                           ctx=Load()))]),
                                                       Name(id='False',
                                                            ctx=Load())]),
                                   ops=[Eq()],
                                   comparators=[BoolOp(op=Or(),
                                                       values=[BoolOp(op=And(),
                                                                      values=[UnaryOp(op=Not(),
                                                                                      operand=Name(id='True',
                                                                                                   ctx=Load())),
                                                                              UnaryOp(op=Not(),
                                                                                      operand=Name(id='False',
                                                                                                   ctx=Load()))]),
                                                               Name(id='False',
                                                                    ctx=Load())])])],
                   nl=True)])
