Module(body=[FunctionDef(name='default_outside',
                         args=arguments(args=[Name(id='x',
                                                   ctx=Param())],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[List(elts=[],
                                                       ctx=Load())]),
                         body=[Return(value=Name(id='x',
                                                 ctx=Load()))],
                         decorator_list=[]),
             Assign(targets=[Name(id='a',
                                  ctx=Store())],
                    value=Call(func=Name(id='default_outside',
                                         ctx=Load()),
                               args=[],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Expr(value=Call(func=Attribute(value=Name(id='a',
                                                       ctx=Load()),
                                            attr='append',
                                            ctx=Load()),
                             args=[Num(n=1)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Name(id='a',
                                ctx=Load())],
                   nl=True),
             Assign(targets=[Name(id='b',
                                  ctx=Store())],
                    value=Call(func=Name(id='default_outside',
                                         ctx=Load()),
                               args=[],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Expr(value=Call(func=Attribute(value=Name(id='b',
                                                       ctx=Load()),
                                            attr='append',
                                            ctx=Load()),
                             args=[Num(n=2)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Name(id='b',
                                ctx=Load())],
                   nl=True)])
