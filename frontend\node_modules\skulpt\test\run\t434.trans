Module(body=[Assign(targets=[Name(id='l',
                                  ctx=Store())],
                    value=BinOp(left=List(elts=[Num(n=0)],
                                          ctx=Load()),
                                op=Mult(),
                                right=Num(n=5))),
             Assign(targets=[Subscript(value=Name(id='l',
                                                  ctx=Load()),
                                       slice=Index(value=Num(n=1)),
                                       ctx=Store())],
                    value=Num(n=1)),
             Print(dest=None,
                   values=[Compare(left=Subscript(value=Name(id='l',
                                                             ctx=Load()),
                                                  slice=Index(value=Num(n=1)),
                                                  ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Num(n=1)])],
                   nl=True),
             Assign(targets=[Subscript(value=Name(id='l',
                                                  ctx=Load()),
                                       slice=Slice(lower=Num(n=2),
                                                   upper=Num(n=5),
                                                   step=None),
                                       ctx=Store())],
                    value=Call(func=Name(id='range',
                                         ctx=Load()),
                               args=[Num(n=2),
                                     Num(n=5)],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[BoolOp(op=And(),
                                  values=[Compare(left=Subscript(value=Name(id='l',
                                                                            ctx=Load()),
                                                                 slice=Index(value=Num(n=2)),
                                                                 ctx=Load()),
                                                  ops=[Eq()],
                                                  comparators=[Num(n=2)]),
                                          Compare(left=Subscript(value=Name(id='l',
                                                                            ctx=Load()),
                                                                 slice=Index(value=Num(n=3)),
                                                                 ctx=Load()),
                                                  ops=[Eq()],
                                                  comparators=[Num(n=3)]),
                                          Compare(left=Subscript(value=Name(id='l',
                                                                            ctx=Load()),
                                                                 slice=Index(value=Num(n=4)),
                                                                 ctx=Load()),
                                                  ops=[Eq()],
                                                  comparators=[Num(n=4)])])],
                   nl=True),
             Delete(targets=[Subscript(value=Name(id='l',
                                                  ctx=Load()),
                                       slice=Slice(lower=Num(n=2),
                                                   upper=Num(n=5),
                                                   step=None),
                                       ctx=Del())]),
             Print(dest=None,
                   values=[Compare(left=Call(func=Name(id='len',
                                                       ctx=Load()),
                                             args=[Name(id='l',
                                                        ctx=Load())],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Num(n=2)])],
                   nl=True),
             Assign(targets=[Name(id='l',
                                  ctx=Store())],
                    value=BinOp(left=List(elts=[Num(n=0)],
                                          ctx=Load()),
                                op=Mult(),
                                right=Num(n=5))),
             Assign(targets=[Subscript(value=Name(id='l',
                                                  ctx=Load()),
                                       slice=Slice(lower=Num(n=0),
                                                   upper=Num(n=5),
                                                   step=Num(n=2)),
                                       ctx=Store())],
                    value=Call(func=Name(id='range',
                                         ctx=Load()),
                               args=[Num(n=0),
                                     Num(n=5),
                                     Num(n=2)],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Assign(targets=[Subscript(value=Name(id='l',
                                                  ctx=Load()),
                                       slice=Slice(lower=Num(n=1),
                                                   upper=Num(n=4),
                                                   step=Num(n=2)),
                                       ctx=Store())],
                    value=Call(func=Name(id='range',
                                         ctx=Load()),
                               args=[Num(n=1),
                                     Num(n=4),
                                     Num(n=2)],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Compare(left=Name(id='l',
                                             ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Call(func=Name(id='list',
                                                               ctx=Load()),
                                                     args=[Call(func=Name(id='range',
                                                                          ctx=Load()),
                                                                args=[Num(n=5)],
                                                                keywords=[],
                                                                starargs=None,
                                                                kwargs=None)],
                                                     keywords=[],
                                                     starargs=None,
                                                     kwargs=None)])],
                   nl=True),
             Delete(targets=[Subscript(value=Name(id='l',
                                                  ctx=Load()),
                                       slice=Slice(lower=Num(n=0),
                                                   upper=Num(n=5),
                                                   step=Num(n=2)),
                                       ctx=Del())]),
             Print(dest=None,
                   values=[Compare(left=Name(id='l',
                                             ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[List(elts=[Num(n=1),
                                                           Num(n=3)],
                                                     ctx=Load())])],
                   nl=True),
             Expr(value=Call(func=Attribute(value=Name(id='l',
                                                       ctx=Load()),
                                            attr='append',
                                            ctx=Load()),
                             args=[Num(n=5)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Compare(left=Name(id='l',
                                             ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[List(elts=[Num(n=1),
                                                           Num(n=3),
                                                           Num(n=5)],
                                                     ctx=Load())])],
                   nl=True),
             Expr(value=Call(func=Attribute(value=Name(id='l',
                                                       ctx=Load()),
                                            attr='extend',
                                            ctx=Load()),
                             args=[List(elts=[Num(n=7),
                                              Num(n=9)],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Compare(left=Name(id='l',
                                             ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[List(elts=[Num(n=1),
                                                           Num(n=3),
                                                           Num(n=5),
                                                           Num(n=7),
                                                           Num(n=9)],
                                                     ctx=Load())])],
                   nl=True),
             Expr(value=Call(func=Attribute(value=Name(id='l',
                                                       ctx=Load()),
                                            attr='extend',
                                            ctx=Load()),
                             args=[Name(id='l',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Compare(left=Name(id='l',
                                             ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[List(elts=[Num(n=1),
                                                           Num(n=3),
                                                           Num(n=5),
                                                           Num(n=7),
                                                           Num(n=9),
                                                           Num(n=1),
                                                           Num(n=3),
                                                           Num(n=5),
                                                           Num(n=7),
                                                           Num(n=9)],
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Attribute(value=Name(id='l',
                                                                       ctx=Load()),
                                                            attr='count',
                                                            ctx=Load()),
                                             args=[Num(n=3)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Num(n=2)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Attribute(value=Name(id='l',
                                                                       ctx=Load()),
                                                            attr='count',
                                                            ctx=Load()),
                                             args=[Num(n=2)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Num(n=0)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Attribute(value=Name(id='l',
                                                                       ctx=Load()),
                                                            attr='index',
                                                            ctx=Load()),
                                             args=[Num(n=3)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Num(n=1)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Call(func=Attribute(value=Name(id='l',
                                                                       ctx=Load()),
                                                            attr='index',
                                                            ctx=Load()),
                                             args=[Num(n=9)],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   ops=[Eq()],
                                   comparators=[Num(n=4)])],
                   nl=True),
             Expr(value=Call(func=Attribute(value=Name(id='l',
                                                       ctx=Load()),
                                            attr='pop',
                                            ctx=Load()),
                             args=[Num(n=0)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Compare(left=Name(id='l',
                                             ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[List(elts=[Num(n=3),
                                                           Num(n=5),
                                                           Num(n=7),
                                                           Num(n=9),
                                                           Num(n=1),
                                                           Num(n=3),
                                                           Num(n=5),
                                                           Num(n=7),
                                                           Num(n=9)],
                                                     ctx=Load())])],
                   nl=True),
             Expr(value=Call(func=Attribute(value=Name(id='l',
                                                       ctx=Load()),
                                            attr='remove',
                                            ctx=Load()),
                             args=[Num(n=3)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Compare(left=Name(id='l',
                                             ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[List(elts=[Num(n=5),
                                                           Num(n=7),
                                                           Num(n=9),
                                                           Num(n=1),
                                                           Num(n=3),
                                                           Num(n=5),
                                                           Num(n=7),
                                                           Num(n=9)],
                                                     ctx=Load())])],
                   nl=True),
             Expr(value=Call(func=Attribute(value=Name(id='l',
                                                       ctx=Load()),
                                            attr='reverse',
                                            ctx=Load()),
                             args=[],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Compare(left=Name(id='l',
                                             ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[List(elts=[Num(n=9),
                                                           Num(n=7),
                                                           Num(n=5),
                                                           Num(n=3),
                                                           Num(n=1),
                                                           Num(n=9),
                                                           Num(n=7),
                                                           Num(n=5)],
                                                     ctx=Load())])],
                   nl=True),
             Expr(value=Call(func=Attribute(value=Name(id='l',
                                                       ctx=Load()),
                                            attr='sort',
                                            ctx=Load()),
                             args=[],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Compare(left=Name(id='l',
                                             ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[List(elts=[Num(n=1),
                                                           Num(n=3),
                                                           Num(n=5),
                                                           Num(n=5),
                                                           Num(n=7),
                                                           Num(n=7),
                                                           Num(n=9),
                                                           Num(n=9)],
                                                     ctx=Load())])],
                   nl=True),
             Expr(value=Call(func=Attribute(value=Name(id='l',
                                                       ctx=Load()),
                                            attr='sort',
                                            ctx=Load()),
                             args=[Lambda(args=arguments(args=[Name(id='x',
                                                                    ctx=Param()),
                                                               Name(id='y',
                                                                    ctx=Param())],
                                                         vararg=None,
                                                         kwarg=None,
                                                         defaults=[]),
                                          body=BinOp(left=Name(id='y',
                                                               ctx=Load()),
                                                     op=Sub(),
                                                     right=Name(id='x',
                                                                ctx=Load())))],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Compare(left=Name(id='l',
                                             ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[List(elts=[Num(n=9),
                                                           Num(n=9),
                                                           Num(n=7),
                                                           Num(n=7),
                                                           Num(n=5),
                                                           Num(n=5),
                                                           Num(n=3),
                                                           Num(n=1)],
                                                     ctx=Load())])],
                   nl=True)])
