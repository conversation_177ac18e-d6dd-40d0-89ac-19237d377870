Module(body=[ClassDef(name='MyTest',
                      bases=[],
                      body=[FunctionDef(name='__init__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param()),
                                                             Name(id='s',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Assign(targets=[Attribute(value=Name(id='self',
                                                                                   ctx=Load()),
                                                                        attr='w',
                                                                        ctx=Store())],
                                                     value=Name(id='s',
                                                                ctx=Load()))],
                                        decorator_list=[]),
                            FunctionDef(name='length',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Return(value=Call(func=Name(id='len',
                                                                          ctx=Load()),
                                                                args=[Attribute(value=Name(id='self',
                                                                                           ctx=Load()),
                                                                                attr='w',
                                                                                ctx=Load())],
                                                                keywords=[],
                                                                starargs=None,
                                                                kwargs=None))],
                                        decorator_list=[])],
                      decorator_list=[]),
             Assign(targets=[Name(id='x',
                                  ctx=Store())],
                    value=Call(func=Name(id='MyTest',
                                         ctx=Load()),
                               args=[Str(s='foo')],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='x',
                                                          ctx=Load()),
                                               attr='length',
                                               ctx=Load()),
                                args=[],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True)])
