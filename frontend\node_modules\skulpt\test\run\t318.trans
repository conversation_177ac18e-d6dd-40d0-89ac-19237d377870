Module(body=[Assign(targets=[Name(id='fpexp',
                                  ctx=Store())],
                    value=Str(s='( 1 + 1 )')),
             Assign(targets=[Name(id='fplist',
                                  ctx=Store())],
                    value=Call(func=Attribute(value=Name(id='fpexp',
                                                         ctx=Load()),
                                              attr='split',
                                              ctx=Load()),
                               args=[],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             For(target=Name(id='i',
                             ctx=Store()),
                 iter=Name(id='fplist',
                           ctx=Load()),
                 body=[Print(dest=None,
                             values=[Name(id='i',
                                          ctx=Load())],
                             nl=True)],
                 orelse=[])])
