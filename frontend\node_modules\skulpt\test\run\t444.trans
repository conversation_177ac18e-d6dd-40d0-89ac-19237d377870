Module(body=[Global(names=['a']),
             Assign(targets=[Name(id='a',
                                  ctx=Store())],
                    value=Call(func=Name(id='set',
                                         ctx=Load()),
                               args=[List(elts=[],
                                          ctx=Load())],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Assign(targets=[Name(id='b',
                                  ctx=Store())],
                    value=Call(func=Name(id='set',
                                         ctx=Load()),
                               args=[List(elts=[],
                                          ctx=Load())],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             FunctionDef(name='A',
                         args=arguments(args=[Name(id='x',
                                                   ctx=Param())],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[]),
                         body=[Expr(value=Call(func=Attribute(value=Name(id='a',
                                                                         ctx=Load()),
                                                              attr='add',
                                                              ctx=Load()),
                                               args=[Name(id='x',
                                                          ctx=Load())],
                                               keywords=[],
                                               starargs=None,
                                               kwargs=None))],
                         decorator_list=[]),
             FunctionDef(name='B',
                         args=arguments(args=[Name(id='x',
                                                   ctx=Param())],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[]),
                         body=[Expr(value=Call(func=Attribute(value=Name(id='b',
                                                                         ctx=Load()),
                                                              attr='update',
                                                              ctx=Load()),
                                               args=[Name(id='a',
                                                          ctx=Load())],
                                               keywords=[],
                                               starargs=None,
                                               kwargs=None)),
                               Expr(value=Call(func=Attribute(value=Name(id='b',
                                                                         ctx=Load()),
                                                              attr='add',
                                                              ctx=Load()),
                                               args=[Name(id='x',
                                                          ctx=Load())],
                                               keywords=[],
                                               starargs=None,
                                               kwargs=None))],
                         decorator_list=[]),
             Expr(value=Call(func=Name(id='A',
                                       ctx=Load()),
                             args=[Num(n=5)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='A',
                                       ctx=Load()),
                             args=[Num(n=6)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='B',
                                       ctx=Load()),
                             args=[Num(n=4)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='a: '),
                           Name(id='a',
                                ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='b: '),
                           Name(id='b',
                                ctx=Load())],
                   nl=True),
             FunctionDef(name='C',
                         args=arguments(args=[Name(id='x',
                                                   ctx=Param())],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[]),
                         body=[Global(names=['c']),
                               Assign(targets=[Name(id='c',
                                                    ctx=Store())],
                                      value=Call(func=Name(id='set',
                                                           ctx=Load()),
                                                 args=[List(elts=[],
                                                            ctx=Load())],
                                                 keywords=[],
                                                 starargs=None,
                                                 kwargs=None)),
                               FunctionDef(name='D',
                                           args=arguments(args=[Name(id='x',
                                                                     ctx=Param())],
                                                          vararg=None,
                                                          kwarg=None,
                                                          defaults=[]),
                                           body=[If(test=Compare(left=Name(id='x',
                                                                           ctx=Load()),
                                                                 ops=[NotIn()],
                                                                 comparators=[Name(id='b',
                                                                                   ctx=Load())]),
                                                    body=[Expr(value=Call(func=Attribute(value=Name(id='c',
                                                                                                    ctx=Load()),
                                                                                         attr='add',
                                                                                         ctx=Load()),
                                                                          args=[Name(id='x',
                                                                                     ctx=Load())],
                                                                          keywords=[],
                                                                          starargs=None,
                                                                          kwargs=None))],
                                                    orelse=[])],
                                           decorator_list=[]),
                               For(target=Name(id='n',
                                               ctx=Store()),
                                   iter=Call(func=Name(id='range',
                                                       ctx=Load()),
                                             args=[Name(id='x',
                                                        ctx=Load())],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   body=[Expr(value=Call(func=Name(id='D',
                                                                   ctx=Load()),
                                                         args=[Name(id='n',
                                                                    ctx=Load())],
                                                         keywords=[],
                                                         starargs=None,
                                                         kwargs=None))],
                                   orelse=[]),
                               Expr(value=Call(func=Attribute(value=Name(id='a',
                                                                         ctx=Load()),
                                                              attr='update',
                                                              ctx=Load()),
                                               args=[Name(id='c',
                                                          ctx=Load())],
                                               keywords=[],
                                               starargs=None,
                                               kwargs=None))],
                         decorator_list=[]),
             Expr(value=Call(func=Name(id='C',
                                       ctx=Load()),
                             args=[Num(n=10)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='a: '),
                           Name(id='a',
                                ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='b: '),
                           Name(id='b',
                                ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='c: '),
                           Name(id='c',
                                ctx=Load())],
                   nl=True),
             FunctionDef(name='D',
                         args=arguments(args=[Name(id='x',
                                                   ctx=Param())],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[]),
                         body=[Expr(value=Call(func=Attribute(value=Name(id='a',
                                                                         ctx=Load()),
                                                              attr='remove',
                                                              ctx=Load()),
                                               args=[Name(id='x',
                                                          ctx=Load())],
                                               keywords=[],
                                               starargs=None,
                                               kwargs=None)),
                               Expr(value=Call(func=Attribute(value=Name(id='b',
                                                                         ctx=Load()),
                                                              attr='update',
                                                              ctx=Load()),
                                               args=[Name(id='a',
                                                          ctx=Load())],
                                               keywords=[],
                                               starargs=None,
                                               kwargs=None)),
                               Expr(value=Call(func=Attribute(value=Name(id='a',
                                                                         ctx=Load()),
                                                              attr='intersection_update',
                                                              ctx=Load()),
                                               args=[Name(id='c',
                                                          ctx=Load())],
                                               keywords=[],
                                               starargs=None,
                                               kwargs=None))],
                         decorator_list=[]),
             Expr(value=Call(func=Name(id='D',
                                       ctx=Load()),
                             args=[Num(n=7)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='a: '),
                           Name(id='a',
                                ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='b: '),
                           Name(id='b',
                                ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='c: '),
                           Name(id='c',
                                ctx=Load())],
                   nl=True),
             FunctionDef(name='E',
                         args=arguments(args=[Name(id='x',
                                                   ctx=Param())],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[]),
                         body=[Expr(value=Call(func=Name(id='A',
                                                         ctx=Load()),
                                               args=[Name(id='x',
                                                          ctx=Load())],
                                               keywords=[],
                                               starargs=None,
                                               kwargs=None)),
                               Expr(value=Call(func=Name(id='B',
                                                         ctx=Load()),
                                               args=[Name(id='x',
                                                          ctx=Load())],
                                               keywords=[],
                                               starargs=None,
                                               kwargs=None)),
                               Expr(value=Call(func=Name(id='C',
                                                         ctx=Load()),
                                               args=[Name(id='x',
                                                          ctx=Load())],
                                               keywords=[],
                                               starargs=None,
                                               kwargs=None)),
                               Expr(value=Call(func=Name(id='D',
                                                         ctx=Load()),
                                               args=[Name(id='x',
                                                          ctx=Load())],
                                               keywords=[],
                                               starargs=None,
                                               kwargs=None))],
                         decorator_list=[]),
             Expr(value=Call(func=Name(id='E',
                                       ctx=Load()),
                             args=[Num(n=10)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='a: '),
                           Name(id='a',
                                ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='b: '),
                           Name(id='b',
                                ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='c: '),
                           Name(id='c',
                                ctx=Load())],
                   nl=True)])
