Module(body=[Assign(targets=[Name(id='myList',
                                  ctx=Store())],
                    value=List(elts=[Num(n=1),
                                     Num(n=2),
                                     Num(n=3),
                                     Str(s='foo'),
                                     Num(n=4),
                                     Num(n=5),
                                     Name(id='True',
                                          ctx=Load()),
                                     Name(id='False',
                                          ctx=Load())],
                               ctx=Load())),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='myList',
                                                          ctx=Load()),
                                               attr='index',
                                               ctx=Load()),
                                args=[Str(s='foo')],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Str(s='foo'),
                                   ops=[In()],
                                   comparators=[Name(id='myList',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='myList',
                                                          ctx=Load()),
                                               attr='index',
                                               ctx=Load()),
                                args=[Name(id='True',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Num(n=2),
                                   ops=[In()],
                                   comparators=[Name(id='myList',
                                                     ctx=Load())])],
                   nl=True)])
