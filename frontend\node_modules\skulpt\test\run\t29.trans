Module(body=[Assign(targets=[Name(id='z',
                                  ctx=Store())],
                    value=Num(n=0)),
             For(target=Name(id='x',
                             ctx=Store()),
                 iter=Call(func=Name(id='range',
                                     ctx=Load()),
                           args=[Num(n=1),
                                 Num(n=4)],
                           keywords=[],
                           starargs=None,
                           kwargs=None),
                 body=[AugAssign(target=Name(id='z',
                                             ctx=Store()),
                                 op=Add(),
                                 value=Name(id='x',
                                            ctx=Load()))],
                 orelse=[]),
             Print(dest=None,
                   values=[Name(id='z',
                                ctx=Load())],
                   nl=True)])
