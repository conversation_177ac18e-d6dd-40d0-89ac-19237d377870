Module(body=[Assign(targets=[Name(id='d',
                                  ctx=Store())],
                    value=Dict(keys=[Num(n=1),
                                     Num(n=3)],
                               values=[Num(n=2),
                                       Num(n=4)])),
             Assign(targets=[Name(id='d2',
                                  ctx=Store())],
                    value=Call(func=Name(id='dict',
                                         ctx=Load()),
                               args=[Name(id='d',
                                          ctx=Load())],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Name(id='d',
                                ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Name(id='d2',
                                ctx=Load())],
                   nl=True),
             Assign(targets=[Subscript(value=Name(id='d2',
                                                  ctx=Load()),
                                       slice=Index(value=Num(n=4)),
                                       ctx=Store())],
                    value=Num(n=6)),
             Print(dest=None,
                   values=[Name(id='d',
                                ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Name(id='d2',
                                ctx=Load())],
                   nl=True)])
