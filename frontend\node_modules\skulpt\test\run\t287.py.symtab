Sym_type: module
Sym_name: top
Sym_lineno: 0
Sym_nested: False
Sym_haschildren: True
-- Identifiers --
name: True
  is_referenced: True
  is_imported: False
  is_parameter: False
  is_global: True
  is_declared_global: False
  is_local: False
  is_free: False
  is_assigned: False
  is_namespace: False
  namespaces: [
  ]
name: f
  is_referenced: True
  is_imported: False
  is_parameter: False
  is_global: False
  is_declared_global: False
  is_local: True
  is_free: False
  is_assigned: True
  is_namespace: True
  namespaces: [
    Sym_type: function
    Sym_name: f
    Sym_lineno: 1
    Sym_nested: False
    Sym_haschildren: False
    Func_params: []
    Func_locals: []
    Func_globals: []
    Func_frees: []
    -- Identifiers --
  ]
name: g
  is_referenced: True
  is_imported: False
  is_parameter: False
  is_global: False
  is_declared_global: False
  is_local: True
  is_free: False
  is_assigned: True
  is_namespace: True
  namespaces: [
    Sym_type: function
    Sym_name: g
    Sym_lineno: 5
    Sym_nested: False
    Sym_haschildren: False
    Func_params: []
    Func_locals: []
    Func_globals: []
    Func_frees: []
    -- Identifiers --
  ]
name: h
  is_referenced: True
  is_imported: False
  is_parameter: False
  is_global: False
  is_declared_global: False
  is_local: True
  is_free: False
  is_assigned: True
  is_namespace: True
  namespaces: [
    Sym_type: function
    Sym_name: h
    Sym_lineno: 11
    Sym_nested: False
    Sym_haschildren: False
    Func_params: []
    Func_locals: []
    Func_globals: ['retval']
    Func_frees: []
    -- Identifiers --
    name: retval
      is_referenced: True
      is_imported: False
      is_parameter: False
      is_global: True
      is_declared_global: True
      is_local: True
      is_free: False
      is_assigned: True
      is_namespace: False
      namespaces: [
      ]
  ]
name: i
  is_referenced: False
  is_imported: False
  is_parameter: False
  is_global: False
  is_declared_global: False
  is_local: True
  is_free: False
  is_assigned: True
  is_namespace: False
  namespaces: [
  ]
name: range
  is_referenced: True
  is_imported: False
  is_parameter: False
  is_global: True
  is_declared_global: False
  is_local: False
  is_free: False
  is_assigned: False
  is_namespace: False
  namespaces: [
  ]
name: retval
  is_referenced: False
  is_imported: False
  is_parameter: False
  is_global: True
  is_declared_global: True
  is_local: True
  is_free: False
  is_assigned: True
  is_namespace: False
  namespaces: [
  ]
