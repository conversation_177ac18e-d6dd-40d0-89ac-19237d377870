Module(body=[FunctionDef(name='test',
                         args=arguments(args=[],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[]),
                         body=[Expr(value=Call(func=Name(id='quit',
                                                         ctx=Load()),
                                               args=[],
                                               keywords=[],
                                               starargs=None,
                                               kwargs=None))],
                         decorator_list=[]),
             Print(dest=None,
                   values=[Str(s='a')],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='b')],
                   nl=True),
             Expr(value=Call(func=Name(id='test',
                                       ctx=Load()),
                             args=[],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Str(s='c')],
                   nl=True)])
