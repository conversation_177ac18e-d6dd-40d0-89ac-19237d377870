Module(body=[ClassDef(name='A',
                      bases=[],
                      body=[FunctionDef(name='__getitem__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param()),
                                                             Name(id='slices',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Return(value=Name(id='slices',
                                                                ctx=Load()))],
                                        decorator_list=[])],
                      decorator_list=[]),
             Assign(targets=[Name(id='a',
                                  ctx=Store())],
                    value=Call(func=Name(id='A',
                                         ctx=Load()),
                               args=[],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Subscript(value=Name(id='a',
                                                ctx=Load()),
                                     slice=Index(value=Num(n=1)),
                                     ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Subscript(value=Name(id='a',
                                                ctx=Load()),
                                     slice=Slice(lower=Num(n=0),
                                                 upper=Num(n=2),
                                                 step=None),
                                     ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Subscript(value=Name(id='a',
                                                ctx=Load()),
                                     slice=Slice(lower=None,
                                                 upper=Num(n=2),
                                                 step=None),
                                     ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='slice',
                                          ctx=Load()),
                                args=[Num(n=2)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Subscript(value=Name(id='a',
                                                ctx=Load()),
                                     slice=Slice(lower=Num(n=1),
                                                 upper=None,
                                                 step=None),
                                     ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Subscript(value=Name(id='a',
                                                ctx=Load()),
                                     slice=Slice(lower=None,
                                                 upper=None,
                                                 step=None),
                                     ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Subscript(value=Name(id='a',
                                                ctx=Load()),
                                     slice=Slice(lower=None,
                                                 upper=None,
                                                 step=Name(id='None',
                                                           ctx=Load())),
                                     ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Subscript(value=Name(id='a',
                                                ctx=Load()),
                                     slice=Slice(lower=None,
                                                 upper=None,
                                                 step=Num(n=-1)),
                                     ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Subscript(value=Name(id='a',
                                                ctx=Load()),
                                     slice=ExtSlice(dims=[Index(value=Num(n=0)),
                                                          Slice(lower=Num(n=1),
                                                                upper=Num(n=2),
                                                                step=None)]),
                                     ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Subscript(value=Name(id='a',
                                                ctx=Load()),
                                     slice=ExtSlice(dims=[Slice(lower=Num(n=0),
                                                                upper=Num(n=2),
                                                                step=None),
                                                          Slice(lower=Num(n=2),
                                                                upper=Num(n=30),
                                                                step=Num(n=1))]),
                                     ctx=Load())],
                   nl=True),
             Assert(test=Compare(left=Subscript(value=Name(id='a',
                                                           ctx=Load()),
                                                slice=Index(value=Num(n=1)),
                                                ctx=Load()),
                                 ops=[Eq()],
                                 comparators=[Num(n=1)]),
                    msg=None),
             Assert(test=Compare(left=Subscript(value=Name(id='a',
                                                           ctx=Load()),
                                                slice=Slice(lower=Num(n=0),
                                                            upper=Num(n=2),
                                                            step=None),
                                                ctx=Load()),
                                 ops=[Eq()],
                                 comparators=[Call(func=Name(id='slice',
                                                             ctx=Load()),
                                                   args=[Num(n=0),
                                                         Num(n=2)],
                                                   keywords=[],
                                                   starargs=None,
                                                   kwargs=None)]),
                    msg=None),
             Assert(test=Compare(left=Subscript(value=Name(id='a',
                                                           ctx=Load()),
                                                slice=ExtSlice(dims=[Index(value=Num(n=0)),
                                                                     Slice(lower=Num(n=1),
                                                                           upper=Num(n=2),
                                                                           step=None)]),
                                                ctx=Load()),
                                 ops=[Eq()],
                                 comparators=[Tuple(elts=[Num(n=0),
                                                          Call(func=Name(id='slice',
                                                                         ctx=Load()),
                                                               args=[Num(n=1),
                                                                     Num(n=2)],
                                                               keywords=[],
                                                               starargs=None,
                                                               kwargs=None)],
                                                    ctx=Load())]),
                    msg=None),
             Assert(test=Compare(left=Subscript(value=Name(id='a',
                                                           ctx=Load()),
                                                slice=ExtSlice(dims=[Slice(lower=Num(n=0),
                                                                           upper=Num(n=2),
                                                                           step=None),
                                                                     Slice(lower=Num(n=2),
                                                                           upper=Num(n=30),
                                                                           step=Num(n=1))]),
                                                ctx=Load()),
                                 ops=[Eq()],
                                 comparators=[Tuple(elts=[Call(func=Name(id='slice',
                                                                         ctx=Load()),
                                                               args=[Num(n=0),
                                                                     Num(n=2)],
                                                               keywords=[],
                                                               starargs=None,
                                                               kwargs=None),
                                                          Call(func=Name(id='slice',
                                                                         ctx=Load()),
                                                               args=[Num(n=2),
                                                                     Num(n=30),
                                                                     Num(n=1)],
                                                               keywords=[],
                                                               starargs=None,
                                                               kwargs=None)],
                                                    ctx=Load())]),
                    msg=None),
             Assert(test=Compare(left=Call(func=Name(id='slice',
                                                     ctx=Load()),
                                           args=[Num(n=0),
                                                 Num(n=2)],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None),
                                 ops=[Eq()],
                                 comparators=[Call(func=Name(id='slice',
                                                             ctx=Load()),
                                                   args=[Num(n=0),
                                                         Num(n=2)],
                                                   keywords=[],
                                                   starargs=None,
                                                   kwargs=None)]),
                    msg=None),
             Assert(test=Compare(left=Call(func=Name(id='slice',
                                                     ctx=Load()),
                                           args=[Num(n=0),
                                                 Num(n=2)],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None),
                                 ops=[Lt()],
                                 comparators=[Call(func=Name(id='slice',
                                                             ctx=Load()),
                                                   args=[Num(n=1),
                                                         Num(n=2)],
                                                   keywords=[],
                                                   starargs=None,
                                                   kwargs=None)]),
                    msg=None),
             Assert(test=Compare(left=Call(func=Name(id='slice',
                                                     ctx=Load()),
                                           args=[Num(n=0),
                                                 Num(n=2)],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None),
                                 ops=[Lt()],
                                 comparators=[Call(func=Name(id='slice',
                                                             ctx=Load()),
                                                   args=[Num(n=1),
                                                         Num(n=1)],
                                                   keywords=[],
                                                   starargs=None,
                                                   kwargs=None)]),
                    msg=None),
             Assert(test=Compare(left=Call(func=Name(id='slice',
                                                     ctx=Load()),
                                           args=[Num(n=2)],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None),
                                 ops=[Lt()],
                                 comparators=[Call(func=Name(id='slice',
                                                             ctx=Load()),
                                                   args=[Num(n=0),
                                                         Num(n=2)],
                                                   keywords=[],
                                                   starargs=None,
                                                   kwargs=None)]),
                    msg=None),
             Assert(test=Compare(left=Call(func=Name(id='slice',
                                                     ctx=Load()),
                                           args=[Num(n=1),
                                                 Num(n=2)],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None),
                                 ops=[Lt()],
                                 comparators=[Call(func=Name(id='slice',
                                                             ctx=Load()),
                                                   args=[Num(n=1),
                                                         Num(n=2),
                                                         Num(n=3)],
                                                   keywords=[],
                                                   starargs=None,
                                                   kwargs=None)]),
                    msg=None),
             Assert(test=Compare(left=Call(func=Name(id='slice',
                                                     ctx=Load()),
                                           args=[Num(n=1),
                                                 Num(n=2),
                                                 Num(n=3)],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None),
                                 ops=[Lt()],
                                 comparators=[Call(func=Name(id='slice',
                                                             ctx=Load()),
                                                   args=[Num(n=1),
                                                         Num(n=2),
                                                         Num(n=4)],
                                                   keywords=[],
                                                   starargs=None,
                                                   kwargs=None)]),
                    msg=None),
             Assert(test=Compare(left=Call(func=Name(id='slice',
                                                     ctx=Load()),
                                           args=[Num(n=1),
                                                 Num(n=-1)],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None),
                                 ops=[Lt()],
                                 comparators=[Call(func=Name(id='slice',
                                                             ctx=Load()),
                                                   args=[Num(n=1),
                                                         Num(n=1)],
                                                   keywords=[],
                                                   starargs=None,
                                                   kwargs=None)]),
                    msg=None),
             Assert(test=Compare(left=Call(func=Name(id='slice',
                                                     ctx=Load()),
                                           args=[Num(n=0),
                                                 Num(n=1)],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None),
                                 ops=[Lt()],
                                 comparators=[Call(func=Name(id='slice',
                                                             ctx=Load()),
                                                   args=[Num(n=1),
                                                         Num(n=-1)],
                                                   keywords=[],
                                                   starargs=None,
                                                   kwargs=None)]),
                    msg=None),
             Assert(test=Compare(left=Subscript(value=Name(id='a',
                                                           ctx=Load()),
                                                slice=Index(value=Str(s='foo')),
                                                ctx=Load()),
                                 ops=[Eq()],
                                 comparators=[Str(s='foo')]),
                    msg=None),
             Assert(test=Compare(left=Attribute(value=Subscript(value=Name(id='a',
                                                                           ctx=Load()),
                                                                slice=Slice(lower=Str(s='foo'),
                                                                            upper=Tuple(elts=[Num(n=1),
                                                                                              Num(n=2)],
                                                                                        ctx=Load()),
                                                                            step=Name(id='True',
                                                                                      ctx=Load())),
                                                                ctx=Load()),
                                                attr='start',
                                                ctx=Load()),
                                 ops=[Eq()],
                                 comparators=[Str(s='foo')]),
                    msg=None),
             Assert(test=Compare(left=Attribute(value=Subscript(value=Name(id='a',
                                                                           ctx=Load()),
                                                                slice=Slice(lower=Str(s='foo'),
                                                                            upper=Tuple(elts=[Num(n=1),
                                                                                              Num(n=2)],
                                                                                        ctx=Load()),
                                                                            step=Name(id='True',
                                                                                      ctx=Load())),
                                                                ctx=Load()),
                                                attr='stop',
                                                ctx=Load()),
                                 ops=[Eq()],
                                 comparators=[Tuple(elts=[Num(n=1),
                                                          Num(n=2)],
                                                    ctx=Load())]),
                    msg=None),
             Assert(test=Compare(left=Attribute(value=Subscript(value=Name(id='a',
                                                                           ctx=Load()),
                                                                slice=Slice(lower=Str(s='foo'),
                                                                            upper=Tuple(elts=[Num(n=1),
                                                                                              Num(n=2)],
                                                                                        ctx=Load()),
                                                                            step=Name(id='True',
                                                                                      ctx=Load())),
                                                                ctx=Load()),
                                                attr='step',
                                                ctx=Load()),
                                 ops=[Eq()],
                                 comparators=[Name(id='True',
                                                   ctx=Load())]),
                    msg=None)])
