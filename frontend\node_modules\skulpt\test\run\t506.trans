Module(body=[Print(dest=None,
                   values=[Call(func=Name(id='sorted',
                                          ctx=Load()),
                                args=[List(elts=[Num(n=1),
                                                 Num(n=2),
                                                 Num(n=3)],
                                           ctx=Load()),
                                      Name(id='None',
                                           ctx=Load()),
                                      Name(id='None',
                                           ctx=Load())],
                                keywords=[keyword(arg='key',
                                                  value=Lambda(args=arguments(args=[Name(id='x',
                                                                                         ctx=Param())],
                                                                              vararg=None,
                                                                              kwarg=None,
                                                                              defaults=[]),
                                                               body=UnaryOp(op=USub(),
                                                                            operand=Name(id='x',
                                                                                         ctx=Load()))))],
                                starargs=None,
                                kwargs=None)],
                   nl=True)])
