Module(body=[ClassDef(name='A',
                      bases=[],
                      body=[FunctionDef(name='__len__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Return(value=Num(n=0))],
                                        decorator_list=[])],
                      decorator_list=[]),
             Print(dest=None,
                   values=[Call(func=Name(id='bool',
                                          ctx=Load()),
                                args=[Call(func=Name(id='A',
                                                     ctx=Load()),
                                           args=[],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             ClassDef(name='B',
                      bases=[],
                      body=[FunctionDef(name='__len__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Return(value=Name(id='False',
                                                                ctx=Load()))],
                                        decorator_list=[])],
                      decorator_list=[]),
             Print(dest=None,
                   values=[Call(func=Name(id='bool',
                                          ctx=Load()),
                                args=[Call(func=Name(id='B',
                                                     ctx=Load()),
                                           args=[],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             ClassDef(name='C',
                      bases=[],
                      body=[FunctionDef(name='__nonzero__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Return(value=Num(n=0))],
                                        decorator_list=[])],
                      decorator_list=[]),
             Print(dest=None,
                   values=[Call(func=Name(id='bool',
                                          ctx=Load()),
                                args=[Call(func=Name(id='C',
                                                     ctx=Load()),
                                           args=[],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             ClassDef(name='D',
                      bases=[],
                      body=[FunctionDef(name='__nonzero__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Return(value=Name(id='False',
                                                                ctx=Load()))],
                                        decorator_list=[])],
                      decorator_list=[]),
             Print(dest=None,
                   values=[Call(func=Name(id='bool',
                                          ctx=Load()),
                                args=[Call(func=Name(id='D',
                                                     ctx=Load()),
                                           args=[],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             ClassDef(name='E',
                      bases=[],
                      body=[FunctionDef(name='__len__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Return(value=Num(n=1))],
                                        decorator_list=[])],
                      decorator_list=[]),
             Print(dest=None,
                   values=[Call(func=Name(id='bool',
                                          ctx=Load()),
                                args=[Call(func=Name(id='E',
                                                     ctx=Load()),
                                           args=[],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             ClassDef(name='F',
                      bases=[],
                      body=[FunctionDef(name='__nonzero__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Return(value=Num(n=1))],
                                        decorator_list=[])],
                      decorator_list=[]),
             Print(dest=None,
                   values=[Call(func=Name(id='bool',
                                          ctx=Load()),
                                args=[Call(func=Name(id='F',
                                                     ctx=Load()),
                                           args=[],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             ClassDef(name='G',
                      bases=[],
                      body=[FunctionDef(name='__nonzero__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Return(value=Num(n=0))],
                                        decorator_list=[]),
                            FunctionDef(name='__len__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Return(value=Num(n=1))],
                                        decorator_list=[])],
                      decorator_list=[]),
             Print(dest=None,
                   values=[Call(func=Name(id='bool',
                                          ctx=Load()),
                                args=[Call(func=Name(id='G',
                                                     ctx=Load()),
                                           args=[],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True)]),
             Print(dest=None,
                   values=[Call(func=Name(id='bool',
                                          ctx=Load()),
                                args=[Name(id='A',
                                                     ctx=Load()),
                                           args=[],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True)
             Print(dest=None,
                   values=[Call(func=Name(id='bool',
                                          ctx=Load()),
                                args=[Name(id='B',
                                                     ctx=Load()),
                                           args=[],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True)
             Print(dest=None,
                   values=[Call(func=Name(id='bool',
                                          ctx=Load()),
                                args=[Name(id='C',
                                                     ctx=Load()),
                                           args=[],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True)
             Print(dest=None,
                   values=[Call(func=Name(id='bool',
                                          ctx=Load()),
                                args=[Name(id='D',
                                                     ctx=Load()),
                                           args=[],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True)
             Print(dest=None,
                   values=[Call(func=Name(id='bool',
                                          ctx=Load()),
                                args=[Name(id='E',
                                                     ctx=Load()),
                                           args=[],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True)
             Print(dest=None,
                   values=[Call(func=Name(id='bool',
                                          ctx=Load()),
                                args=[Name(id='F',
                                                     ctx=Load()),
                                           args=[],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True)
             Print(dest=None,
                   values=[Call(func=Name(id='bool',
                                          ctx=Load()),
                                args=[Name(id='G',
                                                     ctx=Load()),
                                           args=[],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True)