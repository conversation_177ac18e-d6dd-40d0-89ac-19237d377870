# Deployment Guide

This guide covers deploying the Python Learning Platform to production environments.

## Frontend Deployment (Vercel/Netlify)

### Vercel Deployment

1. **Install Vercel CLI**
   ```bash
   npm install -g vercel
   ```

2. **Build the frontend**
   ```bash
   cd frontend
   npm run build
   ```

3. **Deploy to Vercel**
   ```bash
   vercel --prod
   ```

4. **Environment Variables**
   Set the following environment variable in Vercel dashboard:
   - `VITE_API_URL`: Your backend API URL

### Netlify Deployment

1. **Build the frontend**
   ```bash
   cd frontend
   npm run build
   ```

2. **Deploy to Netlify**
   - Connect your GitHub repository to Netlify
   - Set build command: `npm run build`
   - Set publish directory: `dist`
   - Set environment variable: `VITE_API_URL`

## Backend Deployment (Render.com)

### Render.com Deployment

1. **Create a new Web Service on Render**
   - Connect your GitHub repository
   - Select the `backend` directory as root

2. **Configure the service**
   - **Build Command**: `pip install -r requirements.txt`
   - **Start Command**: `gunicorn app:app`
   - **Environment**: Python 3

3. **Environment Variables**
   Set the following environment variables:
   ```env
   FLASK_ENV=production
   SECRET_KEY=your-production-secret-key
   JWT_SECRET_KEY=your-production-jwt-secret
   DATABASE_URL=your-postgresql-database-url
   ```

4. **Add PostgreSQL Database**
   - Create a PostgreSQL database on Render
   - Copy the database URL to your environment variables

### Alternative: Heroku Deployment

1. **Install Heroku CLI**
   ```bash
   # Install from https://devcenter.heroku.com/articles/heroku-cli
   ```

2. **Create Heroku app**
   ```bash
   cd backend
   heroku create your-app-name
   ```

3. **Add PostgreSQL addon**
   ```bash
   heroku addons:create heroku-postgresql:hobby-dev
   ```

4. **Set environment variables**
   ```bash
   heroku config:set FLASK_ENV=production
   heroku config:set SECRET_KEY=your-secret-key
   heroku config:set JWT_SECRET_KEY=your-jwt-secret
   ```

5. **Deploy**
   ```bash
   git push heroku main
   ```

## Database Setup

### PostgreSQL Production Setup

1. **Create database and user**
   ```sql
   CREATE DATABASE pylearning;
   CREATE USER pylearning_user WITH PASSWORD 'your_password';
   GRANT ALL PRIVILEGES ON DATABASE pylearning TO pylearning_user;
   ```

2. **Run migrations**
   ```bash
   flask db upgrade
   ```

3. **Seed initial data**
   ```bash
   python seed_data.py
   ```

## Environment Configuration

### Production Environment Variables

#### Backend
```env
FLASK_ENV=production
FLASK_DEBUG=0
SECRET_KEY=your-very-secure-secret-key
JWT_SECRET_KEY=your-very-secure-jwt-secret
DATABASE_URL=postgresql://user:password@host:port/database
CORS_ORIGINS=https://your-frontend-domain.com
```

#### Frontend
```env
VITE_API_URL=https://your-backend-domain.com
```

## Security Considerations

### Backend Security

1. **Use strong secret keys**
   ```bash
   python -c "import secrets; print(secrets.token_hex(32))"
   ```

2. **Enable HTTPS**
   - Use SSL certificates (Let's Encrypt recommended)
   - Configure your web server to redirect HTTP to HTTPS

3. **Database Security**
   - Use strong database passwords
   - Enable SSL for database connections
   - Restrict database access to your application servers

4. **CORS Configuration**
   - Only allow your frontend domain in CORS origins
   - Remove localhost origins in production

### Frontend Security

1. **Environment Variables**
   - Never expose sensitive data in frontend environment variables
   - Only use `VITE_` prefixed variables for public configuration

2. **Content Security Policy**
   - Implement CSP headers to prevent XSS attacks
   - Configure your hosting provider's security settings

## Performance Optimization

### Backend Optimization

1. **Use a production WSGI server**
   ```bash
   gunicorn --workers 4 --bind 0.0.0.0:5000 app:app
   ```

2. **Database Connection Pooling**
   ```python
   # In app.py
   app.config['SQLALCHEMY_ENGINE_OPTIONS'] = {
       'pool_size': 10,
       'pool_recycle': 120,
       'pool_pre_ping': True
   }
   ```

3. **Caching**
   - Implement Redis for session storage
   - Cache frequently accessed data

### Frontend Optimization

1. **Build Optimization**
   ```bash
   npm run build
   ```

2. **CDN Configuration**
   - Use a CDN for static assets
   - Enable gzip compression

## Monitoring and Logging

### Backend Monitoring

1. **Application Logging**
   ```python
   import logging
   logging.basicConfig(level=logging.INFO)
   ```

2. **Error Tracking**
   - Integrate with Sentry or similar service
   - Monitor API response times

### Frontend Monitoring

1. **Analytics**
   - Integrate Google Analytics or similar
   - Track user engagement and lesson completion

2. **Error Tracking**
   - Use Sentry for frontend error tracking
   - Monitor Core Web Vitals

## Backup and Recovery

### Database Backups

1. **Automated Backups**
   ```bash
   # Daily backup script
   pg_dump $DATABASE_URL > backup_$(date +%Y%m%d).sql
   ```

2. **Backup Storage**
   - Store backups in cloud storage (AWS S3, Google Cloud Storage)
   - Implement backup rotation policy

### Application Backups

1. **Code Repository**
   - Ensure code is backed up in version control
   - Tag releases for easy rollback

2. **Configuration Backups**
   - Document all environment variables
   - Store configuration templates securely

## Scaling Considerations

### Horizontal Scaling

1. **Load Balancing**
   - Use a load balancer for multiple backend instances
   - Configure session affinity if needed

2. **Database Scaling**
   - Consider read replicas for heavy read workloads
   - Implement database sharding if necessary

### Vertical Scaling

1. **Resource Monitoring**
   - Monitor CPU, memory, and disk usage
   - Scale resources based on usage patterns

2. **Performance Testing**
   - Load test your application before scaling
   - Identify bottlenecks and optimize accordingly

## Troubleshooting

### Common Deployment Issues

1. **Build Failures**
   - Check Node.js/Python versions
   - Verify all dependencies are listed
   - Check for environment-specific issues

2. **Database Connection Issues**
   - Verify database URL format
   - Check firewall and security group settings
   - Ensure database is accessible from your application

3. **CORS Issues**
   - Update CORS origins for production domains
   - Check for protocol mismatches (HTTP vs HTTPS)

### Health Checks

1. **Backend Health Check**
   ```bash
   curl https://your-api-domain.com/api/health
   ```

2. **Frontend Health Check**
   - Verify the application loads correctly
   - Test user registration and login flows
   - Check lesson functionality

## Maintenance

### Regular Maintenance Tasks

1. **Security Updates**
   - Keep dependencies updated
   - Monitor security advisories
   - Apply patches promptly

2. **Database Maintenance**
   - Regular database cleanup
   - Index optimization
   - Query performance monitoring

3. **Log Rotation**
   - Implement log rotation policies
   - Archive old logs
   - Monitor disk space usage
