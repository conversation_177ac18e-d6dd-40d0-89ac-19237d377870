Module(body=[Assign(targets=[Name(id='lst',
                                  ctx=Store())],
                    value=List(elts=[Num(n=1),
                                     Num(n=2),
                                     Num(n=3)],
                               ctx=Load())),
             Assign(targets=[Name(id='check',
                                  ctx=Store())],
                    value=Compare(left=Num(n=1),
                                  ops=[In()],
                                  comparators=[Name(id='lst',
                                                    ctx=Load())])),
             Print(dest=None,
                   values=[Call(func=Name(id='type',
                                          ctx=Load()),
                                args=[Name(id='check',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Assign(targets=[Name(id='d',
                                  ctx=Store())],
                    value=Dict(keys=[Num(n=1)],
                               values=[Num(n=2)])),
             Assign(targets=[Name(id='check',
                                  ctx=Store())],
                    value=Call(func=Attribute(value=Name(id='d',
                                                         ctx=Load()),
                                              attr='has_key',
                                              ctx=Load()),
                               args=[Num(n=1)],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Call(func=Name(id='type',
                                          ctx=Load()),
                                args=[Name(id='check',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Assign(targets=[Name(id='check',
                                  ctx=Store())],
                    value=Compare(left=Num(n=3),
                                  ops=[In()],
                                  comparators=[Name(id='d',
                                                    ctx=Load())])),
             Print(dest=None,
                   values=[Call(func=Name(id='type',
                                          ctx=Load()),
                                args=[Name(id='check',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Assign(targets=[Name(id='lst2',
                                  ctx=Store())],
                    value=List(elts=[Num(n=1),
                                     Num(n=2),
                                     Num(n=3),
                                     Num(n=4)],
                               ctx=Load())),
             Assign(targets=[Name(id='check',
                                  ctx=Store())],
                    value=Call(func=Attribute(value=Name(id='lst2',
                                                         ctx=Load()),
                                              attr='reverse',
                                              ctx=Load()),
                               args=[],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Call(func=Name(id='type',
                                          ctx=Load()),
                                args=[Name(id='check',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Assign(targets=[Name(id='check',
                                  ctx=Store())],
                    value=Call(func=Attribute(value=Name(id='lst2',
                                                         ctx=Load()),
                                              attr='append',
                                              ctx=Load()),
                               args=[Num(n=8)],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Call(func=Name(id='type',
                                          ctx=Load()),
                                args=[Name(id='check',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Assign(targets=[Name(id='check',
                                  ctx=Store())],
                    value=Call(func=Attribute(value=Name(id='lst2',
                                                         ctx=Load()),
                                              attr='insert',
                                              ctx=Load()),
                               args=[Num(n=2),
                                     Num(n=3)],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Call(func=Name(id='type',
                                          ctx=Load()),
                                args=[Name(id='check',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Assign(targets=[Name(id='check',
                                  ctx=Store())],
                    value=Call(func=Attribute(value=Name(id='lst2',
                                                         ctx=Load()),
                                              attr='extend',
                                              ctx=Load()),
                               args=[Name(id='lst',
                                          ctx=Load())],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Call(func=Name(id='type',
                                          ctx=Load()),
                                args=[Name(id='check',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Assign(targets=[Name(id='check',
                                  ctx=Store())],
                    value=Call(func=Attribute(value=Name(id='lst2',
                                                         ctx=Load()),
                                              attr='remove',
                                              ctx=Load()),
                               args=[Num(n=4)],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Call(func=Name(id='type',
                                          ctx=Load()),
                                args=[Name(id='check',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Assign(targets=[Name(id='check',
                                  ctx=Store())],
                    value=Call(func=Attribute(value=Name(id='lst2',
                                                         ctx=Load()),
                                              attr='index',
                                              ctx=Load()),
                               args=[Num(n=2)],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Call(func=Name(id='type',
                                          ctx=Load()),
                                args=[Name(id='check',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Assign(targets=[Name(id='check',
                                  ctx=Store())],
                    value=Call(func=Attribute(value=Name(id='lst2',
                                                         ctx=Load()),
                                              attr='count',
                                              ctx=Load()),
                               args=[Num(n=3)],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Call(func=Name(id='type',
                                          ctx=Load()),
                                args=[Name(id='check',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Assign(targets=[Name(id='check',
                                  ctx=Store())],
                    value=Call(func=Attribute(value=Name(id='lst2',
                                                         ctx=Load()),
                                              attr='sort',
                                              ctx=Load()),
                               args=[],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Call(func=Name(id='type',
                                          ctx=Load()),
                                args=[Name(id='check',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Assign(targets=[Name(id='s',
                                  ctx=Store())],
                    value=Call(func=Name(id='set',
                                         ctx=Load()),
                               args=[List(elts=[Num(n=1),
                                                Num(n=2),
                                                Num(n=3)],
                                          ctx=Load())],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Assign(targets=[Name(id='check',
                                  ctx=Store())],
                    value=Call(func=Attribute(value=Name(id='s',
                                                         ctx=Load()),
                                              attr='isdisjoint',
                                              ctx=Load()),
                               args=[Name(id='s',
                                          ctx=Load())],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Call(func=Name(id='type',
                                          ctx=Load()),
                                args=[Name(id='check',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Name(id='s',
                                ctx=Load())],
                   nl=True),
             Assign(targets=[Name(id='check',
                                  ctx=Store())],
                    value=Call(func=Attribute(value=Name(id='s',
                                                         ctx=Load()),
                                              attr='issubset',
                                              ctx=Load()),
                               args=[Name(id='s',
                                          ctx=Load())],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Call(func=Name(id='type',
                                          ctx=Load()),
                                args=[Name(id='check',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Name(id='s',
                                ctx=Load())],
                   nl=True),
             Assign(targets=[Name(id='check',
                                  ctx=Store())],
                    value=Call(func=Attribute(value=Name(id='s',
                                                         ctx=Load()),
                                              attr='update',
                                              ctx=Load()),
                               args=[Name(id='s',
                                          ctx=Load())],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Call(func=Name(id='type',
                                          ctx=Load()),
                                args=[Name(id='check',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Name(id='s',
                                ctx=Load())],
                   nl=True),
             Assign(targets=[Name(id='s2',
                                  ctx=Store())],
                    value=Call(func=Name(id='set',
                                         ctx=Load()),
                               args=[List(elts=[Num(n=2),
                                                Num(n=3)],
                                          ctx=Load())],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Assign(targets=[Name(id='check',
                                  ctx=Store())],
                    value=Call(func=Attribute(value=Name(id='s',
                                                         ctx=Load()),
                                              attr='intersection_update',
                                              ctx=Load()),
                               args=[Name(id='s2',
                                          ctx=Load())],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Call(func=Name(id='type',
                                          ctx=Load()),
                                args=[Name(id='check',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Name(id='s',
                                ctx=Load())],
                   nl=True),
             Assign(targets=[Name(id='check',
                                  ctx=Store())],
                    value=Call(func=Attribute(value=Name(id='s',
                                                         ctx=Load()),
                                              attr='difference_update',
                                              ctx=Load()),
                               args=[Name(id='s2',
                                          ctx=Load())],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Call(func=Name(id='type',
                                          ctx=Load()),
                                args=[Name(id='check',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Name(id='s',
                                ctx=Load())],
                   nl=True),
             Assign(targets=[Name(id='check',
                                  ctx=Store())],
                    value=Call(func=Attribute(value=Name(id='s',
                                                         ctx=Load()),
                                              attr='symmetric_difference_update',
                                              ctx=Load()),
                               args=[Name(id='s2',
                                          ctx=Load())],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Call(func=Name(id='type',
                                          ctx=Load()),
                                args=[Name(id='check',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Name(id='s',
                                ctx=Load())],
                   nl=True),
             Assign(targets=[Name(id='check',
                                  ctx=Store())],
                    value=Call(func=Attribute(value=Name(id='s',
                                                         ctx=Load()),
                                              attr='add',
                                              ctx=Load()),
                               args=[Num(n=4)],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Call(func=Name(id='type',
                                          ctx=Load()),
                                args=[Name(id='check',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Name(id='s',
                                ctx=Load())],
                   nl=True),
             Assign(targets=[Name(id='check',
                                  ctx=Store())],
                    value=Call(func=Attribute(value=Name(id='s',
                                                         ctx=Load()),
                                              attr='discard',
                                              ctx=Load()),
                               args=[Num(n=4)],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Call(func=Name(id='type',
                                          ctx=Load()),
                                args=[Name(id='check',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Name(id='s',
                                ctx=Load())],
                   nl=True),
             Assign(targets=[Name(id='check',
                                  ctx=Store())],
                    value=Call(func=Attribute(value=Name(id='s',
                                                         ctx=Load()),
                                              attr='remove',
                                              ctx=Load()),
                               args=[Num(n=3)],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Call(func=Name(id='type',
                                          ctx=Load()),
                                args=[Name(id='check',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Name(id='s',
                                ctx=Load())],
                   nl=True),
             Assign(targets=[Name(id='t',
                                  ctx=Store())],
                    value=Tuple(elts=[Num(n=1),
                                      Num(n=2),
                                      Num(n=3)],
                                ctx=Load())),
             Assign(targets=[Name(id='check',
                                  ctx=Store())],
                    value=Call(func=Attribute(value=Name(id='t',
                                                         ctx=Load()),
                                              attr='index',
                                              ctx=Load()),
                               args=[Num(n=2)],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Call(func=Name(id='type',
                                          ctx=Load()),
                                args=[Name(id='check',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Assign(targets=[Name(id='check',
                                  ctx=Store())],
                    value=Call(func=Attribute(value=Name(id='t',
                                                         ctx=Load()),
                                              attr='count',
                                              ctx=Load()),
                               args=[Num(n=3)],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Call(func=Name(id='type',
                                          ctx=Load()),
                                args=[Name(id='check',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Assign(targets=[Name(id='s',
                                  ctx=Store())],
                    value=Str(s='abcabcabc')),
             Assign(targets=[Name(id='check',
                                  ctx=Store())],
                    value=Call(func=Attribute(value=Name(id='s',
                                                         ctx=Load()),
                                              attr='count',
                                              ctx=Load()),
                               args=[Str(s='a')],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Call(func=Name(id='type',
                                          ctx=Load()),
                                args=[Name(id='check',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Assign(targets=[Name(id='check',
                                  ctx=Store())],
                    value=Call(func=Attribute(value=Name(id='s',
                                                         ctx=Load()),
                                              attr='find',
                                              ctx=Load()),
                               args=[Str(s='bc')],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Call(func=Name(id='type',
                                          ctx=Load()),
                                args=[Name(id='check',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Assign(targets=[Name(id='check',
                                  ctx=Store())],
                    value=Call(func=Attribute(value=Name(id='s',
                                                         ctx=Load()),
                                              attr='index',
                                              ctx=Load()),
                               args=[Str(s='cab')],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Call(func=Name(id='type',
                                          ctx=Load()),
                                args=[Name(id='check',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Assign(targets=[Name(id='check',
                                  ctx=Store())],
                    value=Call(func=Attribute(value=Name(id='s',
                                                         ctx=Load()),
                                              attr='rfind',
                                              ctx=Load()),
                               args=[Str(s='bc')],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Call(func=Name(id='type',
                                          ctx=Load()),
                                args=[Name(id='check',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Assign(targets=[Name(id='check',
                                  ctx=Store())],
                    value=Call(func=Attribute(value=Name(id='s',
                                                         ctx=Load()),
                                              attr='rindex',
                                              ctx=Load()),
                               args=[Str(s='cab')],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[Call(func=Name(id='type',
                                          ctx=Load()),
                                args=[Name(id='check',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True)])
