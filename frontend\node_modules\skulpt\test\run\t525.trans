Module(body=[Print(dest=None,
                   values=[Call(func=Name(id='int',
                                          ctx=Load()),
                                args=[Str(s='0a'),
                                      Num(n=13)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='int',
                                          ctx=Load()),
                                args=[Str(s='0c'),
                                      Num(n=13)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='int',
                                          ctx=Load()),
                                args=[Str(s='0b'),
                                      Num(n=13)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='int',
                                          ctx=Load()),
                                args=[Str(s='0n'),
                                      Num(n=26)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='int',
                                          ctx=Load()),
                                args=[Str(s='0p'),
                                      Num(n=26)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='int',
                                          ctx=Load()),
                                args=[Str(s='0o'),
                                      Num(n=26)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='int',
                                          ctx=Load()),
                                args=[Str(s='0w'),
                                      Num(n=35)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='int',
                                          ctx=Load()),
                                args=[Str(s='0y'),
                                      Num(n=35)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='int',
                                          ctx=Load()),
                                args=[Str(s='0x'),
                                      Num(n=35)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True)])
