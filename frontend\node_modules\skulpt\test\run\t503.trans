Module(body=[ClassDef(name='Comparable',
                      bases=[],
                      body=[FunctionDef(name='__init__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param()),
                                                             Name(id='value',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Assign(targets=[Attribute(value=Name(id='self',
                                                                                   ctx=Load()),
                                                                        attr='value',
                                                                        ctx=Store())],
                                                     value=Name(id='value',
                                                                ctx=Load()))],
                                        decorator_list=[]),
                            FunctionDef(name='__lt__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param()),
                                                             Name(id='other',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Return(value=Compare(left=Attribute(value=Name(id='self',
                                                                                             ctx=Load()),
                                                                                  attr='value',
                                                                                  ctx=Load()),
                                                                   ops=[Lt()],
                                                                   comparators=[Attribute(value=Name(id='other',
                                                                                                     ctx=Load()),
                                                                                          attr='value',
                                                                                          ctx=Load())]))],
                                        decorator_list=[]),
                            FunctionDef(name='__repr__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Return(value=BinOp(left=Str(s='Value :'),
                                                                 op=Add(),
                                                                 right=Call(func=Name(id='str',
                                                                                      ctx=Load()),
                                                                            args=[Attribute(value=Name(id='self',
                                                                                                       ctx=Load()),
                                                                                            attr='value',
                                                                                            ctx=Load())],
                                                                            keywords=[],
                                                                            starargs=None,
                                                                            kwargs=None)))],
                                        decorator_list=[])],
                      decorator_list=[]),
             Assign(targets=[Name(id='lst',
                                  ctx=Store())],
                    value=List(elts=[Num(n=5),
                                     Num(n=9),
                                     Num(n=2),
                                     Num(n=7)],
                               ctx=Load())),
             Assign(targets=[Name(id='otherLst',
                                  ctx=Store())],
                    value=ListComp(elt=Call(func=Name(id='Comparable',
                                                      ctx=Load()),
                                            args=[Name(id='a',
                                                       ctx=Load())],
                                            keywords=[],
                                            starargs=None,
                                            kwargs=None),
                                   generators=[comprehension(target=Name(id='a',
                                                                         ctx=Store()),
                                                             iter=Name(id='lst',
                                                                       ctx=Load()),
                                                             ifs=[])])),
             Print(dest=None,
                   values=[Name(id='lst',
                                ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Name(id='otherLst',
                                ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='min',
                                          ctx=Load()),
                                args=[Name(id='lst',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='min',
                                          ctx=Load()),
                                args=[Name(id='otherLst',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True)])
