Module(body=[Print(dest=None,
                   values=[Call(func=Name(id='dict',
                                          ctx=Load()),
                                args=[],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='dict',
                                          ctx=Load()),
                                args=[Dict(keys=[],
                                           values=[])],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='dict',
                                          ctx=Load()),
                                args=[Dict(keys=[Num(n=1),
                                                 Num(n=3)],
                                           values=[Num(n=2),
                                                   Num(n=4)])],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Assign(targets=[Name(id='d',
                                  ctx=Store())],
                    value=Dict(keys=[Str(s='a'),
                                     Str(s='b')],
                               values=[Str(s='A'),
                                       Str(s='B')])),
             Print(dest=None,
                   values=[Call(func=Name(id='dict',
                                          ctx=Load()),
                                args=[Name(id='d',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True)])
