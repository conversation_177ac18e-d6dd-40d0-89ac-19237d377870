Module(body=[Assign(targets=[Name(id='x',
                                  ctx=Store())],
                    value=List(elts=[Num(n=1)],
                               ctx=Load())),
             Expr(value=Call(func=Attribute(value=Name(id='x',
                                                       ctx=Load()),
                                            attr='extend',
                                            ctx=Load()),
                             args=[List(elts=[Num(n=2),
                                              Num(n=3)],
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Subscript(value=Name(id='x',
                                                ctx=Load()),
                                     slice=Index(value=Num(n=1)),
                                     ctx=Load())],
                   nl=True)])
