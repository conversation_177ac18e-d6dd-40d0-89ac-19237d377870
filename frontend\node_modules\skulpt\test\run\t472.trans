Module(body=[FunctionDef(name='helper',
                         args=arguments(args=[Name(id='got',
                                                   ctx=Param()),
                                              Name(id='expect',
                                                   ctx=Param())],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[]),
                         body=[If(test=Compare(left=Name(id='got',
                                                         ctx=Load()),
                                               ops=[Eq()],
                                               comparators=[Name(id='expect',
                                                                 ctx=Load())]),
                                  body=[Print(dest=None,
                                              values=[Name(id='True',
                                                           ctx=Load())],
                                              nl=True)],
                                  orelse=[Print(dest=None,
                                                values=[Name(id='False',
                                                             ctx=Load()),
                                                        Name(id='expect',
                                                             ctx=Load()),
                                                        Name(id='got',
                                                             ctx=Load())],
                                                nl=True)])],
                         decorator_list=[]),
             Print(dest=None,
                   values=[Str(s='\nstr.replace')],
                   nl=True),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='hello'),
                                                       attr='replace',
                                                       ctx=Load()),
                                        args=[Str(s='l'),
                                              Str(s='L')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='heLLo')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='hello'),
                                                       attr='replace',
                                                       ctx=Load()),
                                        args=[Str(s='l'),
                                              Str(s='L'),
                                              Num(n=1)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='heLlo')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='hello'),
                                                       attr='replace',
                                                       ctx=Load()),
                                        args=[Str(s='l'),
                                              Str(s='L'),
                                              Num(n=5)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='heLLo')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='hello'),
                                                       attr='replace',
                                                       ctx=Load()),
                                        args=[Str(s='l'),
                                              Str(s='L'),
                                              Num(n=0)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='hello')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='hello hello hello'),
                                                       attr='replace',
                                                       ctx=Load()),
                                        args=[Str(s='ll'),
                                              Str(s='lll')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='helllo helllo helllo')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='hello hello hello'),
                                                       attr='replace',
                                                       ctx=Load()),
                                        args=[Str(s='ll'),
                                              Str(s='lll'),
                                              Num(n=2)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='helllo helllo hello')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='hello hello hello'),
                                                       attr='replace',
                                                       ctx=Load()),
                                        args=[Str(s='ll'),
                                              Str(s='l')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='helo helo helo')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='hello hello hello'),
                                                       attr='replace',
                                                       ctx=Load()),
                                        args=[Str(s='ll'),
                                              Str(s='l'),
                                              Num(n=2)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='helo helo hello')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='abcabcaaaabc'),
                                                       attr='replace',
                                                       ctx=Load()),
                                        args=[Str(s='abc'),
                                              Str(s='123')],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='123123aaa123')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='abcabcaaaabc'),
                                                       attr='replace',
                                                       ctx=Load()),
                                        args=[Str(s='abc'),
                                              Str(s='123'),
                                              Num(n=2)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='123123aaaabc')],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Expr(value=Call(func=Name(id='helper',
                                       ctx=Load()),
                             args=[Call(func=Attribute(value=Str(s='abcabcaaaabc'),
                                                       attr='replace',
                                                       ctx=Load()),
                                        args=[Str(s='abc'),
                                              Str(s='123'),
                                              Num(n=-1)],
                                        keywords=[],
                                        starargs=None,
                                        kwargs=None),
                                   Str(s='123123aaa123')],
                             keywords=[],
                             starargs=None,
                             kwargs=None))])
