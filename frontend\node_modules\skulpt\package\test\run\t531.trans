Module(body=[ClassDef(name='Foo',
                      bases=[object],
                      body=[Pass()],
                      decorator_list=[]),
             ClassDef(name='Bar',
                      bases=[Name(id='Foo',
                                  ctx=Load())],
                      body=[Pass()],
                      decorator_list=[]),
             ClassDef(name='Baz',
                      bases=[Name(id='Bar',
                                  ctx=Load())],
                      body=[Pass()],
                      decorator_list=[]),
             ClassDef(name='XXX',
                      bases=[],
                      body=[Pass()],
                      decorator_list=[]),
             ClassDef(name='Frob',
                      bases=[Name(id='Baz',
                                  ctx=Load()),
                             Name(id='XXX',
                                  ctx=Load())],
                      body=[Pass()],
                      decorator_list=[]),
             Print(dest=None,
                   values=[Call(func=Name(id='issubclass',
                                          ctx=Load()),
                                args=[Name(id='Bar',
                                           ctx=Load()),
                                      Name(id='Foo',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='issubclass',
                                          ctx=Load()),
                                args=[Name(id='Foo',
                                           ctx=Load()),
                                      Name(id='Bar',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='issubclass',
                                          ctx=Load()),
                                args=[Name(id='Baz',
                                           ctx=Load()),
                                      Name(id='Foo',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='issubclass',
                                          ctx=Load()),
                                args=[Name(id='Baz',
                                           ctx=Load()),
                                      Name(id='Bar',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='issubclass',
                                          ctx=Load()),
                                args=[Name(id='Foo',
                                           ctx=Load()),
                                      Name(id='object',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='issubclass',
                                          ctx=Load()),
                                args=[Name(id='Frob',
                                           ctx=Load()),
                                      Name(id='XXX',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True)])
