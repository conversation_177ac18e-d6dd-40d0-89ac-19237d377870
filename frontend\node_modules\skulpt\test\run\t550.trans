Module(body=[FunctionDef(name='foo',
                         args=arguments(args=[],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[]),
                         body=[Pass()],
                         decorator_list=[]),
             Assign(targets=[Name(id='bar',
                                  ctx=Store())],
                    value=Num(n=11)),
             Print(dest=None,
                   values=[Subscript(value=Call(func=Name(id='globals',
                                                          ctx=Load()),
                                                args=[],
                                                keywords=[],
                                                starargs=None,
                                                kwargs=None),
                                     slice=Index(value=Str(s='bar')),
                                     ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Str(s='foo'),
                                   ops=[In()],
                                   comparators=[Call(func=Name(id='globals',
                                                               ctx=Load()),
                                                     args=[],
                                                     keywords=[],
                                                     starargs=None,
                                                     kwargs=None)])],
                   nl=True),
             FunctionDef(name='baz',
                         args=arguments(args=[Name(id='x',
                                                   ctx=Param())],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[]),
                         body=[Print(dest=None,
                                     values=[Compare(left=Str(s='baz'),
                                                     ops=[In()],
                                                     comparators=[Call(func=Name(id='globals',
                                                                                 ctx=Load()),
                                                                       args=[],
                                                                       keywords=[],
                                                                       starargs=None,
                                                                       kwargs=None)])],
                                     nl=True)],
                         decorator_list=[]),
             Expr(value=Call(func=Name(id='baz',
                                       ctx=Load()),
                             args=[Num(n=10)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             ClassDef(name='MyClass',
                      bases=[],
                      body=[FunctionDef(name='__init__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Print(dest=None,
                                                    values=[Subscript(value=Call(func=Name(id='globals',
                                                                                           ctx=Load()),
                                                                                 args=[],
                                                                                 keywords=[],
                                                                                 starargs=None,
                                                                                 kwargs=None),
                                                                      slice=Index(value=Str(s='__name__')),
                                                                      ctx=Load())],
                                                    nl=True),
                                              Print(dest=None,
                                                    values=[Compare(left=Str(s='MyClass'),
                                                                    ops=[In()],
                                                                    comparators=[Call(func=Name(id='globals',
                                                                                                ctx=Load()),
                                                                                      args=[],
                                                                                      keywords=[],
                                                                                      starargs=None,
                                                                                      kwargs=None)])],
                                                    nl=True),
                                              Print(dest=None,
                                                    values=[Call(func=Name(id='type',
                                                                           ctx=Load()),
                                                                 args=[Subscript(value=Call(func=Name(id='globals',
                                                                                                      ctx=Load()),
                                                                                            args=[],
                                                                                            keywords=[],
                                                                                            starargs=None,
                                                                                            kwargs=None),
                                                                                 slice=Index(value=Str(s='baz')),
                                                                                 ctx=Load())],
                                                                 keywords=[],
                                                                 starargs=None,
                                                                 kwargs=None)],
                                                    nl=True)],
                                        decorator_list=[])],
                      decorator_list=[]),
             Assign(targets=[Name(id='y',
                                  ctx=Store())],
                    value=Call(func=Name(id='MyClass',
                                         ctx=Load()),
                               args=[],
                               keywords=[],
                               starargs=None,
                               kwargs=None))])
