Module(body=[ImportFrom(module='time',
                        names=[alias(name='sleep',
                                     asname=None)],
                        level=0),
             ClassDef(name='A',
                      bases=[Name(id='object',
                                  ctx=Load())],
                      body=[FunctionDef(name='__init__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Expr(value=Call(func=Attribute(value=Name(id='object',
                                                                                        ctx=Load()),
                                                                             attr='__setattr__',
                                                                             ctx=Load()),
                                                              args=[Name(id='self',
                                                                         ctx=Load()),
                                                                    Str(s='x'),
                                                                    Num(n=42)],
                                                              keywords=[],
                                                              starargs=None,
                                                              kwargs=None))],
                                        decorator_list=[]),
                            FunctionDef(name='__getattr__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param()),
                                                             Name(id='attr',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[If(test=Call(func=Name(id='isinstance',
                                                                     ctx=Load()),
                                                           args=[Name(id='attr',
                                                                      ctx=Load()),
                                                                 Name(id='str',
                                                                      ctx=Load())],
                                                           keywords=[],
                                                           starargs=None,
                                                           kwargs=None),
                                                 body=[Print(dest=None,
                                                             values=[Str(s='attr is a string, as it should be')],
                                                             nl=True)],
                                                 orelse=[]),
                                              Print(dest=None,
                                                    values=[BinOp(left=Str(s='Getting '),
                                                                  op=Add(),
                                                                  right=Name(id='attr',
                                                                             ctx=Load()))],
                                                    nl=True),
                                              If(test=Compare(left=Name(id='attr',
                                                                        ctx=Load()),
                                                              ops=[Eq()],
                                                              comparators=[Str(s='y')]),
                                                 body=[Return(value=Num(n=41))],
                                                 orelse=[Return(value=Num(n=43))])],
                                        decorator_list=[]),
                            FunctionDef(name='__setattr__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param()),
                                                             Name(id='attr',
                                                                  ctx=Param()),
                                                             Name(id='value',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[If(test=Call(func=Name(id='isinstance',
                                                                     ctx=Load()),
                                                           args=[Name(id='attr',
                                                                      ctx=Load()),
                                                                 Name(id='str',
                                                                      ctx=Load())],
                                                           keywords=[],
                                                           starargs=None,
                                                           kwargs=None),
                                                 body=[Print(dest=None,
                                                             values=[Str(s='attr is a string, as it should be')],
                                                             nl=True)],
                                                 orelse=[]),
                                              Print(dest=None,
                                                    values=[BinOp(left=BinOp(left=BinOp(left=Str(s='Intercepted attempt to set '),
                                                                                        op=Add(),
                                                                                        right=Name(id='attr',
                                                                                                   ctx=Load())),
                                                                             op=Add(),
                                                                             right=Str(s=' to ')),
                                                                  op=Add(),
                                                                  right=Call(func=Name(id='str',
                                                                                       ctx=Load()),
                                                                             args=[Name(id='value',
                                                                                        ctx=Load())],
                                                                             keywords=[],
                                                                             starargs=None,
                                                                             kwargs=None))],
                                                    nl=True)],
                                        decorator_list=[])],
                      decorator_list=[]),
             Assign(targets=[Name(id='a',
                                  ctx=Store())],
                    value=Call(func=Name(id='A',
                                         ctx=Load()),
                               args=[],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Print(dest=None,
                   values=[BinOp(left=Str(s='a.x = '),
                                 op=Add(),
                                 right=Call(func=Name(id='str',
                                                      ctx=Load()),
                                            args=[Attribute(value=Name(id='a',
                                                                       ctx=Load()),
                                                            attr='x',
                                                            ctx=Load())],
                                            keywords=[],
                                            starargs=None,
                                            kwargs=None))],
                   nl=True),
             Print(dest=None,
                   values=[BinOp(left=Str(s='a.y = '),
                                 op=Add(),
                                 right=Call(func=Name(id='str',
                                                      ctx=Load()),
                                            args=[Attribute(value=Name(id='a',
                                                                       ctx=Load()),
                                                            attr='y',
                                                            ctx=Load())],
                                            keywords=[],
                                            starargs=None,
                                            kwargs=None))],
                   nl=True),
             Print(dest=None,
                   values=[BinOp(left=Str(s='a.z = '),
                                 op=Add(),
                                 right=Call(func=Name(id='str',
                                                      ctx=Load()),
                                            args=[Attribute(value=Name(id='a',
                                                                       ctx=Load()),
                                                            attr='z',
                                                            ctx=Load())],
                                            keywords=[],
                                            starargs=None,
                                            kwargs=None))],
                   nl=True),
             Assign(targets=[Attribute(value=Name(id='a',
                                                  ctx=Load()),
                                       attr='x',
                                       ctx=Store())],
                    value=Num(n=0)),
             Print(dest=None,
                   values=[BinOp(left=Str(s='a.x = '),
                                 op=Add(),
                                 right=Call(func=Name(id='str',
                                                      ctx=Load()),
                                            args=[Attribute(value=Name(id='a',
                                                                       ctx=Load()),
                                                            attr='x',
                                                            ctx=Load())],
                                            keywords=[],
                                            starargs=None,
                                            kwargs=None))],
                   nl=True),
             AugAssign(target=Attribute(value=Name(id='a',
                                                   ctx=Load()),
                                        attr='x',
                                        ctx=Store()),
                       op=Add(),
                       value=Num(n=1)),
             ClassDef(name='B',
                      bases=[Name(id='object',
                                  ctx=Load())],
                      body=[FunctionDef(name='__getattr__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param()),
                                                             Name(id='attr',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Expr(value=Call(func=Name(id='sleep',
                                                                        ctx=Load()),
                                                              args=[Num(n=0.01)],
                                                              keywords=[],
                                                              starargs=None,
                                                              kwargs=None)),
                                              Return(value=Call(func=Attribute(value=Name(id='object',
                                                                                          ctx=Load()),
                                                                               attr='__getattr__',
                                                                               ctx=Load()),
                                                                args=[Name(id='self',
                                                                           ctx=Load()),
                                                                      Name(id='attr',
                                                                           ctx=Load())],
                                                                keywords=[],
                                                                starargs=None,
                                                                kwargs=None))],
                                        decorator_list=[]),
                            FunctionDef(name='__setattr__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param()),
                                                             Name(id='attr',
                                                                  ctx=Param()),
                                                             Name(id='value',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Expr(value=Call(func=Name(id='sleep',
                                                                        ctx=Load()),
                                                              args=[Num(n=0.01)],
                                                              keywords=[],
                                                              starargs=None,
                                                              kwargs=None)),
                                              Return(value=Call(func=Attribute(value=Name(id='object',
                                                                                          ctx=Load()),
                                                                               attr='__setattr__',
                                                                               ctx=Load()),
                                                                args=[Name(id='self',
                                                                           ctx=Load()),
                                                                      Name(id='attr',
                                                                           ctx=Load()),
                                                                      Name(id='value',
                                                                           ctx=Load())],
                                                                keywords=[],
                                                                starargs=None,
                                                                kwargs=None))],
                                        decorator_list=[])],
                      decorator_list=[]),
             Assign(targets=[Name(id='b',
                                  ctx=Store())],
                    value=Call(func=Name(id='B',
                                         ctx=Load()),
                               args=[],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Assign(targets=[Attribute(value=Name(id='b',
                                                  ctx=Load()),
                                       attr='x',
                                       ctx=Store())],
                    value=Num(n=42)),
             Print(dest=None,
                   values=[BinOp(left=Str(s='b.x = '),
                                 op=Add(),
                                 right=Call(func=Name(id='str',
                                                      ctx=Load()),
                                            args=[Attribute(value=Name(id='b',
                                                                       ctx=Load()),
                                                            attr='x',
                                                            ctx=Load())],
                                            keywords=[],
                                            starargs=None,
                                            kwargs=None))],
                   nl=True),
             AugAssign(target=Attribute(value=Name(id='b',
                                                   ctx=Load()),
                                        attr='x',
                                        ctx=Store()),
                       op=Add(),
                       value=Num(n=1)),
             Print(dest=None,
                   values=[BinOp(left=Str(s='b.x = '),
                                 op=Add(),
                                 right=Call(func=Name(id='str',
                                                      ctx=Load()),
                                            args=[Attribute(value=Name(id='b',
                                                                       ctx=Load()),
                                                            attr='x',
                                                            ctx=Load())],
                                            keywords=[],
                                            starargs=None,
                                            kwargs=None))],
                   nl=True)])
