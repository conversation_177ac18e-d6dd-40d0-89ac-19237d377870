Module(body=[ClassDef(name='A',
                      bases=[],
                      body=[FunctionDef(name='__nonzero__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Return(value=Str(s='not the right value'))],
                                        decorator_list=[])],
                      decorator_list=[]),
             TryExcept(body=[Print(dest=None,
                                   values=[Call(func=Name(id='bool',
                                                          ctx=Load()),
                                                args=[Call(func=Name(id='A',
                                                                     ctx=Load()),
                                                           args=[],
                                                           keywords=[],
                                                           starargs=None,
                                                           kwargs=None)],
                                                keywords=[],
                                                starargs=None,
                                                kwargs=None)],
                                   nl=True)],
                       handlers=[ExceptHandler(type=Name(id='TypeError',
                                                         ctx=Load()),
                                               name=Name(id='e',
                                                         ctx=Store()),
                                               body=[Print(dest=None,
                                                           values=[Name(id='e',
                                                                        ctx=Load())],
                                                           nl=True)])],
                       orelse=[]),
             ClassDef(name='B',
                      bases=[],
                      body=[FunctionDef(name='__len__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Return(value=Str(s='not the right value'))],
                                        decorator_list=[])],
                      decorator_list=[]),
             TryExcept(body=[Print(dest=None,
                                   values=[Call(func=Name(id='bool',
                                                          ctx=Load()),
                                                args=[Call(func=Name(id='B',
                                                                     ctx=Load()),
                                                           args=[],
                                                           keywords=[],
                                                           starargs=None,
                                                           kwargs=None)],
                                                keywords=[],
                                                starargs=None,
                                                kwargs=None)],
                                   nl=True)],
                       handlers=[ExceptHandler(type=Name(id='TypeError',
                                                         ctx=Load()),
                                               name=Name(id='e',
                                                         ctx=Store()),
                                               body=[Print(dest=None,
                                                           values=[Name(id='e',
                                                                        ctx=Load())],
                                                           nl=True)])],
                       orelse=[])])
