Module(body=[Assign(targets=[Name(id='x',
                                  ctx=Store())],
                    value=List(elts=[],
                               ctx=Load())),
             Expr(value=Call(func=Attribute(value=Name(id='x',
                                                       ctx=Load()),
                                            attr='append',
                                            ctx=Load()),
                             args=[Name(id='x',
                                        ctx=Load())],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Compare(left=Name(id='x',
                                             ctx=Load()),
                                   ops=[Lt()],
                                   comparators=[Name(id='x',
                                                     ctx=Load())])],
                   nl=True)])
