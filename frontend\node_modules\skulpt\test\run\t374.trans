Module(body=[Import(names=[alias(name='re',
                                 asname=None)]),
             FunctionDef(name='f',
                         args=arguments(args=[Name(id='a',
                                                   ctx=Param()),
                                              Name(id='b',
                                                   ctx=Param()),
                                              Name(id='c',
                                                   ctx=Param())],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[Num(n=3),
                                                  Name(id='None',
                                                       ctx=Load())]),
                         body=[Print(dest=None,
                                     values=[Name(id='a',
                                                  ctx=Load()),
                                             Name(id='b',
                                                  ctx=Load()),
                                             Name(id='c',
                                                  ctx=Load())],
                                     nl=True)],
                         decorator_list=[]),
             Expr(value=Call(func=Name(id='f',
                                       ctx=Load()),
                             args=[Num(n=1)],
                             keywords=[keyword(arg='c',
                                               value=Num(n=4))],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='split',
                                               ctx=Load()),
                                args=[Str(s='a'),
                                      Str(s='A stitch in time saves nine.')],
                                keywords=[keyword(arg='flags',
                                                  value=Attribute(value=Name(id='re',
                                                                             ctx=Load()),
                                                                  attr='IGNORECASE',
                                                                  ctx=Load()))],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='re',
                                                          ctx=Load()),
                                               attr='findall',
                                               ctx=Load()),
                                args=[],
                                keywords=[keyword(arg='string',
                                                  value=Str(s='A stitch in time saves nine.')),
                                          keyword(arg='flags',
                                                  value=Attribute(value=Name(id='re',
                                                                             ctx=Load()),
                                                                  attr='IGNORECASE',
                                                                  ctx=Load())),
                                          keyword(arg='pattern',
                                                  value=Str(s='a'))],
                                starargs=None,
                                kwargs=None)],
                   nl=True)])
