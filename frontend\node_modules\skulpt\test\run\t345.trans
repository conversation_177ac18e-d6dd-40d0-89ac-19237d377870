Module(body=[ImportFrom(module='math',
                        names=[alias(name='*',
                                     asname=None)],
                        level=0),
             Print(dest=None,
                   values=[Call(func=Name(id='cos',
                                          ctx=Load()),
                                args=[Name(id='pi',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Name(id='pi',
                                ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='log',
                                          ctx=Load()),
                                args=[Num(n=100),
                                      Num(n=2)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='pow',
                                          ctx=Load()),
                                args=[Num(n=2),
                                      Num(n=24)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True)])
