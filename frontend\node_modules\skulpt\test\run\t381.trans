Module(body=[Print(dest=None,
                   values=[Call(func=Name(id='max',
                                          ctx=Load()),
                                args=[Num(n=1),
                                      Num(n=2),
                                      Num(n=3)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Assign(targets=[Name(id='l',
                                  ctx=Store())],
                    value=List(elts=[Num(n=1),
                                     Num(n=2),
                                     Num(n=3)],
                               ctx=Load())),
             Print(dest=None,
                   values=[Call(func=Name(id='max',
                                          ctx=Load()),
                                args=[Name(id='l',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='max',
                                          ctx=Load()),
                                args=[],
                                keywords=[],
                                starargs=Name(id='l',
                                              ctx=Load()),
                                kwargs=None)],
                   nl=True)])
