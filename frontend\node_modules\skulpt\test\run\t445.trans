Module(body=[Assign(targets=[Name(id='square',
                                  ctx=Store())],
                    value=Lambda(args=arguments(args=[Name(id='x',
                                                           ctx=Param())],
                                                vararg=None,
                                                kwarg=None,
                                                defaults=[]),
                                 body=BinOp(left=Name(id='x',
                                                      ctx=Load()),
                                            op=Pow(),
                                            right=Num(n=2)))),
             FunctionDef(name='test1',
                         args=arguments(args=[Name(id='f',
                                                   ctx=Param()),
                                              Name(id='x',
                                                   ctx=Param())],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[]),
                         body=[Return(value=Call(func=Name(id='f',
                                                           ctx=Load()),
                                                 args=[Name(id='x',
                                                            ctx=Load())],
                                                 keywords=[],
                                                 starargs=None,
                                                 kwargs=None))],
                         decorator_list=[]),
             FunctionDef(name='test2',
                         args=arguments(args=[Name(id='f',
                                                   ctx=Param()),
                                              Name(id='x',
                                                   ctx=Param()),
                                              Name(id='y',
                                                   ctx=Param())],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[]),
                         body=[Return(value=Call(func=Name(id='f',
                                                           ctx=Load()),
                                                 args=[Name(id='x',
                                                            ctx=Load()),
                                                       Name(id='y',
                                                            ctx=Load())],
                                                 keywords=[],
                                                 starargs=None,
                                                 kwargs=None))],
                         decorator_list=[]),
             Print(dest=None,
                   values=[Call(func=Name(id='square',
                                          ctx=Load()),
                                args=[Num(n=2)],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Call(func=Name(id='test1',
                                          ctx=Load()),
                                args=[Name(id='square',
                                           ctx=Load()),
                                      Num(n=2)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Lambda(args=arguments(args=[Name(id='x',
                                                                      ctx=Param())],
                                                           vararg=None,
                                                           kwarg=None,
                                                           defaults=[]),
                                            body=BinOp(left=Name(id='x',
                                                                 ctx=Load()),
                                                       op=Add(),
                                                       right=Num(n=5))),
                                args=[Num(n=4)],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Call(func=Name(id='test1',
                                          ctx=Load()),
                                args=[Lambda(args=arguments(args=[Name(id='x',
                                                                       ctx=Param())],
                                                            vararg=None,
                                                            kwarg=None,
                                                            defaults=[]),
                                             body=BinOp(left=Name(id='x',
                                                                  ctx=Load()),
                                                        op=Add(),
                                                        right=Num(n=5))),
                                      Num(n=4)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Lambda(args=arguments(args=[Name(id='x',
                                                                      ctx=Param()),
                                                                 Name(id='y',
                                                                      ctx=Param())],
                                                           vararg=None,
                                                           kwarg=None,
                                                           defaults=[]),
                                            body=BinOp(left=Name(id='x',
                                                                 ctx=Load()),
                                                       op=Sub(),
                                                       right=Name(id='y',
                                                                  ctx=Load()))),
                                args=[Num(n=5),
                                      Num(n=4)],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Call(func=Name(id='test2',
                                          ctx=Load()),
                                args=[Lambda(args=arguments(args=[Name(id='x',
                                                                       ctx=Param()),
                                                                  Name(id='y',
                                                                       ctx=Param())],
                                                            vararg=None,
                                                            kwarg=None,
                                                            defaults=[]),
                                             body=BinOp(left=Name(id='x',
                                                                  ctx=Load()),
                                                        op=Sub(),
                                                        right=Name(id='y',
                                                                   ctx=Load()))),
                                      Num(n=5),
                                      Num(n=4)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Lambda(args=arguments(args=[Name(id='x',
                                                                      ctx=Param()),
                                                                 Name(id='y',
                                                                      ctx=Param())],
                                                           vararg=None,
                                                           kwarg=None,
                                                           defaults=[]),
                                            body=BinOp(left=Subscript(value=Name(id='x',
                                                                                 ctx=Load()),
                                                                      slice=Index(value=Name(id='y',
                                                                                             ctx=Load())),
                                                                      ctx=Load()),
                                                       op=Mult(),
                                                       right=Num(n=2))),
                                args=[List(elts=[Num(n=0),
                                                 Num(n=1),
                                                 Num(n=2),
                                                 Num(n=3),
                                                 Num(n=4)],
                                           ctx=Load()),
                                      Num(n=4)],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Call(func=Name(id='test2',
                                          ctx=Load()),
                                args=[Lambda(args=arguments(args=[Name(id='x',
                                                                       ctx=Param()),
                                                                  Name(id='y',
                                                                       ctx=Param())],
                                                            vararg=None,
                                                            kwarg=None,
                                                            defaults=[]),
                                             body=BinOp(left=Subscript(value=Name(id='x',
                                                                                  ctx=Load()),
                                                                       slice=Index(value=Name(id='y',
                                                                                              ctx=Load())),
                                                                       ctx=Load()),
                                                        op=Mult(),
                                                        right=Num(n=2))),
                                      List(elts=[Num(n=0),
                                                 Num(n=1),
                                                 Num(n=2),
                                                 Num(n=3),
                                                 Num(n=4)],
                                           ctx=Load()),
                                      Num(n=4)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             FunctionDef(name='test3',
                         args=arguments(args=[Name(id='f',
                                                   ctx=Param()),
                                              Name(id='g',
                                                   ctx=Param()),
                                              Name(id='x',
                                                   ctx=Param()),
                                              Name(id='y',
                                                   ctx=Param())],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[]),
                         body=[Return(value=Tuple(elts=[Call(func=Name(id='f',
                                                                       ctx=Load()),
                                                             args=[Name(id='x',
                                                                        ctx=Load())],
                                                             keywords=[],
                                                             starargs=None,
                                                             kwargs=None),
                                                        Call(func=Name(id='f',
                                                                       ctx=Load()),
                                                             args=[Name(id='y',
                                                                        ctx=Load())],
                                                             keywords=[],
                                                             starargs=None,
                                                             kwargs=None),
                                                        Call(func=Name(id='g',
                                                                       ctx=Load()),
                                                             args=[Name(id='x',
                                                                        ctx=Load()),
                                                                   Name(id='y',
                                                                        ctx=Load())],
                                                             keywords=[],
                                                             starargs=None,
                                                             kwargs=None),
                                                        Call(func=Name(id='g',
                                                                       ctx=Load()),
                                                             args=[Call(func=Name(id='f',
                                                                                  ctx=Load()),
                                                                        args=[Name(id='x',
                                                                                   ctx=Load())],
                                                                        keywords=[],
                                                                        starargs=None,
                                                                        kwargs=None),
                                                                   Call(func=Name(id='f',
                                                                                  ctx=Load()),
                                                                        args=[Name(id='y',
                                                                                   ctx=Load())],
                                                                        keywords=[],
                                                                        starargs=None,
                                                                        kwargs=None)],
                                                             keywords=[],
                                                             starargs=None,
                                                             kwargs=None),
                                                        Call(func=Name(id='f',
                                                                       ctx=Load()),
                                                             args=[Call(func=Name(id='g',
                                                                                  ctx=Load()),
                                                                        args=[Name(id='x',
                                                                                   ctx=Load()),
                                                                              Name(id='y',
                                                                                   ctx=Load())],
                                                                        keywords=[],
                                                                        starargs=None,
                                                                        kwargs=None)],
                                                             keywords=[],
                                                             starargs=None,
                                                             kwargs=None),
                                                        Call(func=Name(id='f',
                                                                       ctx=Load()),
                                                             args=[Call(func=Name(id='g',
                                                                                  ctx=Load()),
                                                                        args=[Name(id='y',
                                                                                   ctx=Load()),
                                                                              Name(id='x',
                                                                                   ctx=Load())],
                                                                        keywords=[],
                                                                        starargs=None,
                                                                        kwargs=None)],
                                                             keywords=[],
                                                             starargs=None,
                                                             kwargs=None)],
                                                  ctx=Load()))],
                         decorator_list=[]),
             Assign(targets=[Name(id='f',
                                  ctx=Store())],
                    value=Lambda(args=arguments(args=[Name(id='x',
                                                           ctx=Param())],
                                                vararg=None,
                                                kwarg=None,
                                                defaults=[]),
                                 body=BinOp(left=Name(id='x',
                                                      ctx=Load()),
                                            op=Mult(),
                                            right=Num(n=27)))),
             Assign(targets=[Name(id='g',
                                  ctx=Store())],
                    value=Lambda(args=arguments(args=[Name(id='x',
                                                           ctx=Param()),
                                                      Name(id='y',
                                                           ctx=Param())],
                                                vararg=None,
                                                kwarg=None,
                                                defaults=[]),
                                 body=BinOp(left=Name(id='y',
                                                      ctx=Load()),
                                            op=Add(),
                                            right=BinOp(left=Num(n=12),
                                                        op=Mult(),
                                                        right=Name(id='x',
                                                                   ctx=Load()))))),
             Assign(targets=[Name(id='h',
                                  ctx=Store())],
                    value=Lambda(args=arguments(args=[Name(id='x',
                                                           ctx=Param())],
                                                vararg=None,
                                                kwarg=None,
                                                defaults=[]),
                                 body=Call(func=Name(id='f',
                                                     ctx=Load()),
                                           args=[Name(id='x',
                                                      ctx=Load())],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None))),
             Assign(targets=[Name(id='i',
                                  ctx=Store())],
                    value=Lambda(args=arguments(args=[Name(id='x',
                                                           ctx=Param()),
                                                      Name(id='y',
                                                           ctx=Param())],
                                                vararg=None,
                                                kwarg=None,
                                                defaults=[]),
                                 body=Call(func=Name(id='g',
                                                     ctx=Load()),
                                           args=[Name(id='x',
                                                      ctx=Load()),
                                                 Name(id='y',
                                                      ctx=Load())],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None))),
             Print(dest=None,
                   values=[Tuple(elts=[Call(func=Name(id='f',
                                                      ctx=Load()),
                                            args=[Num(n=3)],
                                            keywords=[],
                                            starargs=None,
                                            kwargs=None),
                                       Call(func=Name(id='f',
                                                      ctx=Load()),
                                            args=[Num(n=4)],
                                            keywords=[],
                                            starargs=None,
                                            kwargs=None),
                                       Call(func=Name(id='g',
                                                      ctx=Load()),
                                            args=[Num(n=3),
                                                  Num(n=4)],
                                            keywords=[],
                                            starargs=None,
                                            kwargs=None),
                                       Call(func=Name(id='g',
                                                      ctx=Load()),
                                            args=[Call(func=Name(id='f',
                                                                 ctx=Load()),
                                                       args=[Num(n=3)],
                                                       keywords=[],
                                                       starargs=None,
                                                       kwargs=None),
                                                  Call(func=Name(id='f',
                                                                 ctx=Load()),
                                                       args=[Num(n=4)],
                                                       keywords=[],
                                                       starargs=None,
                                                       kwargs=None)],
                                            keywords=[],
                                            starargs=None,
                                            kwargs=None),
                                       Call(func=Name(id='f',
                                                      ctx=Load()),
                                            args=[Call(func=Name(id='g',
                                                                 ctx=Load()),
                                                       args=[Num(n=3),
                                                             Num(n=4)],
                                                       keywords=[],
                                                       starargs=None,
                                                       kwargs=None)],
                                            keywords=[],
                                            starargs=None,
                                            kwargs=None),
                                       Call(func=Name(id='f',
                                                      ctx=Load()),
                                            args=[Call(func=Name(id='g',
                                                                 ctx=Load()),
                                                       args=[Num(n=4),
                                                             Num(n=3)],
                                                       keywords=[],
                                                       starargs=None,
                                                       kwargs=None)],
                                            keywords=[],
                                            starargs=None,
                                            kwargs=None)],
                                 ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='test3',
                                          ctx=Load()),
                                args=[Name(id='f',
                                           ctx=Load()),
                                      Name(id='g',
                                           ctx=Load()),
                                      Num(n=3),
                                      Num(n=4)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='test3',
                                          ctx=Load()),
                                args=[Name(id='h',
                                           ctx=Load()),
                                      Name(id='i',
                                           ctx=Load()),
                                      Num(n=3),
                                      Num(n=4)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Assign(targets=[Name(id='j',
                                  ctx=Store())],
                    value=Lambda(args=arguments(args=[Name(id='lst',
                                                           ctx=Param()),
                                                      Name(id='num',
                                                           ctx=Param()),
                                                      Name(id='func',
                                                           ctx=Param())],
                                                vararg=None,
                                                kwarg=None,
                                                defaults=[]),
                                 body=BinOp(left=Subscript(value=Name(id='lst',
                                                                      ctx=Load()),
                                                           slice=Index(value=Call(func=Name(id='func',
                                                                                            ctx=Load()),
                                                                                  args=[Name(id='lst',
                                                                                             ctx=Load()),
                                                                                        Name(id='num',
                                                                                             ctx=Load())],
                                                                                  keywords=[],
                                                                                  starargs=None,
                                                                                  kwargs=None)),
                                                           ctx=Load()),
                                            op=Mult(),
                                            right=Call(func=Lambda(args=arguments(args=[Name(id='y',
                                                                                             ctx=Param())],
                                                                                  vararg=None,
                                                                                  kwarg=None,
                                                                                  defaults=[]),
                                                                   body=BinOp(left=Num(n=10),
                                                                              op=Mult(),
                                                                              right=Name(id='y',
                                                                                         ctx=Load()))),
                                                       args=[Name(id='num',
                                                                  ctx=Load())],
                                                       keywords=[],
                                                       starargs=None,
                                                       kwargs=None)))),
             Assign(targets=[Name(id='k',
                                  ctx=Store())],
                    value=Lambda(args=arguments(args=[Name(id='x',
                                                           ctx=Param()),
                                                      Name(id='y',
                                                           ctx=Param())],
                                                vararg=None,
                                                kwarg=None,
                                                defaults=[]),
                                 body=BinOp(left=Call(func=Name(id='len',
                                                                ctx=Load()),
                                                      args=[Name(id='x',
                                                                 ctx=Load())],
                                                      keywords=[],
                                                      starargs=None,
                                                      kwargs=None),
                                            op=Sub(),
                                            right=Name(id='y',
                                                       ctx=Load())))),
             FunctionDef(name='test4',
                         args=arguments(args=[Name(id='f',
                                                   ctx=Param()),
                                              Name(id='x',
                                                   ctx=Param()),
                                              Name(id='y',
                                                   ctx=Param()),
                                              Name(id='z',
                                                   ctx=Param())],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[]),
                         body=[Return(value=Call(func=Name(id='f',
                                                           ctx=Load()),
                                                 args=[Name(id='x',
                                                            ctx=Load()),
                                                       Name(id='y',
                                                            ctx=Load()),
                                                       Name(id='z',
                                                            ctx=Load())],
                                                 keywords=[],
                                                 starargs=None,
                                                 kwargs=None))],
                         decorator_list=[]),
             Print(dest=None,
                   values=[Call(func=Name(id='j',
                                          ctx=Load()),
                                args=[List(elts=[Num(n=1),
                                                 Num(n=2),
                                                 Num(n=3),
                                                 Num(n=4),
                                                 Num(n=5),
                                                 Num(n=6)],
                                           ctx=Load()),
                                      Num(n=2),
                                      Name(id='k',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='test4',
                                          ctx=Load()),
                                args=[Name(id='j',
                                           ctx=Load()),
                                      List(elts=[Num(n=1),
                                                 Num(n=2),
                                                 Num(n=3),
                                                 Num(n=4),
                                                 Num(n=5),
                                                 Num(n=6)],
                                           ctx=Load()),
                                      Num(n=2),
                                      Name(id='k',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True)])
