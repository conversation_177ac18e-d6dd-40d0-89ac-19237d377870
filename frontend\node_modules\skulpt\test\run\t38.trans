Module(body=[Assign(targets=[Name(id='X',
                                  ctx=Store())],
                    value=Str(s='OK')),
             FunctionDef(name='test',
                         args=arguments(args=[],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[]),
                         body=[Print(dest=None,
                                     values=[Name(id='X',
                                                  ctx=Load())],
                                     nl=True)],
                         decorator_list=[]),
             Expr(value=Call(func=Name(id='test',
                                       ctx=Load()),
                             args=[],
                             keywords=[],
                             starargs=None,
                             kwargs=None))])
