Module(body=[ClassDef(name='A',
                      bases=[Name(id='object',
                                  ctx=Load())],
                      body=[FunctionDef(name='__init__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Assign(targets=[Attribute(value=Name(id='self',
                                                                                   ctx=Load()),
                                                                        attr='a',
                                                                        ctx=Store())],
                                                     value=Num(n=1)),
                                              Assign(targets=[Attribute(value=Name(id='self',
                                                                                   ctx=Load()),
                                                                        attr='b',
                                                                        ctx=Store())],
                                                     value=Num(n=2)),
                                              Assign(targets=[Attribute(value=Name(id='self',
                                                                                   ctx=Load()),
                                                                        attr='c',
                                                                        ctx=Store())],
                                                     value=Num(n=3))],
                                        decorator_list=[])],
                      decorator_list=[]),
             ClassDef(name='B',
                      bases=[Name(id='A',
                                  ctx=Load())],
                      body=[FunctionDef(name='__init__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Expr(value=Call(func=Attribute(value=Name(id='A',
                                                                                        ctx=Load()),
                                                                             attr='__init__',
                                                                             ctx=Load()),
                                                              args=[Name(id='self',
                                                                         ctx=Load())],
                                                              keywords=[],
                                                              starargs=None,
                                                              kwargs=None)),
                                              Assign(targets=[Attribute(value=Name(id='self',
                                                                                   ctx=Load()),
                                                                        attr='d',
                                                                        ctx=Store())],
                                                     value=Num(n=4))],
                                        decorator_list=[])],
                      decorator_list=[]),
             ClassDef(name='C',
                      bases=[Name(id='B',
                                  ctx=Load())],
                      body=[FunctionDef(name='__init__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Expr(value=Call(func=Attribute(value=Name(id='B',
                                                                                        ctx=Load()),
                                                                             attr='__init__',
                                                                             ctx=Load()),
                                                              args=[Name(id='self',
                                                                         ctx=Load())],
                                                              keywords=[],
                                                              starargs=None,
                                                              kwargs=None))],
                                        decorator_list=[]),
                            FunctionDef(name='__dir__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Return(value=List(elts=[Str(s='a'),
                                                                      Str(s='b'),
                                                                      Str(s='c'),
                                                                      Str(s='d')],
                                                                ctx=Load()))],
                                        decorator_list=[])],
                      decorator_list=[]),
             Print(dest=None,
                   values=[Call(func=Name(id='dir',
                                          ctx=Load()),
                                args=[Call(func=Name(id='A',
                                                     ctx=Load()),
                                           args=[],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='dir',
                                          ctx=Load()),
                                args=[Call(func=Name(id='B',
                                                     ctx=Load()),
                                           args=[],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='dir',
                                          ctx=Load()),
                                args=[Call(func=Name(id='C',
                                                     ctx=Load()),
                                           args=[],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True)])
