Module(body=[ClassDef(name='Foo',
                      bases=[],
                      body=[FunctionDef(name='__init__',
                                        args=arguments(args=[Name(id='self',
                                                                  ctx=Param())],
                                                       vararg=None,
                                                       kwarg=None,
                                                       defaults=[]),
                                        body=[Assign(targets=[Attribute(value=Name(id='self',
                                                                                   ctx=Load()),
                                                                        attr='x',
                                                                        ctx=Store())],
                                                     value=Num(n=3))],
                                        decorator_list=[])],
                      decorator_list=[]),
             Assign(targets=[Name(id='f',
                                  ctx=Store())],
                    value=Call(func=Name(id='Foo',
                                         ctx=Load()),
                               args=[],
                               keywords=[],
                               starargs=None,
                               kwargs=None)),
             Expr(value=Subscript(value=Name(id='None',
                                             ctx=Load()),
                                  slice=Index(value=Num(n=4)),
                                  ctx=Load())),
             Expr(value=Subscript(value=Name(id='f',
                                             ctx=Load()),
                                  slice=Index(value=Num(n=4)),
                                  ctx=Load()))])
