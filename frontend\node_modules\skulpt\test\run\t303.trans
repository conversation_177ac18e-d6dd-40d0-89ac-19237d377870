Module(body=[Assign(targets=[Name(id='l',
                                  ctx=Store())],
                    value=List(elts=[Num(n=1),
                                     Num(n=2),
                                     Num(n=3),
                                     Num(n=1)],
                               ctx=Load())),
             Print(dest=None,
                   values=[Name(id='l',
                                ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='l',
                                             ctx=Load()),
                                   ops=[Gt()],
                                   comparators=[Name(id='l',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='l',
                                             ctx=Load()),
                                   ops=[GtE()],
                                   comparators=[Name(id='l',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='l',
                                             ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Name(id='l',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='l',
                                             ctx=Load()),
                                   ops=[NotEq()],
                                   comparators=[Name(id='l',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='l',
                                             ctx=Load()),
                                   ops=[LtE()],
                                   comparators=[Name(id='l',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='l',
                                             ctx=Load()),
                                   ops=[Lt()],
                                   comparators=[Name(id='l',
                                                     ctx=Load())])],
                   nl=True)])
