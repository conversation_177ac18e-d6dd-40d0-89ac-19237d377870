Sym_type: module
Sym_name: top
Sym_lineno: 0
Sym_nested: False
Sym_haschildren: True
-- Identifiers --
name: foo
  is_referenced: True
  is_imported: False
  is_parameter: False
  is_global: False
  is_declared_global: False
  is_local: True
  is_free: False
  is_assigned: True
  is_namespace: True
  namespaces: [
    Sym_type: function
    Sym_name: foo
    Sym_lineno: 7
    Sym_nested: False
    Sym_haschildren: False
    Func_params: ['x', 'y']
    Func_locals: ['x', 'y']
    Func_globals: []
    Func_frees: []
    -- Identifiers --
    name: x
      is_referenced: True
      is_imported: False
      is_parameter: True
      is_global: False
      is_declared_global: False
      is_local: True
      is_free: False
      is_assigned: False
      is_namespace: False
      namespaces: [
      ]
    name: y
      is_referenced: True
      is_imported: False
      is_parameter: True
      is_global: False
      is_declared_global: False
      is_local: True
      is_free: False
      is_assigned: False
      is_namespace: False
      namespaces: [
      ]
  ]
name: int
  is_referenced: True
  is_imported: False
  is_parameter: False
  is_global: True
  is_declared_global: False
  is_local: False
  is_free: False
  is_assigned: False
  is_namespace: False
  namespaces: [
  ]
