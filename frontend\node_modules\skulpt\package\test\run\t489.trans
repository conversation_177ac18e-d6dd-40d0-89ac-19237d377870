Module(body=[Import(names=[alias(name='random',
                                 asname=None)]),
             FunctionDef(name='makeset',
                         args=arguments(args=[Name(id='lst',
                                                   ctx=Param())],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[]),
                         body=[Assign(targets=[Name(id='result',
                                                    ctx=Store())],
                                      value=Dict(keys=[],
                                                 values=[])),
                               For(target=Name(id='a',
                                               ctx=Store()),
                                   iter=Name(id='lst',
                                             ctx=Load()),
                                   body=[If(test=UnaryOp(op=Not(),
                                                         operand=Call(func=Attribute(value=Name(id='result',
                                                                                                ctx=Load()),
                                                                                     attr='has_key',
                                                                                     ctx=Load()),
                                                                      args=[Name(id='a',
                                                                                 ctx=Load())],
                                                                      keywords=[],
                                                                      starargs=None,
                                                                      kwargs=None)),
                                            body=[Assign(targets=[Subscript(value=Name(id='result',
                                                                                       ctx=Load()),
                                                                            slice=Index(value=Name(id='a',
                                                                                                   ctx=Load())),
                                                                            ctx=Store())],
                                                         value=List(elts=[],
                                                                    ctx=Load()))],
                                            orelse=[]),
                                         Expr(value=Call(func=Attribute(value=Subscript(value=Name(id='result',
                                                                                                   ctx=Load()),
                                                                                        slice=Index(value=Name(id='a',
                                                                                                               ctx=Load())),
                                                                                        ctx=Load()),
                                                                        attr='append',
                                                                        ctx=Load()),
                                                         args=[Name(id='True',
                                                                    ctx=Load())],
                                                         keywords=[],
                                                         starargs=None,
                                                         kwargs=None))],
                                   orelse=[]),
                               Return(value=Name(id='result',
                                                 ctx=Load()))],
                         decorator_list=[]),
             FunctionDef(name='sorttest',
                         args=arguments(args=[Name(id='lst1',
                                                   ctx=Param())],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[]),
                         body=[Assign(targets=[Name(id='lst2',
                                                    ctx=Store())],
                                      value=Subscript(value=Name(id='lst1',
                                                                 ctx=Load()),
                                                      slice=Slice(lower=None,
                                                                  upper=None,
                                                                  step=None),
                                                      ctx=Load())),
                               Expr(value=Call(func=Attribute(value=Name(id='lst2',
                                                                         ctx=Load()),
                                                              attr='sort',
                                                              ctx=Load()),
                                               args=[],
                                               keywords=[],
                                               starargs=None,
                                               kwargs=None)),
                               Assert(test=Compare(left=Call(func=Name(id='len',
                                                                       ctx=Load()),
                                                             args=[Name(id='lst1',
                                                                        ctx=Load())],
                                                             keywords=[],
                                                             starargs=None,
                                                             kwargs=None),
                                                   ops=[Eq()],
                                                   comparators=[Call(func=Name(id='len',
                                                                               ctx=Load()),
                                                                     args=[Name(id='lst2',
                                                                                ctx=Load())],
                                                                     keywords=[],
                                                                     starargs=None,
                                                                     kwargs=None)]),
                                      msg=None),
                               Assert(test=Compare(left=Call(func=Name(id='makeset',
                                                                       ctx=Load()),
                                                             args=[Name(id='lst1',
                                                                        ctx=Load())],
                                                             keywords=[],
                                                             starargs=None,
                                                             kwargs=None),
                                                   ops=[Eq()],
                                                   comparators=[Call(func=Name(id='makeset',
                                                                               ctx=Load()),
                                                                     args=[Name(id='lst2',
                                                                                ctx=Load())],
                                                                     keywords=[],
                                                                     starargs=None,
                                                                     kwargs=None)]),
                                      msg=None),
                               Assign(targets=[Name(id='position',
                                                    ctx=Store())],
                                      value=Dict(keys=[],
                                                 values=[])),
                               Assign(targets=[Name(id='i',
                                                    ctx=Store())],
                                      value=Num(n=0)),
                               Assign(targets=[Name(id='err',
                                                    ctx=Store())],
                                      value=Name(id='False',
                                                 ctx=Load())),
                               For(target=Name(id='a',
                                               ctx=Store()),
                                   iter=Name(id='lst1',
                                             ctx=Load()),
                                   body=[If(test=UnaryOp(op=Not(),
                                                         operand=Call(func=Attribute(value=Name(id='position',
                                                                                                ctx=Load()),
                                                                                     attr='has_key',
                                                                                     ctx=Load()),
                                                                      args=[Name(id='a',
                                                                                 ctx=Load())],
                                                                      keywords=[],
                                                                      starargs=None,
                                                                      kwargs=None)),
                                            body=[Assign(targets=[Subscript(value=Name(id='position',
                                                                                       ctx=Load()),
                                                                            slice=Index(value=Name(id='a',
                                                                                                   ctx=Load())),
                                                                            ctx=Store())],
                                                         value=List(elts=[],
                                                                    ctx=Load()))],
                                            orelse=[]),
                                         Expr(value=Call(func=Attribute(value=Subscript(value=Name(id='position',
                                                                                                   ctx=Load()),
                                                                                        slice=Index(value=Name(id='a',
                                                                                                               ctx=Load())),
                                                                                        ctx=Load()),
                                                                        attr='append',
                                                                        ctx=Load()),
                                                         args=[Name(id='i',
                                                                    ctx=Load())],
                                                         keywords=[],
                                                         starargs=None,
                                                         kwargs=None)),
                                         AugAssign(target=Name(id='i',
                                                               ctx=Store()),
                                                   op=Add(),
                                                   value=Num(n=1))],
                                   orelse=[]),
                               For(target=Name(id='i',
                                               ctx=Store()),
                                   iter=Call(func=Name(id='range',
                                                       ctx=Load()),
                                             args=[BinOp(left=Call(func=Name(id='len',
                                                                             ctx=Load()),
                                                                   args=[Name(id='lst2',
                                                                              ctx=Load())],
                                                                   keywords=[],
                                                                   starargs=None,
                                                                   kwargs=None),
                                                         op=Sub(),
                                                         right=Num(n=1))],
                                             keywords=[],
                                             starargs=None,
                                             kwargs=None),
                                   body=[Assign(targets=[Tuple(elts=[Name(id='a',
                                                                          ctx=Store()),
                                                                     Name(id='b',
                                                                          ctx=Store())],
                                                               ctx=Store())],
                                                value=Tuple(elts=[Subscript(value=Name(id='lst2',
                                                                                       ctx=Load()),
                                                                            slice=Index(value=Name(id='i',
                                                                                                   ctx=Load())),
                                                                            ctx=Load()),
                                                                  Subscript(value=Name(id='lst2',
                                                                                       ctx=Load()),
                                                                            slice=Index(value=BinOp(left=Name(id='i',
                                                                                                              ctx=Load()),
                                                                                                    op=Add(),
                                                                                                    right=Num(n=1))),
                                                                            ctx=Load())],
                                                            ctx=Load())),
                                         If(test=UnaryOp(op=Not(),
                                                         operand=Compare(left=Name(id='a',
                                                                                   ctx=Load()),
                                                                         ops=[LtE()],
                                                                         comparators=[Name(id='b',
                                                                                           ctx=Load())])),
                                            body=[Print(dest=None,
                                                        values=[Str(s='resulting list is not sorted')],
                                                        nl=True),
                                                  Assign(targets=[Name(id='err',
                                                                       ctx=Store())],
                                                         value=Name(id='True',
                                                                    ctx=Load()))],
                                            orelse=[]),
                                         If(test=Compare(left=Name(id='a',
                                                                   ctx=Load()),
                                                         ops=[Eq()],
                                                         comparators=[Name(id='b',
                                                                           ctx=Load())]),
                                            body=[If(test=UnaryOp(op=Not(),
                                                                  operand=Compare(left=Subscript(value=Subscript(value=Name(id='position',
                                                                                                                            ctx=Load()),
                                                                                                                 slice=Index(value=Name(id='a',
                                                                                                                                        ctx=Load())),
                                                                                                                 ctx=Load()),
                                                                                                 slice=Index(value=Num(n=0)),
                                                                                                 ctx=Load()),
                                                                                  ops=[Lt()],
                                                                                  comparators=[Subscript(value=Subscript(value=Name(id='position',
                                                                                                                                    ctx=Load()),
                                                                                                                         slice=Index(value=Name(id='b',
                                                                                                                                                ctx=Load())),
                                                                                                                         ctx=Load()),
                                                                                                         slice=Index(value=Num(n=-1)),
                                                                                                         ctx=Load())])),
                                                     body=[Print(dest=None,
                                                                 values=[Str(s='not stable')],
                                                                 nl=True),
                                                           Assign(targets=[Name(id='err',
                                                                                ctx=Store())],
                                                                  value=Name(id='True',
                                                                             ctx=Load()))],
                                                     orelse=[])],
                                            orelse=[])],
                                   orelse=[]),
                               If(test=Name(id='err',
                                            ctx=Load()),
                                  body=[Print(dest=None,
                                              values=[Name(id='lst1',
                                                           ctx=Load())],
                                              nl=True),
                                        Print(dest=None,
                                              values=[Name(id='lst2',
                                                           ctx=Load())],
                                              nl=True)],
                                  orelse=[])],
                         decorator_list=[]),
             For(target=Name(id='v',
                             ctx=Store()),
                 iter=Call(func=Name(id='range',
                                     ctx=Load()),
                           args=[Num(n=137)],
                           keywords=[],
                           starargs=None,
                           kwargs=None),
                 body=[Assign(targets=[Name(id='up',
                                            ctx=Store())],
                              value=BinOp(left=Num(n=1),
                                          op=Add(),
                                          right=Call(func=Name(id='int',
                                                               ctx=Load()),
                                                     args=[BinOp(left=BinOp(left=Name(id='v',
                                                                                      ctx=Load()),
                                                                            op=Mult(),
                                                                            right=Call(func=Attribute(value=Name(id='random',
                                                                                                                 ctx=Load()),
                                                                                                      attr='random',
                                                                                                      ctx=Load()),
                                                                                       args=[],
                                                                                       keywords=[],
                                                                                       starargs=None,
                                                                                       kwargs=None)),
                                                                 op=Mult(),
                                                                 right=Num(n=2.7))],
                                                     keywords=[],
                                                     starargs=None,
                                                     kwargs=None))),
                       Assign(targets=[Name(id='lst1',
                                            ctx=Store())],
                              value=ListComp(elt=Call(func=Attribute(value=Name(id='random',
                                                                                ctx=Load()),
                                                                     attr='randrange',
                                                                     ctx=Load()),
                                                      args=[Num(n=0),
                                                            Name(id='up',
                                                                 ctx=Load())],
                                                      keywords=[],
                                                      starargs=None,
                                                      kwargs=None),
                                             generators=[comprehension(target=Name(id='i',
                                                                                   ctx=Store()),
                                                                       iter=Call(func=Name(id='range',
                                                                                           ctx=Load()),
                                                                                 args=[Name(id='v',
                                                                                            ctx=Load())],
                                                                                 keywords=[],
                                                                                 starargs=None,
                                                                                 kwargs=None),
                                                                       ifs=[])])),
                       Expr(value=Call(func=Name(id='sorttest',
                                                 ctx=Load()),
                                       args=[Name(id='lst1',
                                                  ctx=Load())],
                                       keywords=[],
                                       starargs=None,
                                       kwargs=None)),
                       Print(dest=None,
                             values=[Str(s="everything's fine")],
                             nl=True)],
                 orelse=[])])
