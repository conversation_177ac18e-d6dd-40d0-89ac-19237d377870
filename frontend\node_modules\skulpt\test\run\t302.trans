Module(body=[Assign(targets=[Name(id='d',
                                  ctx=Store())],
                    value=Dict(keys=[Str(s='foo')],
                               values=[Num(n=2)])),
             Print(dest=None,
                   values=[Call(func=Name(id='len',
                                          ctx=Load()),
                                args=[Name(id='d',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Name(id='d',
                                ctx=Load())],
                   nl=True),
             Assign(targets=[Subscript(value=Name(id='d',
                                                  ctx=Load()),
                                       slice=Index(value=Str(s='foo')),
                                       ctx=Store())],
                    value=Num(n=13)),
             Print(dest=None,
                   values=[Call(func=Name(id='len',
                                          ctx=Load()),
                                args=[Name(id='d',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None),
                           Name(id='d',
                                ctx=Load())],
                   nl=True)])
