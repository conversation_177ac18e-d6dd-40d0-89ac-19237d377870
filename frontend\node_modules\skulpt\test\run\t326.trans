Module(body=[Import(names=[alias(name='math',
                                 asname=None)]),
             Print(dest=None,
                   values=[BinOp(left=Str(s='%10.5f'),
                                 op=Mod(),
                                 right=Call(func=Attribute(value=Name(id='math',
                                                                      ctx=Load()),
                                                           attr='radians',
                                                           ctx=Load()),
                                            args=[Num(n=180)],
                                            keywords=[],
                                            starargs=None,
                                            kwargs=None))],
                   nl=True),
             Print(dest=None,
                   values=[BinOp(left=Str(s='%10.5f'),
                                 op=Mod(),
                                 right=Call(func=Attribute(value=Name(id='math',
                                                                      ctx=Load()),
                                                           attr='degrees',
                                                           ctx=Load()),
                                            args=[Call(func=Attribute(value=Name(id='math',
                                                                                 ctx=Load()),
                                                                      attr='radians',
                                                                      ctx=Load()),
                                                       args=[Num(n=180)],
                                                       keywords=[],
                                                       starargs=None,
                                                       kwargs=None)],
                                            keywords=[],
                                            starargs=None,
                                            kwargs=None))],
                   nl=True)])
