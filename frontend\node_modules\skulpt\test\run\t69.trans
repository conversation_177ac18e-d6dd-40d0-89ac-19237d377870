Module(body=[Assign(targets=[Name(id='x',
                                  ctx=Store())],
                    value=Dict(keys=[Num(n=1)],
                               values=[Num(n=2)])),
             Delete(targets=[Subscript(value=Name(id='x',
                                                  ctx=Load()),
                                       slice=Index(value=Num(n=1)),
                                       ctx=Del())]),
             Print(dest=None,
                   values=[Call(func=Name(id='len',
                                          ctx=Load()),
                                args=[Name(id='x',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True)])
