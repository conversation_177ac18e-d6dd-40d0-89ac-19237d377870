Module(body=[Assign(targets=[Name(id='x',
                                  ctx=Store())],
                    value=Str(s='Please make startswith and endswith work')),
             If(test=Call(func=Attribute(value=Name(id='x',
                                                    ctx=Load()),
                                         attr='startswith',
                                         ctx=Load()),
                          args=[Str(s='Please')],
                          keywords=[],
                          starargs=None,
                          kwargs=None),
                body=[Print(dest=None,
                            values=[Str(s='Starts with Please')],
                            nl=True)],
                orelse=[Print(dest=None,
                              values=[Str(s='Not good')],
                              nl=True)]),
             If(test=Call(func=Attribute(value=Name(id='x',
                                                    ctx=Load()),
                                         attr='endswith',
                                         ctx=Load()),
                          args=[Str(s='work')],
                          keywords=[],
                          starargs=None,
                          kwargs=None),
                body=[Print(dest=None,
                            values=[Str(s='Ends with work')],
                            nl=True)],
                orelse=[Print(dest=None,
                              values=[Str(s='Not good')],
                              nl=True)]),
             If(test=Call(func=Attribute(value=Name(id='x',
                                                    ctx=Load()),
                                         attr='startswith',
                                         ctx=Load()),
                          args=[Str(s='please')],
                          keywords=[],
                          starargs=None,
                          kwargs=None),
                body=[Print(dest=None,
                            values=[Str(s='Not good')],
                            nl=True)],
                orelse=[Print(dest=None,
                              values=[Str(s='Does not start with please')],
                              nl=True)]),
             If(test=Call(func=Attribute(value=Name(id='x',
                                                    ctx=Load()),
                                         attr='endswith',
                                         ctx=Load()),
                          args=[Str(s='please')],
                          keywords=[],
                          starargs=None,
                          kwargs=None),
                body=[Print(dest=None,
                            values=[Str(s='Not good')],
                            nl=True)],
                orelse=[Print(dest=None,
                              values=[Str(s='Does not end with please')],
                              nl=True)])])
