Sym_type: module
Sym_name: top
Sym_lineno: 0
Sym_nested: False
Sym_haschildren: True
-- Identifiers --
name: s
  is_referenced: True
  is_imported: False
  is_parameter: False
  is_global: False
  is_declared_global: False
  is_local: True
  is_free: False
  is_assigned: True
  is_namespace: False
  namespaces: [
  ]
name: stuff
  is_referenced: True
  is_imported: False
  is_parameter: False
  is_global: False
  is_declared_global: False
  is_local: True
  is_free: False
  is_assigned: True
  is_namespace: True
  namespaces: [
    Sym_type: class
    Sym_name: stuff
    Sym_lineno: 7
    Sym_nested: False
    Sym_haschildren: True
    Class_methods: ['toString', 'valueOf']
    -- Identifiers --
    name: toString
      is_referenced: False
      is_imported: False
      is_parameter: False
      is_global: False
      is_declared_global: False
      is_local: True
      is_free: False
      is_assigned: True
      is_namespace: True
      namespaces: [
        Sym_type: function
        Sym_name: toString
        Sym_lineno: 8
        Sym_nested: False
        Sym_haschildren: False
        Func_params: ['self']
        Func_locals: ['self']
        Func_globals: []
        Func_frees: []
        -- Identifiers --
        name: self
          is_referenced: False
          is_imported: False
          is_parameter: True
          is_global: False
          is_declared_global: False
          is_local: True
          is_free: False
          is_assigned: False
          is_namespace: False
          namespaces: [
          ]
      ]
    name: valueOf
      is_referenced: False
      is_imported: False
      is_parameter: False
      is_global: False
      is_declared_global: False
      is_local: True
      is_free: False
      is_assigned: True
      is_namespace: True
      namespaces: [
        Sym_type: function
        Sym_name: valueOf
        Sym_lineno: 10
        Sym_nested: False
        Sym_haschildren: False
        Func_params: ['self']
        Func_locals: ['self']
        Func_globals: []
        Func_frees: []
        -- Identifiers --
        name: self
          is_referenced: False
          is_imported: False
          is_parameter: True
          is_global: False
          is_declared_global: False
          is_local: True
          is_free: False
          is_assigned: False
          is_namespace: False
          namespaces: [
          ]
      ]
  ]
name: toString
  is_referenced: True
  is_imported: False
  is_parameter: False
  is_global: False
  is_declared_global: False
  is_local: True
  is_free: False
  is_assigned: True
  is_namespace: True
  namespaces: [
    Sym_type: function
    Sym_name: toString
    Sym_lineno: 4
    Sym_nested: False
    Sym_haschildren: False
    Func_params: []
    Func_locals: []
    Func_globals: []
    Func_frees: []
    -- Identifiers --
  ]
