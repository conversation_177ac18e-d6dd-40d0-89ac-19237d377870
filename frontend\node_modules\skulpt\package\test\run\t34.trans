Module(body=[Assign(targets=[Name(id='n',
                                  ctx=Store())],
                    value=Num(n=0)),
             For(target=Name(id='x',
                             ctx=Store()),
                 iter=Call(func=Name(id='range',
                                     ctx=Load()),
                           args=[Num(n=0),
                                 Num(n=10),
                                 Num(n=2)],
                           keywords=[],
                           starargs=None,
                           kwargs=None),
                 body=[AugAssign(target=Name(id='n',
                                             ctx=Store()),
                                 op=Add(),
                                 value=Num(n=1))],
                 orelse=[]),
             Print(dest=None,
                   values=[Name(id='n',
                                ctx=Load())],
                   nl=True)])
