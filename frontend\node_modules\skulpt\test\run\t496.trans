Module(body=[Print(dest=None,
                   values=[Call(func=Name(id='str',
                                          ctx=Load()),
                                args=[Name(id='None',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='repr',
                                          ctx=Load()),
                                args=[Name(id='None',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='type',
                                          ctx=Load()),
                                args=[Name(id='None',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='isinstance',
                                          ctx=Load()),
                                args=[Name(id='None',
                                           ctx=Load()),
                                      Call(func=Name(id='type',
                                                     ctx=Load()),
                                           args=[Name(id='None',
                                                      ctx=Load())],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Str(s='hello'),
                                               attr='split',
                                               ctx=Load()),
                                args=[Name(id='None',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             If(test=Name(id='None',
                          ctx=Load()),
                body=[Print(dest=None,
                            values=[Name(id='False',
                                         ctx=Load())],
                            nl=True)],
                orelse=[Print(dest=None,
                              values=[Name(id='None',
                                           ctx=Load())],
                              nl=True)]),
             Print(dest=None,
                   values=[Str(s='\nin/not in with a list')],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='None',
                                             ctx=Load()),
                                   ops=[In()],
                                   comparators=[List(elts=[Num(n=1),
                                                           Num(n=2),
                                                           Num(n=3)],
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='None',
                                             ctx=Load()),
                                   ops=[In()],
                                   comparators=[List(elts=[Num(n=1),
                                                           Num(n=2),
                                                           Num(n=3),
                                                           Name(id='None',
                                                                ctx=Load())],
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='None',
                                             ctx=Load()),
                                   ops=[NotIn()],
                                   comparators=[List(elts=[Num(n=1),
                                                           Num(n=2),
                                                           Num(n=3)],
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='None',
                                             ctx=Load()),
                                   ops=[NotIn()],
                                   comparators=[List(elts=[Num(n=1),
                                                           Num(n=2),
                                                           Num(n=3),
                                                           Name(id='None',
                                                                ctx=Load())],
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\nin/not in with a dict')],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='None',
                                             ctx=Load()),
                                   ops=[In()],
                                   comparators=[Dict(keys=[Num(n=1),
                                                           Num(n=3)],
                                                     values=[Num(n=2),
                                                             Num(n=4)])])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='None',
                                             ctx=Load()),
                                   ops=[In()],
                                   comparators=[Dict(keys=[Num(n=1),
                                                           Num(n=3),
                                                           Name(id='None',
                                                                ctx=Load())],
                                                     values=[Num(n=2),
                                                             Num(n=4),
                                                             Num(n=5)])])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='None',
                                             ctx=Load()),
                                   ops=[NotIn()],
                                   comparators=[Dict(keys=[Num(n=1),
                                                           Num(n=3)],
                                                     values=[Num(n=2),
                                                             Num(n=4)])])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='None',
                                             ctx=Load()),
                                   ops=[NotIn()],
                                   comparators=[Dict(keys=[Num(n=1),
                                                           Num(n=3),
                                                           Name(id='None',
                                                                ctx=Load())],
                                                     values=[Num(n=2),
                                                             Num(n=4),
                                                             Num(n=5)])])],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\nis/is not')],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='None',
                                             ctx=Load()),
                                   ops=[Is()],
                                   comparators=[Name(id='None',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='None',
                                             ctx=Load()),
                                   ops=[IsNot()],
                                   comparators=[Name(id='None',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='None',
                                             ctx=Load()),
                                   ops=[Is()],
                                   comparators=[Num(n=3)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Num(n=3),
                                   ops=[Is()],
                                   comparators=[Name(id='None',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='None',
                                             ctx=Load()),
                                   ops=[IsNot()],
                                   comparators=[Num(n=3)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Num(n=3),
                                   ops=[IsNot()],
                                   comparators=[Name(id='None',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\nboolean comparisons with int')],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Num(n=3),
                                   ops=[Eq()],
                                   comparators=[Name(id='None',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='None',
                                             ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Num(n=3)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Num(n=3),
                                   ops=[NotEq()],
                                   comparators=[Name(id='None',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='None',
                                             ctx=Load()),
                                   ops=[NotEq()],
                                   comparators=[Num(n=3)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Num(n=3),
                                   ops=[Gt()],
                                   comparators=[Name(id='None',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Num(n=3),
                                   ops=[GtE()],
                                   comparators=[Name(id='None',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Num(n=3),
                                   ops=[Lt()],
                                   comparators=[Name(id='None',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Num(n=3),
                                   ops=[LtE()],
                                   comparators=[Name(id='None',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='None',
                                             ctx=Load()),
                                   ops=[Gt()],
                                   comparators=[Num(n=3)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='None',
                                             ctx=Load()),
                                   ops=[GtE()],
                                   comparators=[Num(n=3)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='None',
                                             ctx=Load()),
                                   ops=[Lt()],
                                   comparators=[Num(n=3)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='None',
                                             ctx=Load()),
                                   ops=[LtE()],
                                   comparators=[Num(n=3)])],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\nboolean comparisons with long')],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Num(n=3),
                                   ops=[Eq()],
                                   comparators=[Name(id='None',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='None',
                                             ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Num(n=3)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Num(n=3),
                                   ops=[NotEq()],
                                   comparators=[Name(id='None',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='None',
                                             ctx=Load()),
                                   ops=[NotEq()],
                                   comparators=[Num(n=3)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Num(n=3),
                                   ops=[Gt()],
                                   comparators=[Name(id='None',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Num(n=3),
                                   ops=[GtE()],
                                   comparators=[Name(id='None',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Num(n=3),
                                   ops=[Lt()],
                                   comparators=[Name(id='None',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Num(n=3),
                                   ops=[LtE()],
                                   comparators=[Name(id='None',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='None',
                                             ctx=Load()),
                                   ops=[Gt()],
                                   comparators=[Num(n=3)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='None',
                                             ctx=Load()),
                                   ops=[GtE()],
                                   comparators=[Num(n=3)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='None',
                                             ctx=Load()),
                                   ops=[Lt()],
                                   comparators=[Num(n=3)])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='None',
                                             ctx=Load()),
                                   ops=[LtE()],
                                   comparators=[Num(n=3)])],
                   nl=True),
             Print(dest=None,
                   values=[Str(s='\nboolean comparisons with None')],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='None',
                                             ctx=Load()),
                                   ops=[Eq()],
                                   comparators=[Name(id='None',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='None',
                                             ctx=Load()),
                                   ops=[NotEq()],
                                   comparators=[Name(id='None',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='None',
                                             ctx=Load()),
                                   ops=[Gt()],
                                   comparators=[Name(id='None',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='None',
                                             ctx=Load()),
                                   ops=[GtE()],
                                   comparators=[Name(id='None',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='None',
                                             ctx=Load()),
                                   ops=[Lt()],
                                   comparators=[Name(id='None',
                                                     ctx=Load())])],
                   nl=True),
             Print(dest=None,
                   values=[Compare(left=Name(id='None',
                                             ctx=Load()),
                                   ops=[LtE()],
                                   comparators=[Name(id='None',
                                                     ctx=Load())])],
                   nl=True)])
