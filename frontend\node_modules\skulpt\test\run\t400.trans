Module(body=[Import(names=[alias(name='random',
                                 asname=None)]),
             Expr(value=Call(func=Attribute(value=Name(id='random',
                                                       ctx=Load()),
                                            attr='seed',
                                            ctx=Load()),
                             args=[Num(n=0)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Assign(targets=[Name(id='lst',
                                  ctx=Store())],
                    value=List(elts=[Num(n=0),
                                     Num(n=0),
                                     Num(n=0),
                                     Num(n=0),
                                     Num(n=0)],
                               ctx=Load())),
             For(target=Name(id='i',
                             ctx=Store()),
                 iter=Call(func=Name(id='range',
                                     ctx=Load()),
                           args=[Num(n=1000)],
                           keywords=[],
                           starargs=None,
                           kwargs=None),
                 body=[AugAssign(target=Subscript(value=Name(id='lst',
                                                             ctx=Load()),
                                                  slice=Index(value=Call(func=Attribute(value=Name(id='random',
                                                                                                   ctx=Load()),
                                                                                        attr='randint',
                                                                                        ctx=Load()),
                                                                         args=[Num(n=0),
                                                                               Num(n=4)],
                                                                         keywords=[],
                                                                         starargs=None,
                                                                         kwargs=None)),
                                                  ctx=Store()),
                                 op=Add(),
                                 value=Num(n=1))],
                 orelse=[]),
             Print(dest=None,
                   values=[Name(id='lst',
                                ctx=Load())],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='sum',
                                          ctx=Load()),
                                args=[Name(id='lst',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True)])
