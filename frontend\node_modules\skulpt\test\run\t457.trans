Module(body=[Import(names=[alias(name='math',
                                 asname=None)]),
             Import(names=[alias(name='random',
                                 asname=None)]),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='log',
                                               ctx=Load()),
                                args=[Num(n=32)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='log',
                                               ctx=Load()),
                                args=[Num(n=32),
                                      Num(n=10)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='log',
                                               ctx=Load()),
                                args=[Num(n=32),
                                      Num(n=2)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='log10',
                                               ctx=Load()),
                                args=[Num(n=64)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='trunc',
                                               ctx=Load()),
                                args=[Num(n=3.233)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='trunc',
                                               ctx=Load()),
                                args=[Num(n=3.78)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='trunc',
                                               ctx=Load()),
                                args=[Num(n=-232.83)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='math',
                                                          ctx=Load()),
                                               attr='trunc',
                                               ctx=Load()),
                                args=[Num(n=-232.1)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Assign(targets=[Name(id='l',
                                  ctx=Store())],
                    value=List(elts=[Num(n=1),
                                     Num(n=2),
                                     Num(n=3)],
                               ctx=Load())),
             Expr(value=Call(func=Attribute(value=Name(id='random',
                                                       ctx=Load()),
                                            attr='seed',
                                            ctx=Load()),
                             args=[Num(n=1234)],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='choice',
                                               ctx=Load()),
                                args=[Name(id='l',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='choice',
                                               ctx=Load()),
                                args=[Name(id='l',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='choice',
                                               ctx=Load()),
                                args=[Name(id='l',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='choice',
                                               ctx=Load()),
                                args=[Name(id='l',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='choice',
                                               ctx=Load()),
                                args=[Name(id='l',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='choice',
                                               ctx=Load()),
                                args=[Name(id='l',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='choice',
                                               ctx=Load()),
                                args=[Name(id='l',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Assign(targets=[Name(id='t',
                                  ctx=Store())],
                    value=Tuple(elts=[Num(n=8),
                                      Num(n=9),
                                      Num(n=10)],
                                ctx=Load())),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='choice',
                                               ctx=Load()),
                                args=[Name(id='t',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='choice',
                                               ctx=Load()),
                                args=[Name(id='t',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='choice',
                                               ctx=Load()),
                                args=[Name(id='t',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='choice',
                                               ctx=Load()),
                                args=[Name(id='t',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='choice',
                                               ctx=Load()),
                                args=[Name(id='t',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='choice',
                                               ctx=Load()),
                                args=[Name(id='t',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='choice',
                                               ctx=Load()),
                                args=[Name(id='t',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Attribute(value=Name(id='random',
                                                          ctx=Load()),
                                               attr='choice',
                                               ctx=Load()),
                                args=[Name(id='t',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True)])
