Module(body=[FunctionDef(name='quit',
                         args=arguments(args=[],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[]),
                         body=[Print(dest=None,
                                     values=[Str(s='REDEFINED QUIT')],
                                     nl=True)],
                         decorator_list=[]),
             Print(dest=None,
                   values=[Num(n=1)],
                   nl=True),
             Expr(value=Call(func=Name(id='quit',
                                       ctx=Load()),
                             args=[],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Num(n=2)],
                   nl=True),
             FunctionDef(name='exit',
                         args=arguments(args=[],
                                        vararg=None,
                                        kwarg=None,
                                        defaults=[]),
                         body=[Print(dest=None,
                                     values=[Str(s='REDEFINED EXIT')],
                                     nl=True)],
                         decorator_list=[]),
             Print(dest=None,
                   values=[Num(n=3)],
                   nl=True),
             Expr(value=Call(func=Name(id='exit',
                                       ctx=Load()),
                             args=[],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Num(n=4)],
                   nl=True)])
