Module(body=[ClassDef(name='A',
                      bases=[],
                      body=[Pass()],
                      decorator_list=[]),
             Print(dest=None,
                   values=[Call(func=Name(id='isinstance',
                                          ctx=Load()),
                                args=[Num(n=4),
                                      Name(id='int',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='isinstance',
                                          ctx=Load()),
                                args=[Num(n=4),
                                      Tuple(elts=[Name(id='float',
                                                       ctx=Load()),
                                                  Name(id='int',
                                                       ctx=Load())],
                                            ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='isinstance',
                                          ctx=Load()),
                                args=[Call(func=Name(id='A',
                                                     ctx=Load()),
                                           args=[],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None),
                                      Name(id='A',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='isinstance',
                                          ctx=Load()),
                                args=[Num(n=4),
                                      Tuple(elts=[Name(id='int',
                                                       ctx=Load()),
                                                  Name(id='float',
                                                       ctx=Load()),
                                                  Num(n=5)],
                                            ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='isinstance',
                                          ctx=Load()),
                                args=[Num(n=4),
                                      Tuple(elts=[Name(id='int',
                                                       ctx=Load()),
                                                  Name(id='float',
                                                       ctx=Load()),
                                                  Call(func=Name(id='A',
                                                                 ctx=Load()),
                                                       args=[],
                                                       keywords=[],
                                                       starargs=None,
                                                       kwargs=None)],
                                            ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='isinstance',
                                          ctx=Load()),
                                args=[Name(id='A',
                                           ctx=Load()),
                                      Name(id='A',
                                           ctx=Load())],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='isinstance',
                                          ctx=Load()),
                                args=[Num(n=4),
                                      Call(func=Name(id='type',
                                                     ctx=Load()),
                                           args=[Num(n=4)],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='isinstance',
                                          ctx=Load()),
                                args=[Name(id='True',
                                           ctx=Load()),
                                      Call(func=Name(id='type',
                                                     ctx=Load()),
                                           args=[Name(id='False',
                                                      ctx=Load())],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='isinstance',
                                          ctx=Load()),
                                args=[Num(n=5.4),
                                      Call(func=Name(id='type',
                                                     ctx=Load()),
                                           args=[Num(n=1.2)],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='isinstance',
                                          ctx=Load()),
                                args=[Num(n=3),
                                      Call(func=Name(id='type',
                                                     ctx=Load()),
                                           args=[Num(n=8)],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='isinstance',
                                          ctx=Load()),
                                args=[List(elts=[Num(n=1),
                                                 Num(n=2),
                                                 Num(n=3)],
                                           ctx=Load()),
                                      Call(func=Name(id='type',
                                                     ctx=Load()),
                                           args=[List(elts=[Num(n=5),
                                                            Num(n=6)],
                                                      ctx=Load())],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='isinstance',
                                          ctx=Load()),
                                args=[Dict(keys=[Num(n=1)],
                                           values=[Num(n=2)]),
                                      Call(func=Name(id='type',
                                                     ctx=Load()),
                                           args=[Dict(keys=[Num(n=3)],
                                                      values=[Num(n=4)])],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='isinstance',
                                          ctx=Load()),
                                args=[Tuple(elts=[Num(n=1),
                                                  Num(n=2)],
                                            ctx=Load()),
                                      Call(func=Name(id='type',
                                                     ctx=Load()),
                                           args=[Tuple(elts=[Num(n=3),
                                                             Num(n=4)],
                                                       ctx=Load())],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='isinstance',
                                          ctx=Load()),
                                args=[Call(func=Name(id='set',
                                                     ctx=Load()),
                                           args=[List(elts=[Num(n=1),
                                                            Num(n=2)],
                                                      ctx=Load())],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None),
                                      Call(func=Name(id='type',
                                                     ctx=Load()),
                                           args=[Call(func=Name(id='set',
                                                                ctx=Load()),
                                                      args=[List(elts=[Num(n=3),
                                                                       Num(n=4)],
                                                                 ctx=Load())],
                                                      keywords=[],
                                                      starargs=None,
                                                      kwargs=None)],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='isinstance',
                                          ctx=Load()),
                                args=[Call(func=Name(id='A',
                                                     ctx=Load()),
                                           args=[],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None),
                                      Call(func=Name(id='type',
                                                     ctx=Load()),
                                           args=[Call(func=Name(id='A',
                                                                ctx=Load()),
                                                      args=[],
                                                      keywords=[],
                                                      starargs=None,
                                                      kwargs=None)],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True),
             Print(dest=None,
                   values=[Call(func=Name(id='isinstance',
                                          ctx=Load()),
                                args=[Name(id='None',
                                           ctx=Load()),
                                      Call(func=Name(id='type',
                                                     ctx=Load()),
                                           args=[Name(id='None',
                                                      ctx=Load())],
                                           keywords=[],
                                           starargs=None,
                                           kwargs=None)],
                                keywords=[],
                                starargs=None,
                                kwargs=None)],
                   nl=True)])
