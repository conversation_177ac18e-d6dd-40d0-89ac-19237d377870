Module(body=[Assign(targets=[Name(id='v',
                                  ctx=Store())],
                    value=List(elts=[Num(n=3),
                                     Num(n=2),
                                     Num(n=1)],
                               ctx=Load())),
             Expr(value=Call(func=Attribute(value=Name(id='v',
                                                       ctx=Load()),
                                            attr='sort',
                                            ctx=Load()),
                             args=[],
                             keywords=[],
                             starargs=None,
                             kwargs=None)),
             Print(dest=None,
                   values=[Subscript(value=Name(id='v',
                                                ctx=Load()),
                                     slice=Index(value=Num(n=0)),
                                     ctx=Load())],
                   nl=True)])
